import os
import ast
import re
import traceback
import plugins_manager
from typing import Set, Dict, List, Any, Optional

from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from ui.settings import Header, Divider, Input, Switch, Text
from markdown_utils import parse_markdown
from ui.bulletin import BulletinHelper
from android_utils import log as _logcat
from java.util import ArrayList, Locale
from org.telegram.tgnet import TLRPC

__id__ = "command_list_by_mi<PERSON><PERSON><PERSON><PERSON><PERSON>"
__name__ = "Command List"
__version__ = "2.0"
__description__ = "Shows an up-to-date list of all commands from all installed plugins."
__author__ = "@mihailk<PERSON>vski & @mishabotov"
__min_version__ = "11.9.0"
__icon__ = "ThePixelMan/5"

AUTOUPDATE_CHANNEL_ID = 2349438816
AUTOUPDATE_CHANNEL_USERNAME = "mishabotov"
AUTOUPDATE_MESSAGE_ID = 65

zwylib: Optional[Any] = None
is_debug_mode: bool = False
DEFAULT_CMD = ".cmds"

class LocalizationManager:
    """Manages multi-language strings for the plugin."""
    strings = {
        "ru": {
            "zwylib_not_found": "Для работы плагина требуется ZwyLib!",
            "settings_header": "Настройки Command List",
            "cmd_alias_title": "Команда для вызова",
            "cmd_alias_subtitle": "Команда, по которой будет выводиться список всех команд.",
            "blockquote_title": "Использовать цитату",
            "blockquote_subtitle": "Отображать список команд в виде сворачиваемой цитаты.",
            "debug_header": "Отладка",
            "debug_mode_title": "Режим отладки",
            "debug_mode_subtitle": "Выводит подробную информацию в logcat.",
            "info_divider": "Этот плагин анализирует код других плагинов для поиска команд.",
            "cmd_list_header": "📜 **Список обнаруженных команд ({plugin_count} плагинов, {command_count} команд):**\n\n",
            "plugin_status_on": "Включен",
            "plugin_status_off": "Выключен",
            "cmd_note": "*⚠️ Примечание: Некоторые команды могут быть определены динамически и не показаны здесь.*",
            "error_collecting_cmds": "Произошла ошибка при сборе команд:\n\n",
            "no_cmds_found": "Не удалось найти команды в других плагинах.",
            "dir_error": "Не удалось определить директорию плагинов."
        },
        "en": {
            "zwylib_not_found": "ZwyLib is required for this plugin to work!",
            "settings_header": "Command List Settings",
            "cmd_alias_title": "Trigger Command",
            "cmd_alias_subtitle": "The command to show the list of all commands.",
            "blockquote_title": "Use blockquote",
            "blockquote_subtitle": "Display the command list as a collapsible blockquote.",
            "debug_header": "Debugging",
            "debug_mode_title": "Debug Mode",
            "debug_mode_subtitle": "Prints detailed information to logcat.",
            "info_divider": "This plugin analyzes the code of other plugins to find commands.",
            "cmd_list_header": "📜 **Discovered Commands ({plugin_count} plugins, {command_count} commands):**\n\n",
            "plugin_status_on": "Enabled",
            "plugin_status_off": "Disabled",
            "cmd_note": "*⚠️ Note: Some commands might be defined dynamically and not shown here.*",
            "error_collecting_cmds": "An error occurred while collecting commands:\n\n",
            "no_cmds_found": "Could not find commands in other plugins.",
            "dir_error": "Could not determine the plugins directory."
        },
        "uk": {
            "zwylib_not_found": "Для роботи плагіна потрібен ZwyLib!",
            "settings_header": "Налаштування Command List",
            "cmd_alias_title": "Команда для виклику",
            "cmd_alias_subtitle": "Команда, за якою буде виводитись список усіх команд.",
            "blockquote_title": "Використовувати цитату",
            "blockquote_subtitle": "Відображати список команд у вигляді цитати, що згортається.",
            "debug_header": "Налагодження",
            "debug_mode_title": "Режим налагодження",
            "debug_mode_subtitle": "Виводить детальну інформацію в logcat.",
            "info_divider": "Цей плагін аналізує код інших плагінів для пошуку команд.",
            "cmd_list_header": "📜 **Список знайдених команд ({plugin_count} плагінів, {command_count} команд):**\n\n",
            "plugin_status_on": "Увімкнено",
            "plugin_status_off": "Вимкнено",
            "cmd_note": "*⚠️ Примітка: Деякі команди можуть бути визначені динамічно і не показані тут.*",
            "error_collecting_cmds": "Сталася помилка під час збору команд:\n\n",
            "no_cmds_found": "Не вдалося знайти команди в інших плагінах.",
            "dir_error": "Не вдалося визначити директорію плагінів."
        }
    }

    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self.strings else "en"

    def get_string(self, key: str) -> str:
        return self.strings[self.language].get(key, self.strings["en"].get(key, key))

locali = LocalizationManager()

def _log(message: str):
    """Logs a message if debug mode is enabled."""
    if is_debug_mode:
        _logcat(f"[{__name__}] {message}")

def import_zwylib(show_bulletin: bool = True):
    """Imports zwylib and handles import errors."""
    global zwylib
    try:
        import zwylib
        _log("ZwyLib imported successfully.")
    except ImportError:
        if show_bulletin:
            BulletinHelper.show_error(locali.get_string("zwylib_not_found"))
        _log("ZwyLib not found.")

def is_zwylib_present() -> bool:
    return zwylib is not None

class CommandVisitor(ast.NodeVisitor):
    """
    Analyzes Python's Abstract Syntax Tree (AST) to find potential commands.
    It looks for string literals starting with '.' in comparisons, `startswith`
    calls, and inside command-related Enums.
    """
    VALID_COMMAND_RE = re.compile(r"^\.[\w-]+$")

    def __init__(self):
        self.commands: Set[str] = set()
        self.variables: Dict[str, Any] = {}

    def visit_Assign(self, node: ast.Assign):
        if len(node.targets) == 1 and isinstance(node.targets[0], ast.Name):
            try:
                self.variables[node.targets[0].id] = ast.literal_eval(node.value)
            except (ValueError, TypeError, AttributeError, SyntaxError):
                pass  
        self.generic_visit(node)

    def _add_command(self, value: Any):
        if isinstance(value, str):
            command = value.strip().split(" ")[0]
            if self.VALID_COMMAND_RE.match(command):
                self.commands.add(command)

    def visit_Compare(self, node: ast.Compare):
        op = node.ops[0]
        if isinstance(op, (ast.Eq, ast.In)):
            for item in [node.left] + node.comparators:
                if isinstance(item, ast.Constant):
                    self._add_command(item.value)
                elif isinstance(item, ast.Name) and item.id in self.variables:
                    self._add_command(self.variables[item.id])
                elif isinstance(item, (ast.List, ast.Tuple)):
                    for element in item.elts:
                        if isinstance(element, ast.Constant):
                            self._add_command(element.value)
        self.generic_visit(node)

    def visit_Call(self, node: ast.Call):
        if isinstance(node.func, ast.Attribute) and node.func.attr == 'startswith':
            if node.args:
                arg = node.args[0]
                if isinstance(arg, ast.Constant):
                    self._add_command(arg.value)
                elif isinstance(arg, ast.Name) and arg.id in self.variables:
                    self._add_command(self.variables[arg.id])
        self.generic_visit(node)

    def visit_ClassDef(self, node: ast.ClassDef):
        """
        NEW: Visits class definitions to find commands defined in Enums,
        which is a common pattern in more complex plugins like adminTools.
        """
        is_enum = False
        for base in node.bases:
            if isinstance(base, ast.Name) and 'Enum' in base.id:
                is_enum = True
                break
        
        if is_enum:
            _log(f"Found a potential command Enum: {node.name}")
            for item in node.body:
                if isinstance(item, ast.Assign) and len(item.targets) == 1:
                    if isinstance(item.value, ast.Constant) and isinstance(item.value.value, str):
                        command_stem = item.value.value
                        full_command = f".{command_stem}"
                        self.commands.add(full_command)
                        _log(f"Found Enum command: {full_command}")

        self.generic_visit(node)


class CommandHarvesterPlugin(BasePlugin):
    """
    Main plugin class to harvest and display commands from other plugins.
    """
    def on_plugin_load(self):
        """Called when the plugin is loaded."""
        global is_debug_mode
        self.add_on_send_message_hook()
        is_debug_mode = self.get_setting("debug_mode", False)
        
        import_zwylib()
        if is_zwylib_present():
            zwylib.add_autoupdater_task(__id__, AUTOUPDATE_CHANNEL_ID, AUTOUPDATE_CHANNEL_USERNAME, AUTOUPDATE_MESSAGE_ID)
        _log("Plugin loaded.")

    def on_plugin_unload(self):
        """Called when the plugin is unloaded."""
        if is_zwylib_present():
            zwylib.remove_autoupdater_task(__id__)
        _log("Plugin unloaded.")

    def create_settings(self) -> List[Any]:
        """Creates the settings UI for the plugin."""
        return [
            Header(text=locali.get_string("settings_header")),
            Input(
                key="command_alias",
                text=locali.get_string("cmd_alias_title"),
                default=DEFAULT_CMD,
                subtext=locali.get_string("cmd_alias_subtitle"),
            ),
            Switch(
                key="use_blockquote",
                text=locali.get_string("blockquote_title"),
                subtext=locali.get_string("blockquote_subtitle"),
                default=True
            ),
            Divider(),
            Header(text=locali.get_string("debug_header")),
            Switch(
                key="debug_mode",
                text=locali.get_string("debug_mode_title"),
                subtext=locali.get_string("debug_mode_subtitle"),
                default=False,
                on_change=self._toggle_debug_mode
            ),
            Divider(text=locali.get_string("info_divider")),
        ]

    def _toggle_debug_mode(self, new_value: bool):
        """Callback to update the debug mode status."""
        global is_debug_mode
        is_debug_mode = new_value
        _log(f"Debug mode set to: {is_debug_mode}")

    def _get_plugin_id_from_file(self, file_path: str) -> Optional[str]:
        """Extracts the __id__ from a plugin file without executing it."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            tree = ast.parse(content, filename=file_path)
            for node in ast.walk(tree):
                if (isinstance(node, ast.Assign) and len(node.targets) == 1 and
                        isinstance(node.targets[0], ast.Name) and node.targets[0].id == '__id__'):
                    return ast.literal_eval(node.value)
        except Exception as e:
            _log(f"Failed to parse ID from {os.path.basename(file_path)}: {e}")
        return None

    def _get_all_commands(self) -> Dict[str, Dict[str, Any]]:
        """Scans all plugin files and extracts commands."""
        _log("Starting command harvesting process.")
        plugin_data: Dict[str, Dict[str, Any]] = {}
        loaded_plugins_map = {p.id: p for p in plugins_manager.PluginsManager._plugins.values()}
        
        pm_instance = plugins_manager.PluginsManager
        if not hasattr(pm_instance, '_plugins_dir') or not pm_instance._plugins_dir:
            raise ValueError(locali.get_string("dir_error"))
        plugins_dir = pm_instance._plugins_dir
        _log(f"Scanning plugins directory: {plugins_dir}")

        for filename in os.listdir(plugins_dir):
            if not filename.endswith(('.py', '.plugin')):
                continue
            
            file_path = os.path.join(plugins_dir, filename)
            _log(f"Analyzing file: {filename}")

            try:
                plugin_id_from_file = self._get_plugin_id_from_file(file_path)
                if not plugin_id_from_file or plugin_id_from_file not in loaded_plugins_map:
                    _log(f"Skipping {filename}: Not a loaded plugin or no ID found.")
                    continue
                if plugin_id_from_file == __id__:
                    _log(f"Skipping self: {filename}")
                    continue

                plugin_obj = loaded_plugins_map[plugin_id_from_file]
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                tree = ast.parse(content, filename=file_path)
                visitor = CommandVisitor()
                visitor.visit(tree)
                
                if visitor.commands:
                    _log(f"Found {len(visitor.commands)} commands in {plugin_obj.name}")
                    plugin_data[plugin_obj.name] = {
                        "commands": sorted(list(visitor.commands)),
                        "enabled": plugin_obj.enabled
                    }
            except Exception as e:
                _log(f"Error processing {filename}: {e}")
                continue
        
        _log(f"Harvesting complete. Found commands in {len(plugin_data)} plugins.")
        return plugin_data

    def _format_command_list(self, all_commands: Dict[str, Dict[str, Any]]) -> str:
        """Formats the collected commands into a readable string."""
        if not all_commands:
            return locali.get_string("no_cmds_found")

        total_plugins = len(all_commands)
        total_commands = sum(len(data["commands"]) for data in all_commands.values())

        message = locali.get_string("cmd_list_header").format(
            plugin_count=total_plugins, command_count=total_commands
        )
        sorted_plugins = sorted(all_commands.items(), key=lambda item: item[0].lower())

        for plugin_name, data in sorted_plugins:
            commands = data["commands"]
            is_enabled = data["enabled"]
            status_icon = "✅" if is_enabled else "❌"
            status_text = locali.get_string("plugin_status_on" if is_enabled else "plugin_status_off")
            status_str = f"||[{status_icon} {status_text}]||"
            
            message += f"**🧩 {plugin_name}** {status_str}\n"

            commands_str = '` `'.join(commands)
            message += f"`{commands_str}`\n\n"
        
        message += locali.get_string("cmd_note")
        return message

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Hook to intercept outgoing messages and handle the command."""
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()
        
        trigger_command = self.get_setting("command_alias", DEFAULT_CMD)
        message_text = params.message.strip()
        
        if message_text == trigger_command:
            try:
                _log("Harvesting commands (caching is disabled).")
                all_commands = self._get_all_commands()

                if not hasattr(params, "entities") or params.entities is None:
                    params.entities = ArrayList()
                else:
                    params.entities.clear()
                
                response_text = self._format_command_list(all_commands)

                if self.get_setting("use_blockquote", True):
                    parsed_for_quote = parse_markdown(response_text)
                    params.message = parsed_for_quote.text

                    entity = TLRPC.TL_messageEntityBlockquote()
                    entity.collapsed = True
                    entity.offset = 0
                    entity.length = len(params.message.encode('utf_16_le')) // 2
                    params.entities.add(entity)
                else:
                    parsed = parse_markdown(response_text)
                    params.message = parsed.text
                    for entity in parsed.entities:
                        params.entities.add(entity.to_tlrpc_object())

                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

            except Exception:
                _log(f"FATAL ERROR: {traceback.format_exc()}")
                error_text = locali.get_string("error_collecting_cmds") + f"```\n{traceback.format_exc()}\n```"
                parsed_error = parse_markdown(error_text)
                params.message = parsed_error.text
                if hasattr(params, "entities") and params.entities is not None:
                    params.entities.clear()
                for entity in parsed_error.entities:
                    params.entities.add(entity.to_tlrpc_object())
                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

        return HookResult()