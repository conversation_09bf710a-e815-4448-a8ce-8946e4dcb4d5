#Первая версия данного плагина. За баги и тд не ручаюсь. Баги?  пишите в лс - @SaturnFake
# лаги? писать туда же
# не работает? включите Overlay
#Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG
#Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG
#Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DDevPluginsEUG
#Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG #Разраб @SaturnFake 
#канал - https://t.me/DevPluginsEUG
# ПЛАГИАТ , РЕДАКТИРОВАНИЕ И ПОСЛЕДУЮЩЕЕ РАСПРОСТРАНЕНИЕ - СТРОГО ЗАПРЕЩЕНО !
# ИСКЛЮЧЕНИЕ - РАЗРЕШЕНИЕ АВТОРА


#imports 

from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.bulletin import BulletinHelper
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
from hook_utils import find_class
from android.content import Context, Intent
from android.net import Uri
from java import dynamic_proxy
import uuid
import time
import random

__id__ = "snake_game"
__name__ = "Snake Game apple 🌟"
__description__ = "a snake running after an apple"
__author__ = "@SaturnFake"
__version__ = "1.0.0"
__min_version__ = "11.12.0"
__icon__ = "Plugins_Test/1"
# Настройки | Настоятельно рекомендую не трогать!!
DEFAULT_SPEED = 4
MIN_SPEED = 1
MAX_SPEED = 10
DEFAULT_FOOD_COUNT = 1
MAX_FOOD_COUNT = 5
#Html | Настоятельно рекомендую не трогать!!
html_content = """<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Snake 🌟</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #000;
            color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
            user-select: none;
            background-image: url('https://i.imgur.com/YOUR_IMAGE_HERE.png');
            background-size: cover;
            background-position: center;
        }
        #game-board {
            background-color: rgba(33, 33, 33, 0.85);
            border: 6px solid #777; /* Thicker and slightly lighter border */
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.8); /* Stronger shadow */
            border-radius: 16px; /* More rounded corners */
        }
        .snake {
            background-color: #8BC34A;
            border: 1px solid #689F38;
            position: absolute;
            border-radius: 50%;
            transition: transform 0.12s linear, background-color 0.2s ease;
        }
        .snake:hover { background-color: #9CCC65; }
        .food {
            background-color: #F44336;
            border-radius: 50%;
            position: absolute;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
            animation: pulse 1.5s infinite alternate;
        }
        @keyframes pulse { from { transform: scale(0.9); } to { transform: scale(1.1); } }
        .snake-path {
            position: absolute;
            width: 6px;
            height: 6px;
            background-color: rgba(139, 195, 74, 0.5);
            border-radius: 50%;
            pointer-events: none;
        }
        #score-container {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 22px;
            color: #eee;
            z-index: 10;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
        }
        #game-over-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            z-index: 11;
            color: #fff;
            text-align: center;
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        #game-over-screen h2 {
            font-size: 3.5em;
            margin-bottom: 0.6em;
            text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9);
        }
        #game-over-screen p { font-size: 1.3em; margin-bottom: 0.6em; }
        #game-over-screen #final-score-container {margin-bottom: 0.6em;}
        #game-over-screen button {
            background-color: #7E57C2;
            border: none;
            color: white;
            padding: 14px 28px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 1.4em;
            margin-top: 12px;
            cursor: pointer;
            border-radius: 8px;
            transition: background-color 0.2s ease;
        }
        #game-over-screen button:hover { background-color: #673AB7; }
        #touch-controls {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 10;
        }
        .touch-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 26px;
            cursor: pointer;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
            transition: background-color 0.2s ease;
        }
        .touch-button:hover { background-color: rgba(255, 255, 255, 0.3); }
        #settings-container {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            align-items: center;
            color: #eee;
            z-index: 10;
        }
        #speed-label { margin-right: 5px; font-size: 18px; }
        #speed-value { font-size: 18px; font-weight: bold; margin-right: 10px; }
        #speed-slider { width: 100px; }
    </style>
</head>
<body>
    <div id="score-container">Счет: <span id="score">0</span></div>
    <div id="settings-container">
        <span id="speed-label">Скорость:</span>
        <span id="speed-value">4</span>
        <input type="range" id="speed-slider" min="1" max="10" value="4">
    </div>
    <div id="game-board"></div>
    <div id="game-over-screen">
        <h2>Игра окончена!</h2>
        <div id="final-score-container">
          <p>Ваш счет: <span id="final-score">0</span></p>
          <p>Лучший счет: <span id="best-score">0</span></p>
        </div>
        <button id="restart-button">Играть снова</button>
    </div>
    <div id="touch-controls">
        <div class="touch-button" id="up-button">↑</div>
        <div class="touch-button" id="down-button">↓</div>
        <div class="touch-button" id="left-button">←</div>
        <div class="touch-button" id="right-button">→</div>
    </div>
    <script>
        const BOARD_SIZE = 20;
        const CELL_SIZE = 20;
        let snake = [{ x: 10, y: 10 }];
        let food = [];
        let direction = 'right';
        let score = 0;
        let bestScore = 0;
        let gameInterval;
        let gameBoard;
        let gameOverScreen;
        let finalScoreDisplay;
        let scoreDisplay;
        let bestScoreDisplay;
        let speedSlider;
        let speedValueDisplay;
        let tilesPerSecond = 4;
        let pathHistory = [];
        const PATH_LENGTH = 15;
        let isGameOverFlag = false;
        let foodCount = 1;

        function initializeGame() {
            gameBoard = document.getElementById('game-board');
            gameOverScreen = document.getElementById('game-over-screen');
            finalScoreDisplay = document.getElementById('final-score');
            scoreDisplay = document.getElementById('score');
            bestScoreDisplay = document.getElementById('best-score');
            speedSlider = document.getElementById('speed-slider');
            speedValueDisplay = document.getElementById('speed-value');
            gameBoard.style.width = `${BOARD_SIZE * CELL_SIZE}px`;
            gameBoard.style.height = `${BOARD_SIZE * CELL_SIZE}px`;
            document.getElementById('restart-button').addEventListener('click', startGame);
            setupTouchControls();
            loadBestScore();
            setupSettings();
        }

        function setupSettings() {
            speedSlider.addEventListener('input', () => {
                tilesPerSecond = parseInt(speedSlider.value);
                speedValueDisplay.innerText = tilesPerSecond;
                localStorage.setItem('snakeSpeed', tilesPerSecond.toString());
                updateGameSpeed();
            });

            const storedSpeed = localStorage.getItem('snakeSpeed');
            if (storedSpeed) {
                tilesPerSecond = parseInt(storedSpeed);
                speedSlider.value = tilesPerSecond;
                speedValueDisplay.innerText = tilesPerSecond;
            }

            if (typeof FOOD_COUNT !== 'undefined') {
              foodCount = FOOD_COUNT;
            }
        }

        function loadBestScore() {
            const storedScore = localStorage.getItem('bestScore');
            if (storedScore) {
                bestScore = parseInt(storedScore);
                bestScoreDisplay.innerText = bestScore;
            }
        }

        function saveBestScore() {
            localStorage.setItem('bestScore', bestScore.toString());
        }

        function setupTouchControls() {
            document.getElementById('up-button').addEventListener('click', () => changeDirection('up'));
            document.getElementById('down-button').addEventListener('click', () => changeDirection('down'));
            document.getElementById('left-button').addEventListener('click', () => changeDirection('left'));
            document.getElementById('right-button').addEventListener('click', () => changeDirection('right'));
        }

        function startGame() {
            snake = [{ x: 10, y: 10 }];
            food = generateFood();
            direction = 'right';
            score = 0;
            scoreDisplay.innerText = '0';
            gameOverScreen.style.display = 'none';
            pathHistory = [];
            isGameOverFlag = false;
            updateGameSpeed();
        }

        function update() {
            if (isGameOverFlag) {
                return;
            }
            moveSnake();
            if (isGameOver()) {
                endGame();
                return;
            }
            if (eatFood()) {
               updateScore();
               snake.unshift({ ...snake[0] });
               if (food.length === 0) {
                  generateNewFood();
               }
            }
            updatePathHistory();
            draw();
        }

        function draw() {
            gameBoard.innerHTML = '';
            drawSnake();
            drawFood();
            drawPath();
        }

        function drawSnake() {
            const snakeLength = snake.length;
            for (let i = 0; i < snakeLength; i++) {
                const segment = snake[i];
                const snakeElement = document.createElement('div');
                snakeElement.style.width = `${CELL_SIZE}px`;
                snakeElement.style.height = `${CELL_SIZE}px`;
                snakeElement.style.left = `${segment.x * CELL_SIZE}px`;
                snakeElement.style.top = `${segment.y * CELL_SIZE}px`;
                snakeElement.classList.add('snake');
                gameBoard.appendChild(snakeElement);
            }
        }

       function drawFood() {
           food.forEach(singleFood => {
               const foodElement = document.createElement('div');
               foodElement.style.width = `${CELL_SIZE}px`;
               foodElement.style.height = `${CELL_SIZE}px`;
               foodElement.style.left = `${singleFood.x * CELL_SIZE}px`;
               foodElement.style.top = `${singleFood.y * CELL_SIZE}px`;
               foodElement.classList.add('food');
               gameBoard.appendChild(foodElement);
           });
       }


        function moveSnake() {
            const head = { ...snake[0] };
            switch (direction) {
                case 'up': head.y--; break;
                case 'down': head.y++; break;
                case 'left': head.x--; break;
                case 'right': head.x++; break;
            }
            snake.unshift(head);
            snake.pop();
        }

        function isGameOver() {
            const head = snake[0];
            if (head.x < 0 || head.x >= BOARD_SIZE || head.y < 0 || head.y >= BOARD_SIZE) {
                return true;
            }

            const snakeLength = snake.length;
            for (let i = 1; i < snakeLength; i++) {
                if (head.x === snake[i].x && head.y === snake[i].y) {
                    return true;
                }
            }

            return false;
        }

        function eatFood() {
            let ateFood = false;
            const head = snake[0];
            const foodLength = food.length;

            for (let i = 0; i < foodLength; i++) {
                if (head.x === food[i].x && head.y === food[i].y) {
                    food.splice(i, 1);
                    ateFood = true;
                    return ateFood;
                }
            }

            return ateFood;
        }

       function generateFood() {
           let newFoods = [];
           let attempts = 0;
           const maxAttempts = 200;

           while (newFoods.length < foodCount && attempts < maxAttempts) {
               let newFood = {
                   x: Math.floor(Math.random() * BOARD_SIZE),
                   y: Math.floor(Math.random() * BOARD_SIZE)
               };

               if (!snake.some(segment => segment.x === newFood.x && segment.y === newFood.y) &&
                   !food.some(existingFood => existingFood.x === newFood.x && existingFood.y === newFood.y) &&
                   newFood.x >= 0 && newFood.x < BOARD_SIZE && newFood.y >= 0 && newFood.y < BOARD_SIZE) {
                   newFoods.push(newFood);
               }

               attempts++;
           }

           if (attempts === maxAttempts) {
               console.warn("Не удалось создать достаточно еды из-за конфликтов размещения.");
           }

           return newFoods;
       }

        function generateNewFood() {
          if (food.length === 0) {
            food = generateFood();
          }
        }

        function updateScore() {
            score++;
            scoreDisplay.innerText = score;
        }

        function endGame() {
            clearInterval(gameInterval);
            finalScoreDisplay.innerText = score;
            gameOverScreen.style.display = 'flex';
            isGameOverFlag = true;
            if (score > bestScore) {
                bestScore = score;
                bestScoreDisplay.innerText = bestScore;
                saveBestScore();
            }
        }

        function changeDirection(newDirection) {
            if (newDirection === 'up' && direction === 'down') return;
            if (newDirection === 'down' && direction === 'up') return;
            if (newDirection === 'left' && direction === 'right') return;
            if (newDirection === 'right' && direction === 'left') return;
            direction = newDirection;
        }

        function updatePathHistory() {
            pathHistory.push({ x: snake[0].x, y: snake[0].y });
            if (pathHistory.length > PATH_LENGTH) {
                pathHistory.shift();
            }
        }

        function drawPath() {
            const pathLength = pathHistory.length;
            for (let i = 0; i < pathLength; i++) {
               const point = pathHistory[i];
                const pathElement = document.createElement('div');
                pathElement.classList.add('snake-path');
                pathElement.style.left = `${point.x * CELL_SIZE + CELL_SIZE / 2 - 3}px`;
                pathElement.style.top = `${point.y * CELL_SIZE + CELL_SIZE / 2 - 3}px`;
                gameBoard.appendChild(pathElement);
            }
        }

        function updateGameSpeed() {
            const millisecondsPerTile = 1000 / tilesPerSecond;
            clearInterval(gameInterval);
            gameInterval = setInterval(update, millisecondsPerTile);
        }

        document.addEventListener('keydown', event => {
            switch (event.key) {
                case 'ArrowUp': changeDirection('up'); break;
                case 'ArrowDown': changeDirection('down'); break;
                case 'ArrowLeft': changeDirection('left'); break;
                case 'ArrowRight': changeDirection('right'); break;
            }
        });

        document.addEventListener('DOMContentLoaded', () => {
            initializeGame();
            startGame();
        });
    </script>
</body>
</html>"""

class SnakeGamePluginV7(BasePlugin):
    WindowManager, LayoutParams, PixelFormat, WebView, Button, LinearLayout, ApplicationLoader, View, OnClickListener = (None,) * 9
    window_manager = None
    browser_view = None
    is_game_open = False
    best_score = 0

    def __init__(self):
        super().__init__()
        self.instance_id = str(uuid.uuid4())
        self.snake_speed = DEFAULT_SPEED
        self.food_count = DEFAULT_FOOD_COUNT

    def get_settings(self):
        return {
            "snake_speed": {
                "type": "number",
                "name": "Скорость змейки",
                "description": f"Скорость змейки (клеток в секунду). Диапазон: {MIN_SPEED}-{MAX_SPEED}",
                "value": self.snake_speed,
                "min": MIN_SPEED,
                "max": MAX_SPEED,
            },
            "food_count": {
                "type": "number",
                "name": "Количество еды",
                "description": "Количество еды на доске. Диапазон: 1-5",
                "value": self.food_count,
                "min": 1,
                "max": 5,
            }
        }

    def set_settings(self, settings):
        try:
            speed = int(settings.get("snake_speed", {}).get("value", DEFAULT_SPEED))
            self.snake_speed = max(MIN_SPEED, min(MAX_SPEED, speed))

            food_count = int(settings.get("food_count", {}).get("value", DEFAULT_FOOD_COUNT))
            self.food_count = max(1, min(5, food_count))

            if self.browser_view:
                run_on_ui_thread(self.refresh_game)

        except ValueError as e:
            BulletinHelper.show_error(f"Неверное значение настройки: {e}. Используются значения по умолчанию.")
            self.snake_speed = DEFAULT_SPEED
            self.food_count = DEFAULT_FOOD_COUNT

    def refresh_game(self):
        if self.browser_view and self.window_manager and self.is_game_open:
            try:
                app_context = self.ApplicationLoader.applicationContext
                if not app_context:
                    self.is_game_open = False
                    return

                web_view = self.browser_view.getChildAt(0)
                if web_view:
                    modified_html_content = html_content.replace(
                        "let tilesPerSecond = 4;",
                        f"let tilesPerSecond = {self.snake_speed};\n        const FOOD_COUNT = {self.food_count};"
                    ).replace(
                        "food = generateFood();",
                        "food = generateFood(FOOD_COUNT);"
                    ).replace(
                        """ function generateFood() {
           let numberOfFood = Math.floor(Math.random() * 5) + 1;""",
                        """ function generateFood(foodCount) {
           let numberOfFood = foodCount;"""
                    )

                    web_view.loadDataWithBaseURL("file:///android_asset/", modified_html_content, "text/html", "UTF-8", None)

                else:
                    pass
            except Exception as e:
                BulletinHelper.show_error(f"Ошибка при обновлении игры: {e}")

    def load_android_classes(self):
        if self.WindowManager:
            return
        try:
            self.WindowManager = find_class("android.view.WindowManager")
            self.LayoutParams = find_class("android.view.WindowManager$LayoutParams")
            self.PixelFormat = find_class("android.graphics.PixelFormat")
            self.WebView = find_class("android.webkit.WebView")
            self.Button = find_class("android.widget.Button")
            self.LinearLayout = find_class("android.widget.LinearLayout")
            self.ApplicationLoader = find_class("org.telegram.messenger.ApplicationLoader")
            self.View = find_class("android.view.View")
            self.OnClickListener = find_class("android.view.View$OnClickListener")
        except Exception as e:
            raise

    def on_plugin_load(self):
        try:
            self.load_android_classes()
            self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.DRAWER_MENU,
                text="Snake game 🌟",
                icon="msg_game",
                on_click=self.toggle_game
            ))
            self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text="Snake game 🌟",
                icon="msg_game",
                on_click=self.toggle_game
            ))
        except Exception as e:
            BulletinHelper.show_error(f"Ошибка при загрузке: {e}")

    def on_plugin_unload(self):
        try:
            run_on_ui_thread(self.close_game)
        except Exception as e:
            pass

    def toggle_game(self, context: dict):
        try:
            chat_id = context.get("dialog_id")
            if chat_id:
                pass

            drawer_layout = context.get("drawer_layout")
            if drawer_layout:
                drawer_layout.closeDrawer(False)

            if self.is_game_open:
                run_on_ui_thread(self.close_game)
            else:
                run_on_ui_thread(self.open_game)
        except Exception as e:
            BulletinHelper.show_error(f"Ошибка: {e}")

    def open_game(self):
        if self.is_game_open:
            return

        self.is_game_open = True
        try:
            app_context = self.ApplicationLoader.applicationContext
            if not app_context:
                self.is_game_open = False
                return

            self.window_manager = app_context.getSystemService(Context.WINDOW_SERVICE)
            display = self.window_manager.getDefaultDisplay()
            size = find_class("android.graphics.Point")()
            display.getSize(size)
            screen_width, screen_height = size.x, size.y

            layout = self.LinearLayout(app_context)
            layout.setOrientation(self.LinearLayout.VERTICAL)

            web_view = self.WebView(app_context)
            web_settings = web_view.getSettings()
            web_settings.setJavaScriptEnabled(True)
            web_settings.setDomStorageEnabled(True)
            web_settings.setCacheMode(web_settings.LOAD_NO_CACHE)

            modified_html_content = html_content.replace(
                "let tilesPerSecond = 4;",
                f"let tilesPerSecond = {self.snake_speed};\n        const FOOD_COUNT = {self.food_count};"
            ).replace(
                "food = generateFood();",
                "food = generateFood(FOOD_COUNT);"
            ).replace(
                """ function generateFood() {
           let numberOfFood = Math.floor(Math.random() * 5) + 1;""",
                """ function generateFood(foodCount) {
           let numberOfFood = foodCount;"""
            )

            web_view.loadDataWithBaseURL("file:///android_asset/", modified_html_content, "text/html", "UTF-8", None)

            layout_params_web = self.LinearLayout.LayoutParams(
                self.LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1.0
            )
            layout.addView(web_view, layout_params_web)

            close_button = self.Button(app_context)
            close_button.setText("Закрыть игру")

            class CloseButtonListener(dynamic_proxy(self.OnClickListener)):
                def __init__(self, plugin):
                    super().__init__()
                    self.plugin = plugin

                def onClick(self, view):
                    run_on_ui_thread(self.plugin.close_game)

            close_button.setOnClickListener(CloseButtonListener(self))

            layout_params_button = self.LinearLayout.LayoutParams(
                self.LinearLayout.LayoutParams.MATCH_PARENT,
                self.LinearLayout.LayoutParams.WRAP_CONTENT
            )
            layout.addView(close_button, layout_params_button)

            self.browser_view = layout

            params = self.LayoutParams()
            Build_VERSION = find_class("android.os.Build$VERSION")
            params.type = self.LayoutParams.TYPE_APPLICATION_OVERLAY if Build_VERSION.SDK_INT >= 26 else self.LayoutParams.TYPE_PHONE
            params.flags = self.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            params.format = self.PixelFormat.TRANSLUCENT
            params.gravity = find_class("android.view.Gravity").TOP | find_class("android.view.Gravity").LEFT
            params.width = int(screen_width * 0.9)
            params.height = int(screen_height * 0.8)
            params.x = int(screen_width * 0.05)
            params.y = int(screen_height * 0.1)

            Settings = find_class("android.provider.Settings")
            if Build_VERSION.SDK_INT >= 23 and not Settings.canDrawOverlays(app_context):
                fragment = get_last_fragment()
                activity = fragment.getParentActivity() if fragment else None
                if activity:
                    intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                    Uri.parse("package:" + app_context.getPackageName()))
                    activity.startActivity(intent)
                    self.is_game_open = False
                    BulletinHelper.show_error("Пожалуйста, предоставьте разрешение на оверлей для открытия игры.")
                    return
                else:
                    self.is_game_open = False
                    return

            self.window_manager.addView(self.browser_view, params)
        except Exception as e:
            self.is_game_open = False
            BulletinHelper.show_error(f"Ошибка при открытии игры: {e}")
            self.close_game()

    def close_game(self):
        if not self.is_game_open:
            return

        self.is_game_open = False
        try:
            if self.window_manager and self.browser_view:
                self.window_manager.removeView(self.browser_view)
            self.browser_view = None
            self.window_manager = None
        except Exception as e:
            BulletinHelper.show_error(f"Ошибка при закрытии игры: {e}")
