import traceback
from android_utils import log, OnClickListener
from base_plugin import BasePlugin, PluginsConstants
from ui.settings import Header, Switch, Input
from hook_utils import find_class, get_private_field
from android.view import View
from android.os import Bundle
from client_utils import get_user_config
from org.telegram.ui import ChatActivity
from org.telegram.messenger import R
from com.exteragram.messenger.plugins.ui import PluginsActivity
from ui.bulletin import BulletinHelper

__id__ = "custom_fab"
__name__ = "Custom FAB"
__description__ = "Customize FAB (Floating Action Button)"
__version__ = "1.2.1"
__author__ = "@itsv1eds & @immat0x1"
__min_version__ = "11.9.0"

DEFAULT_HIDE_SMALL = False
DEFAULT_HIDE_LARGE = False
DEFAULT_USE_SAVED = False
DEFAULT_USE_PLUGIN_SETTINGS = False
DEFAULT_USE_PROFILE = False
ON_APP_EVENT = PluginsConstants.ON_APP_EVENT
APP_START = PluginsConstants.APP_START

class FabPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.hook_ref = None
        self.hook_resume_ref = None

    def _on_use_saved_change(self, v):
        if v:
            if self.get_setting("use_plugin_settings", DEFAULT_USE_PLUGIN_SETTINGS):
                BulletinHelper.show_info("Disable Plugin Settings shortcut first")
            if self.get_setting("use_profile", DEFAULT_USE_PROFILE):
                BulletinHelper.show_info("Disable Profile ID shortcut first")
        self._apply_hook()

    def _on_use_plugin_settings_change(self, v):
        if v:
            if self.get_setting("use_saved", DEFAULT_USE_SAVED):
                BulletinHelper.show_info("Disable Saved Messages shortcut first")
            if self.get_setting("use_profile", DEFAULT_USE_PROFILE):
                BulletinHelper.show_info("Disable Profile ID shortcut first")
        self._apply_hook()

    def _on_use_profile_change(self, v):
        if v:
            if self.get_setting("use_saved", DEFAULT_USE_SAVED):
                BulletinHelper.show_info("Disable Saved Messages shortcut first")
            if self.get_setting("use_plugin_settings", DEFAULT_USE_PLUGIN_SETTINGS):
                BulletinHelper.show_info("Disable Plugin Settings shortcut first")
        self._apply_hook()

    def create_settings(self):
        hide_large_current = self.get_setting("hide_large", DEFAULT_HIDE_LARGE)
        items = [
            Header(text="Settings"),
            Switch(
                key="hide_small",
                text="Hide small FAB",
                icon="floating_pencil",
                subtext="Disable small FAB",
                default=DEFAULT_HIDE_SMALL,
                on_change=lambda v: self._apply_hook()
            ),
            Switch(
                key="hide_large",
                text="Hide large FAB",
                icon="msg_camera",
                subtext="Disable large FAB",
                default=DEFAULT_HIDE_LARGE,
                on_change=lambda v: self._apply_hook()
            ),
        ]
        if not hide_large_current:
            items.extend([
                Switch(
                    key="use_saved",
                    text="Saved Messages shortcut",
                    icon="chats_saved",
                    subtext="Replace large FAB and open Saved Messages",
                    default=DEFAULT_USE_SAVED,
                    on_change=lambda v: self._on_use_saved_change(v)
                ),
                Switch(
                    key="use_plugin_settings",
                    text="Plugin Settings shortcut",
                    icon="msg_link",
                    subtext="Replace large FAB and open Plugins settings",
                    default=DEFAULT_USE_PLUGIN_SETTINGS,
                    on_change=lambda v: self._on_use_plugin_settings_change(v)
                ),
                Switch(
                    key="use_profile",
                    text="Profile shortcut",
                    icon="msg_contacts",
                    subtext="Replace large FAB and open Profile",
                    default=DEFAULT_USE_PROFILE,
                    on_change=lambda v: self._on_use_profile_change(v)
                ),
            ])
        if self.get_setting("use_profile", DEFAULT_USE_PROFILE) and not hide_large_current:
            items.append(
                Input(
                    key="profile_id",
                    text="Profile ID",
                    icon="msg_pin_code",
                    default="",
                    subtext="Enter Profile ID to open",
                    on_change=lambda v: self._apply_hook()
                )
            )
        return items
    
    def on_app_event(self, event_type):
        if event_type == APP_START:
            self._apply_hook()

    def on_plugin_load(self):
        self.add_hook(ON_APP_EVENT)
        self._apply_hook()
        try:
            cls = find_class("org.telegram.ui.DialogsActivity")
            method = cls.getClass().getDeclaredMethod("onResume")
            self.hook_resume_ref = self.hook_method(method, ResumeHook(self))
            log(f"[{__id__}] hooked onResume")
        except Exception as e:
            log(f"[{__id__}] resume hook error: {e}")
        log(f"[{__id__}] plugin loaded")

    def on_plugin_unload(self):
        self._remove_hook()
        log(f"[{__id__}] plugin unloaded")

    def _apply_hook(self):
        self._remove_hook()
        try:
            cls = find_class("org.telegram.ui.DialogsActivity")
            ctx = find_class("android.content.Context")
            method = cls.getClass().getDeclaredMethod("createView", ctx.getClass())
            self.hook_ref = self.hook_method(method, CreateViewHook(self))
            log(f"[{__id__}] hooked createView")
        except Exception as e:
            log(f"[{__id__}] hook error: {e}\n{traceback.format_exc()}")

    def _remove_hook(self):
        if getattr(self, 'hook_ref', None):
            self.unhook_method(self.hook_ref)
            self.hook_ref = None
        if getattr(self, 'hook_resume_ref', None):
            self.unhook_method(self.hook_resume_ref)
            self.hook_resume_ref = None

class CreateViewHook:
    def __init__(self, plugin: FabPlugin):
        self.plugin = plugin

    def after_hooked_method(self, param):
        frag = param.thisObject
        try:
            hide_small = self.plugin.get_setting("hide_small", DEFAULT_HIDE_SMALL)
            hide_large = self.plugin.get_setting("hide_large", DEFAULT_HIDE_LARGE)
            use_saved = self.plugin.get_setting("use_saved", DEFAULT_USE_SAVED)
            use_plugin_settings = self.plugin.get_setting("use_plugin_settings", DEFAULT_USE_PLUGIN_SETTINGS)
            use_profile = self.plugin.get_setting("use_profile", DEFAULT_USE_PROFILE)
            profile_id = self.plugin.get_setting("profile_id", "")
            small_btn = get_private_field(frag, "floatingButton2Container")
            large_btn = get_private_field(frag, "floatingButtonContainer")
            icon = get_private_field(frag, "floatingButton")
            only_select = get_private_field(frag, "onlySelect")
            if only_select:
                if small_btn is not None:
                    small_btn.setVisibility(View.GONE)
                if large_btn is not None:
                    large_btn.setVisibility(View.GONE)
                return

            if small_btn is not None:
                small_btn.setVisibility(View.GONE if hide_small else View.VISIBLE)
                if hide_small:
                    small_btn.setOnClickListener(None)

            if large_btn is not None:
                if hide_large:
                    large_btn.setVisibility(View.GONE)
                    large_btn.setOnClickListener(None)
                else:
                    large_btn.setVisibility(View.VISIBLE)
                    if use_profile and profile_id:
                        icon.setImageResource(R.drawable.msg_contacts)
                        def open_profile():
                            try:
                                uid = int(profile_id)
                                b = Bundle()
                                b.putLong("user_id", uid)
                                act = frag.getParentActivity()
                                if act:
                                    frag.presentFragment(ChatActivity(b))
                            except Exception as ex:
                                log(f"[{__id__}] open_profile error: {ex}")
                        large_btn.setOnClickListener(OnClickListener(open_profile))
                        return
                    if use_saved and icon is not None:
                        icon.setImageResource(R.drawable.chats_saved)
                        def open_saved():
                            try:
                                uid = get_user_config().getClientUserId()
                                b = Bundle()
                                b.putLong("user_id", uid)
                                act = frag.getParentActivity()
                                if act:
                                    frag.presentFragment(ChatActivity(b))
                            except Exception as ex:
                                log(f"[{__id__}] open_saved error: {ex}")
                        large_btn.setOnClickListener(OnClickListener(open_saved))
                    elif use_plugin_settings and icon is not None:
                        icon.setImageResource(R.drawable.msg_link)
                        def open_plugin_settings():
                            try:
                                act = frag.getParentActivity()
                                if act:
                                    frag.presentFragment(PluginsActivity())
                            except Exception as ex:
                                log(f"[{__id__}] open_plugin_settings error: {ex}")
                        large_btn.setOnClickListener(OnClickListener(open_plugin_settings))
        except Exception as e:
            log(f"[{__id__}] after_hook error: {e}\n{traceback.format_exc()}")

class ResumeHook:
    def __init__(self, plugin: FabPlugin):
        self.plugin = plugin

    def after_hooked_method(self, param):
        try:
            CreateViewHook(self.plugin).after_hooked_method(param)
        except Exception as e:
            log(f"[{__id__}] resume after_hook error: {e}\n{traceback.format_exc()}")