import math

import client_utils
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from typing import Any, Callable, Tuple
from android.content import Intent, Context
from android.net import Uri as AndroidUri
from org.telegram.messenger import LocaleController
from android.os import Process, HandlerThread, Handler
from android.text import TextUtils, SpannableStringBuilder, Spanned
from android.text.style import ForegroundColorSpan
from android.view import WindowManagerImpl, Window, View, ViewGroup, WindowManager
from androidx.collection import LongSparseArray
from com.exteragram.messenger.utils import ChatUtils
from hook_utils import get_private_field
from java import cast, jint, jlong
from java import dynamic_proxy
from java.lang import Integer, String, Boolean, Long, System, Math
from java.util import ArrayList
from typing import Dict, Any
from java.util.concurrent import CountDownLatch
from org.telegram.messenger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>der, AndroidUtilities, LocaleController, \
    R, FlagSecureReason, DialogObject, \
    ContactsController, ChatObject, NotificationCenter
from org.telegram.messenger import UserConfig, MessagesStorage, MessagesController, MessageObject, \
    NotificationsController, DownloadController
from org.telegram.messenger.browser import Browser
from org.telegram.tgnet import TLRPC
from org.telegram.tgnet.tl import TL_account
from org.telegram.ui import ProfileActivity, ChatActivity, LaunchActivity, PhotoViewer
from org.telegram.ui.ActionBar import AlertDialog
from org.telegram.ui.ActionBar import Theme
from org.telegram.ui.Cells import ChatMessageCell, EmptyCell
from ui.bulletin import BulletinHelper
from ui.settings import Header, Switch, Divider, Input
from android_utils import log, R, run_on_ui_thread
from base_plugin import HookStrategy
from client_utils import get_messages_storage
from client_utils import send_request, get_connections_manager, run_on_queue, get_last_fragment, get_messages_controller

__id__ = "reExtera"
__name__ = "re:extera"
__description__ = "Just a plugin with ToS breaking features."
__author__ = "@bleizix"
__min_version__ = "11.12.0"
__icon__ = "fuki_dum_pjsk_pack/3"
__version__ = "1.4"

FLAG_SECURE = WindowManager.LayoutParams.FLAG_SECURE
FLAG_DELETED = 1 << 16

readReqs = ["TL_messages_readHistory", "TL_messages_readEncryptedHistory", "TL_messages_readDiscussion",
            "TL_messages_readMessageContents", "TL_channels_readHistory", "TL_channels_readMessageContents",
            "TL_messages_markDialogUnread"]
typingReqs = ["TL_messages_setTyping", "TL_messages_setEncryptedTyping"]
storiesReqs = ["TL_stories_readStories", "TL_stories_incrementStoryViews"]
sendMsgReqs = ["TL_messages_sendMessage",
               "TL_messages_sendMedia",
               "TL_messages_sendMultiMedia",
               "TL_messages_forwardMessage",
               "TL_messages_forwardMessages",
               "TL_messages_sendInlineBotResult",
               "TL_messages_sendEncrypted",
               "TL_messages_sendEncryptedFile",
               "TL_messages_sendEncryptedMultiMedia",
               "TL_messages_sendEncryptedService"]
onlineReqs = [*sendMsgReqs,
              *readReqs,
              "TL_messages_editMessage",
              "TL_messages_createChat",
              "TL_channels_createChannel",
              "TL_channels_createForumTopic",
              "TL_channels_leaveChannel",
              "TL_channels_deleteTopicHistory",
              "TL_channels_editForumTopic",
              "TL_messages_updatePinnedMessage",
              "requestCall",
              "acceptCall",
              "confirmCall",
              "TL_stories_sendStory",
              "TL_stories_sendReaction",
              "TL_stories_readStories"]


class IntCb(dynamic_proxy(MessagesStorage.IntCallback)):
    def __init__(self, fn):
        super().__init__()
        self._fn = fn

    def run(self, param):
        try:
            self._fn(param)
        except Exception as e:
            BulletinHelper.show_info(f"Error in int callback: {e}")
            log(str(e))


NOTIFICATION_DELETED = 6969


def getUser(userId, callback):
    req = TLRPC.TL_users_getUsers()
    req.id.add(get_messages_controller().getInputUser(userId))
    send_request(req, callback)


def getScheduleTime(photo, document):
    time = 10.0 + 2.0
    if (document is not None and document.access_hash != 0 and (
            MessageObject.isStickerDocument(document) or MessageObject.isAnimatedStickerDocument(document, True))):
        return math.ceil(time)
    if (document is not None and document.access_hash != 0 and MessageObject.isGifDocument(document)):
        return math.ceil(time)

    photoFileSize = 0
    if (photo is not None):
        maybeSize = FileLoader.getClosestPhotoSizeWithSize(photo.sizes, AndroidUtilities.getPhotoSize())
        if maybeSize is not None:
            photoFileSize = maybeSize.size

    documentFileSize = 0
    if document is not None:
        documentFileSize = document.size
    if (photoFileSize != 0):
        time += max(6, math.ceil(photoFileSize / 1024.0 / 1024.0 * 4.5))
    if (documentFileSize != 0):
        time += max(6, math.ceil(documentFileSize / 1024.0 / 1024.0 * 4.5))

    return math.ceil(time)


def process(response, error):
    if error is None and response is not None:
        if isinstance(response, TLRPC.TL_messages_affectedMessages):
            get_messages_controller().processNewDifferenceParams(-1, response.pts, -1, response.pts_count)


def markReadOnServer(messageId, readMedia, peer):
    req = None

    if isinstance(peer, TLRPC.TL_inputPeerChannel):
        request = TLRPC.TL_channels_readHistory()
        request.channel = MessagesController.getInputChannel(peer)
        request.max_id = int(messageId)

        req = request
    else:
        request = TLRPC.TL_messages_readHistory()
        request.peer = peer
        request.max_id = int(messageId)

        req = request
    req.networkType = int(1337)
    send_request(req, process)


def getMessageId(object):
    messageId = None
    if isinstance(object, TLRPC.TL_messages_sendReaction):
        messageId = object.msg_id
    elif isinstance(object, TLRPC.TL_messages_sendVote):
        messageId = object.msg_id
    return messageId


def getDialogId(peer):
    dialogId = 0
    if peer.chat_id != 0:
        dialogId = -peer.chat_id
    elif peer.channel_id != 0:
        dialogId = -peer.channel_id
    else:
        dialogId = peer.user_id
    return dialogId


def extractPeer(object):
    peer = None
    if isinstance(object, TLRPC.TL_messages_sendMessage):
        peer = object.peer
    elif isinstance(object, TLRPC.TL_messages_sendMedia):
        peer = object.peer
    elif isinstance(object, TLRPC.TL_messages_sendMultiMedia):
        peer = object.peer
    elif isinstance(object, TLRPC.TL_messages_forwardMessages):
        peer = object.to_peer
    elif isinstance(object, TLRPC.TL_messages_sendInlineBotResult):
        peer = object.peer
    if isinstance(object, TLRPC.TL_messages_sendReaction):
        peer = object.peer
    elif isinstance(object, TLRPC.TL_messages_sendVote):
        peer = object.peer
    return peer


class DeletedMessageInfo:
    def __init__(self, selectedAccount: int, channelId: int, messageIds, messageId: int = 0):
        self.selectedAccount = selectedAccount
        self.channelId = channelId
        if (messageIds):
            self.messageIds = messageIds
        else:
            self.messageIds = ArrayList()
            self.messageIds.add(messageId)


class ReExtera(BasePlugin):
    def __init__(self):
        super().__init__()
        self.hook_cache = {}
        self.cachedDeleted = set()
        self.allowedToDelete = set()

    def isCachedDeleted(self, did, msg):
        return str(did) + "_" + str(msg) in self.cachedDeleted

    def setAllowToDelete(self, id, did):
        self.allowedToDelete.add(str(did) + "_" + str(id))

    def isAllowedToDelete(self, id, did):
        return str(did) + "_" + str(id) in self.allowedToDelete
    #
    # def isDeletedFromDB(self, did, id, msgsStorage):
    #     try:
    #         cursor = get_private_field(msgsStorage, "database").queryFinalized("SELECT data FROM messages_v2 WHERE uid = " + str(did) + " AND mid = " + str(id) + " LIMIT 1")
    #
    #     except Exception as e:
    #         log(str(e))


    def markMessagesDeletedInternal(self, msgStorage, dialogId: int, delMsg: ArrayList):
        # delMsg = ArrayList()
        # for msgId in delMsgReal.toArray():
        #     if self.isCachedDeleted(dialogId, msgId) or self.isDeletedFromDB(dialogId, msgId, msgStorage):
        #         continue
        #     delMsg.add(msgId)
        #

        def runnable():
            db = get_private_field(msgStorage, "database")
            query = "SELECT data,mid,uid " + "FROM messages_v2 " + "WHERE " + (
                "is_channel" if dialogId == 0 else "uid") + " = " + str(dialogId) + " AND mid IN (" + TextUtils.join(
                ",",
                delMsg) + ");"
            update = "UPDATE messages_v2 SET data = ? WHERE uid = ? AND mid = ?"
            cursor = db.queryFinalized(query)
            state = db.executeFast(update)

            for msg in delMsg.toArray():
                self.cachedDeleted.add(str(dialogId) + "_" + str(msg))

            while (cursor.next()):
                data = cursor.byteBufferValue(0)
                mid = cursor.intValue(1)
                lastDialogId = cursor.longValue(2)
                data.position(8)
                flags2 = data.readInt32(True)

                if (flags2 & FLAG_DELETED) != 0:
                    continue
                flags2 |= FLAG_DELETED
                data.position(8)
                data.writeInt32(flags2)
                data.position(0)
                state.requery()
                state.bindByteBuffer(1, data)
                state.bindLong(2, lastDialogId)
                state.bindInteger(3, mid)
                state.step()
                data.reuse()
            cursor.dispose()
            state.dispose()

        try:
            run_on_queue(lambda: runnable(), client_utils.PLUGINS_QUEUE)
        except Exception as e:
            log(f"deleted msgs: {e}")


    def markMessagesDeleted(self, dialogId: int, delMsg: ArrayList):
        self.markMessagesDeletedInternal(get_messages_storage(), dialogId, delMsg)



    def _get_hook_method(self, class_obj, method_name, *param_types):
        key = f"{class_obj.getName()}.{method_name}"
        if key in self.hook_cache:
            return self.hook_cache[key]
        try:
            method = class_obj.getDeclaredMethod(method_name, *param_types)
            self.hook_cache[key] = method
            return method
        except Exception as e:
            log(f"wtf! failed to find hook {key}: {e}")
            return None

    def handle_message_click(self, context: Dict[str, Any]):
        message = context.get("message")
        readMedia = (message.messageOwner.media and (
                    message.messageOwner.media.round or message.messageOwner.media.voice) and message.messageOwner.media.ttl_seconds == 0)

        if message:
            markReadOnServer(message.getId(), readMedia, get_messages_controller().getInputPeer(message.getSenderId()))
            message.setIsRead()

            if (readMedia):
                get_messages_controller().markMessageContentAsRead(message)
                message.setContentIsRead()


    def isGhostModeActive(self):
        def isOn(key):
            return self.get_setting(key, True)
        return isOn("dontRead") or isOn("dontType") or isOn("dontOnline") or isOn("dontReadStories") or isOn("immediateOffline")

    def toggleGhostMode(self, not_used: bool):
        state = self.isGhostModeActive()
        def onUi():
            NotificationCenter.getInstance(UserConfig.selectedAccount).postNotificationName(43) # NotificationCenter.mainUserInfoChanged
            NotificationCenter.getInstance(UserConfig.selectedAccount).postNotificationName(284) # NotificationCenter.currentUserPremiumStatusChanged

        self.set_setting("dontRead", not state)
        self.set_setting("dontType", not state)
        self.set_setting("dontOnline", not state)
        self.set_setting("dontReadStories", not state)
        self.set_setting("immediateOffline", not state)

        if (state):
            self.sendOffline()

        run_on_ui_thread(lambda: onUi())

    def on_plugin_load(self):

        if (self.get_setting("dontRead", True)):
            self.add_menu_item(
                MenuItemData(
                    menu_type=MenuItemType.MESSAGE_CONTEXT_MENU,
                    text="Read Message",
                    on_click=self.handle_message_click,
                    icon="msg_view_file"
                )
            )
        state = "Enable Ghost" if self.isGhostModeActive() else "Disable Ghost"

        # IDK HOW TO UPDATE THIS THING'S STATE
        # ITS JUST STAYS "Enable Ghost" OR "Disable Ghost"

        # self.add_menu_item(
        #     MenuItemData(
        #         menu_type=MenuItemType.DRAWER_MENU,
        #         text=state,
        #         on_click=self.toggleGhostMode,
        #         icon="ghost" # looks terrible to be honest
        #     )
        # )



        for hook in typingReqs:
            self.add_hook(hook)
        for hook in storiesReqs:
            self.add_hook(hook)
        for hook in onlineReqs:
            self.add_hook(hook)
        self.add_hook("updateStatus")

        class FixMsgsController():
            def callback(self, response, error):
                if not error and response:
                    MessagesController.processNewDifferenceParams(-1, response.pts, -1, response.pts_count)

            def handle(self, param):
                messageObject = param.args[0]
                if messageObject.scheduled:
                    return

                arrayList = ArrayList()
                if messageObject.messageOwner.mentioned:
                    get_messages_storage().markMentionMessageAsRead(-messageObject.messageOwner.peer_id.channel_id,
                                                                    messageObject.getId(), messageObject.getDialogId())

                arrayList.add(messageObject.getId())
                dialogId = messageObject.getDialogId()
                get_messages_storage().markMessagesContentAsRead(dialogId, arrayList, 0, 0)
                # NotificationCenter.messagesReadContent = 62
                run_on_ui_thread(lambda: NotificationCenter.getInstance(UserConfig.selectedAccount).postNotificationName(62, dialogId, arrayList))


                if messageObject.getId() < 0:
                    MessagesController.markMessageAsRead(messageObject.getDialogId(),
                                                         messageObject.messageOwner.random_id, Integer.MIN_VALUE)
                else:
                    if (messageObject.messageOwner.peer_id.channel_id != 0):
                        req = TLRPC.TL_channels_readMessageContents()
                        req.channel = MessagesController.getInputChannel(
                            get_messages_controller().getChat(Long(messageObject.messageOwner.peer_id.channel_id)))
                        if req.channel is None:
                            return
                        req.id.add(Integer(messageObject.getId()))
                        send_request(req, None)
                    else:
                        req = TLRPC.TL_messages_readMessageContents()
                        req.id.add(Integer(messageObject.getId()))
                        send_request(req, self.callback)

            def replace_hooked_method(self, param):
                try:
                    self.handle(param)
                except Exception as e:
                    log(str(e))
                    BulletinHelper.show_info(str(e))

        method = self._get_hook_method(MessagesController.getClass(), "markMessageContentAsRead", MessageObject)
        if method:
            self.hook_method(method, FixMsgsController())

        class markMentionMessageAsRead():

            def handle(self, param):
                def callback(self, response, error):
                    if error is None and response is not None:
                        get_messages_controller().processNewDifferenceParams(-1, response.pts, -1, response.pts_count)

                mid = param.args[0]
                channelId = param.args[1]
                did = param.args[2]

                def test(r, e):
                    log(f"error: {e}")
                    log(f"response: {r}")

                get_messages_storage().markMentionMessageAsRead(-channelId, mid, did)
                if channelId != 0:
                    req = TLRPC.TL_channels_readMessageContents()
                    req.channel = get_messages_controller().getInputChannel(channelId)

                    if req.channel is None:
                        return
                    req.id.add(Integer(mid))
                    req.networkType = 1337
                    send_request(req, test)
                else:
                    req = TLRPC.TL_messages_readMessageContents()
                    req.id.add(Integer(mid))
                    req.networkType = 1337
                    send_request(req, callback)


            def replace_hooked_method(self, param):
                try:
                    self.handle(param)
                except Exception as e:
                    log(str(e))
                    BulletinHelper.show_info(str(e))

        method = self._get_hook_method(MessagesController.getClass(), "markMentionMessageAsRead", Integer.TYPE,
                                       Long.TYPE,
                                       Long.TYPE)
        if method:
            self.hook_method(method, markMentionMessageAsRead())

        # flag secure remover
        class Remove:
            def before_hooked_method(self, param):
                flags_arg_index = 0
                mask_arg_index = 1
                original_flags = int(param.args[flags_arg_index])
                original_mask = int(param.args[mask_arg_index])

                if (original_mask & FLAG_SECURE) != 0:
                    modified_flags = original_flags & ~FLAG_SECURE
                    param.args[flags_arg_index] = Integer(modified_flags)
                    param.args[mask_arg_index] = Integer(original_mask)

        class Remove2:
            def before_hooked_method(self, param):
                params = param.args[1]
                if (params.flags & 0x00002000) != 0:
                    params.flags &= int(~0x00002000)

        class Remove3:
            def replace_hooked_method(self, param):
                return

        if (self.get_setting("removeFlagSecure", True)):
            method1 = self._get_hook_method(Window.getClass(), "setFlags", Integer.TYPE, Integer.TYPE)
            if method1:
                self.hook_method(method1, Remove())

            method2 = self._get_hook_method(WindowManagerImpl.getClass(), "addView", View, ViewGroup.LayoutParams)
            if method2:
                self.hook_method(method2, Remove2())

            method3 = self._get_hook_method(FlagSecureReason.getClass(), "attach")
            if method3:
                self.hook_method(method3, Remove3())

        class ScheduleHelper():
            def __init__(self, pl):
                self.plugin = pl
                self.hooked = None

            @staticmethod
            def pseudoReply(message: str, caption: str, photo: TLRPC.TL_photo, peer: int,
                            replyQuote: ChatActivity.ReplyQuote, replyToMsg: MessageObject, entities: ArrayList):


                def getLengthOfTheString(string : str) -> int:
                    # since python is bullshit
                    # and counts 1 emoji as 2 symbols

                    return int(len(string.encode(encoding='utf_16_le')) / 2)
                if (TextUtils.isEmpty(message) and TextUtils.isEmpty(caption) and photo is None) or TextUtils.isEmpty(
                        replyToMsg.messageText):
                    return [message, caption]

                fromPeer = replyToMsg.getFromPeerObject()
                name = ""
                if (not DialogObject.isUserDialog(peer) or abs(replyToMsg.getDialogId()) != abs(peer)):
                    if (isinstance(fromPeer, TLRPC.Chat)):
                        name = fromPeer.title
                    elif (isinstance(fromPeer, TLRPC.User)):
                        name = ContactsController.formatName(fromPeer.first_name, fromPeer.last_name)

                if (not TextUtils.isEmpty(name)):
                    name += "\n"

                replyToPeer = replyQuote.peerId if replyQuote else replyToMsg.getSenderId()
                msgText = replyQuote.getText() if replyQuote else replyToMsg.messageText
                pref = str(msgText)




                # python moment
                if (msgText.length() > 100):
                    pref = str(msgText.subSequence(0, 100)) + str("...")
                pref += str(name)

                prefix = pref
                prefixLength = 0
                if (not TextUtils.isEmpty(message)):
                    message = prefix + "\n" + message
                    prefixLength = getLengthOfTheString(prefix) + 1
                elif (not TextUtils.isEmpty(caption)):
                    caption = prefix + "\n" + caption
                    prefixLength = getLengthOfTheString(prefix) + 1
                elif (photo):
                    caption = prefix
                    prefixLength = getLengthOfTheString(prefix)

                if (entities and entities.size() > 0 and prefixLength != 0):
                    for entity in entities.toArray():
                        entity.offset += prefixLength

                bold = TLRPC.TL_messageEntityBold()
                bold.length = getLengthOfTheString(name)
                entities.add(bold)

                mention = TLRPC.TL_inputMessageEntityMentionName()
                mention.user_id = get_messages_controller().getInputUser(replyToPeer)
                mention.length = getLengthOfTheString(prefix)
                entities.add(mention)

                quote = TLRPC.TL_messageEntityBlockquote()
                quote.length = getLengthOfTheString(prefix)
                entities.add(quote)

                return [message, caption]

            def before_hooked_method(self, param):
                params = param.args[0]
                if params.scheduleDate == 0 and self.plugin.get_setting("useScheduled", False):
                    self.hooked = self.plugin.hook_method(
                        self.plugin._get_hook_method(ChatActivity.getClass(), "openScheduledMessages", Integer.TYPE,
                                                     Boolean.TYPE),
                        Remove3())

                    param.args[0].scheduleDate = get_connections_manager().getCurrentTime() + getScheduleTime(
                        params.photo, params.document)

                    method = self.plugin._get_hook_method(ChatActivity.getClass(), "updateBottomOverlay")
                    if method:
                        method.setAccessible(True)
                        fragment = get_last_fragment()
                        method.invoke(fragment)

                if (params.replyToMsg and params.replyToMsg.messageOwner and (
                        (params.replyToMsg.messageOwner.flags2 & FLAG_DELETED) != 0
                        or self.plugin.isCachedDeleted(MessageObject.getPeerId(params.replyToMsg.messageOwner.from_id),
                                                       params.replyToMsg.messageOwner.id))):

                    if (params.entities is None):
                        params.entities = ArrayList()
                    pair = ["", ""]
                    try:
                        pair = self.pseudoReply(params.message, params.caption, params.photo, params.peer,
                                                params.replyQuote, params.replyToMsg, params.entities)
                    except Exception as e:
                        log(f"dw[ad;aw: {e}")

                    params.message = pair[0]
                    params.caption = pair[1]
                    if (params.replyToMsg.messageOwner.flags2 & FLAG_DELETED) != 0:
                        params.replyToMsg = params.replyToTopMsg
                        params.replyQuote = None


            def after_hooked_method(self, param):
                if self.plugin.get_setting("useScheduled", False):
                    try:
                        run_on_queue(lambda: self.plugin.unhook_method(self.hooked), "externalNetworkQueue", 650)
                    except Exception as e:
                        BulletinHelper.show_info(f"e {e}")

        method = self._get_hook_method(SendMessagesHelper.getClass(), "sendMessage",
                                       SendMessagesHelper.SendMessageParams)
        if method:
            self.hook_method(method, ScheduleHelper(self))

        class ScheduleHelper2():
            def __init__(self, pl):
                self.plugin = pl

            def before_hooked_method(self, param):
                messages = param.args[0]
                peer = param.args[1]
                scheduleDateOrig = param.args[5]
                if scheduleDateOrig == 0 and not DialogObject.isEncryptedDialog(peer) and self.plugin.get_setting(
                        "useScheduled", False):
                    param.args[5] = Integer(get_connections_manager().getCurrentTime() + 12)
                    method = self.plugin._get_hook_method(ChatActivity.getClass(), "updateBottomOverlay")
                    if method:
                        method.setAccessible(True)
                        fragment = get_last_fragment()
                        method.invoke(fragment)

                    did = messages.get(0).getDialogId()
                    noForwardsChat = False
                    chat = get_messages_controller().getChat(-did)
                    if (not chat):
                        chat = get_messages_controller().getChat(did)

                    if (not chat):
                        return

                    if (chat.migrated_to):
                        migrated = get_messages_controller().getChat(chat.migrated_to.channel_id)
                        if (migrated):
                            noForwardsChat = migrated.noforwards
                    noForwardsChat = chat.noforwards

                    if (noForwardsChat):
                        def runnable():
                            for message in messages.toArray():
                                extractedText = ChatUtils.getInstance().getMessageText(message, None)
                                params = SendMessagesHelper.SendMessageParams.of(extractedText if extractedText else "",
                                                                                 peer,
                                                                                 param.args[6],  # replyToTopMsg
                                                                                 param.args[6],  # replyToTopMsg
                                                                                 None,
                                                                                 False,
                                                                                 message.messageOwner.entities,
                                                                                 None,
                                                                                 None,
                                                                                 param.args[4],  # notify
                                                                                 0,  # scheduleDate
                                                                                 None,  # sendAnimationDate
                                                                                 False,  # updateStickersOrder
                                                                                 )
                                run_on_ui_thread(
                                    lambda: SendMessagesHelper.getInstance(UserConfig.selectedAccount).sendMessage(
                                        params))

                        try:
                            run_on_queue(lambda: runnable(), "externalNetworkQueue")
                        except Exception as e2:
                            log(f"caught e2 while processing forward: {e2}")
                            BulletinHelper.show_info(f"caught e2 while processing forward: {e2}")
                        param.setResult(jint(0))
                        return

        try:
            method = self._get_hook_method(SendMessagesHelper.getClass(), "sendMessage", ArrayList, Long.TYPE,
                                           Boolean.TYPE,
                                           Boolean.TYPE, Boolean.TYPE, Integer.TYPE, MessageObject,
                                           Integer.TYPE, Long.TYPE)
            if method:
                self.hook_method(method, ScheduleHelper2(self))
        except Exception as e:
            BulletinHelper.show_info(f"errored: {e}")
            log("errored" + str(e))

        class LastSeenProfileActivity:
            def __init__(self, pl):
                self.plugin = pl
                self.lastStatusUpdate = 0
                self.overridenValue = ""

            def setText(self, text: str, param):
                try:
                    field = ProfileActivity.getClass().getDeclaredField("onlineTextView")
                    field.setAccessible(True)
                    textViews = field.get(param.thisObject)
                    run_on_ui_thread(lambda: textViews[0].setText(text))
                    run_on_ui_thread(lambda: textViews[1].setText(text))
                    run_on_ui_thread(lambda: textViews[2].setText(text))
                    run_on_ui_thread(lambda: textViews[3].setText(text))
                except Exception as e:
                    AndroidUtilities.addToClipboard("blya " + str(e))

            def processTime(self, time, param):
                try:
                    formatted = ""
                    if (time < get_connections_manager().getCurrentTime()):
                        formatted = LocaleController.formatDateTime(time, True)
                    else:
                        formatted = LocaleController.getString(R.string.Online)

                    self.plugin.sendOffline()
                    run_on_queue(lambda: self.update(param, True), "externalNetworkQueue", 1800)
                    self.overridenValue = formatted
                    self.setText(formatted, param)
                except Exception as e:
                    AndroidUtilities.addToClipboard(e)

            def update(self, param, force):
                field = ProfileActivity.getClass().getDeclaredField("userId")
                field.setAccessible(True)
                userId = field.get(param.thisObject)
                if (int(userId) != UserConfig.getInstance(UserConfig.selectedAccount).clientUserId):
                    return
                if (System.currentTimeMillis() - self.lastStatusUpdate <= 10000 and not force):
                    if (self.overridenValue is not None and len(self.overridenValue) != 0):
                        self.setText(self.overridenValue, param)
                    return
                self.lastStatusUpdate = System.currentTimeMillis()
                getUser(int(userId), lambda resp, error: self.processTime(resp.objects.get(0).status.expires, param))

            def after_hooked_method(self, param):
                if self.plugin.get_setting("dontOnline", True) or self.plugin.get_setting("immediateOffline", True):
                    self.update(param, False)

        updProfData = self._get_hook_method(ProfileActivity.getClass(), "updateProfileData", Boolean.TYPE)
        if updProfData:
            updProfData.setAccessible(True)
            self.hook_method(updProfData, LastSeenProfileActivity(self))

        class resFalse():
            def before_hooked_method(self, param):
                param.setResult(False)

        class resTrue():
            def before_hooked_method(self, param):
                param.setResult(True)

        if (self.get_setting("removeNoForward", True)):
            method1 = self._get_hook_method(MessageObject.getClass(), "canForwardMessage")
            if method1:
                self.hook_method(method1, resTrue())

            method2 = self._get_hook_method(ChatActivity.getClass(), "hasSelectedNoforwardsMessage")
            if method2:
                self.hook_method(method2, resFalse())

            method3 = self._get_hook_method(MessagesController.getClass(), "isChatNoForwards", TLRPC.Chat)
            if method3:
                self.hook_method(method3, resFalse())

        class Trollas():
            def __init__(self, pl):
                self.plugin = pl

            def before_hooked_method(self, param):
                intent = param.args[0]
                data = intent.getData()
                url = data.toString()
                if (url.startswith("tg://re")):
                    path = url.replace("tg://re", "")
                    if (path == ("/augh")):
                        audioManager = LaunchActivity.getLastFragment().getContext().getSystemService(
                            Context.AUDIO_SERVICE)
                        for i in range(25):
                            audioManager.adjustVolume(1, 4)  # AudioManager.ADJUST_RAISE & AudioManager.FLAG_PLAY_SOUND

                        def runAfterEnd(dialog, chat):
                            log("hel nah")
                            dialog.dismiss()
                            PhotoViewer.getInstance().closePhoto(True, False)
                            run_on_ui_thread(lambda: chat.finishFragment(True))

                        def run():
                            chatActivity = LaunchActivity.getLastFragment()
                            cell = chatActivity.findMessageCell(2, False)
                            method = self.plugin._get_hook_method(ChatActivity.getClass(), "openPhotoViewerForMessage",
                                                                  ChatMessageCell, MessageObject)
                            if method:
                                method.setAccessible(True)
                                method.invoke(chatActivity, cell, cell.getMessageObject())

                            duration = cell.getMessageObject().getDuration()
                            progressDialog = AlertDialog(chatActivity.getContext(), 2)  # AlertDialog.ALERT_TYPE_LOADING
                            progressDialog.setCanceledOnTouchOutside(False)
                            progressDialog.setCancelable(False)
                            progressDialog.setCanCancel(False)
                            progressDialog.setContentView(EmptyCell(chatActivity.getContext()))
                            progressDialog.show()
                            progressDialog.setContentView(EmptyCell(chatActivity.getContext()))
                            run_on_ui_thread(lambda: runAfterEnd(progressDialog, chatActivity),
                                             jlong(duration * 1000 * 500))

                        browserProgress = Browser.Progress()
                        field = Browser.Progress.getClass().getDeclaredField("onEndListener")
                        field.setAccessible(True)

                        field.set(browserProgress, R(run_on_ui_thread(run, 700)))

                        Browser.openUrl(LaunchActivity.getLastFragment().getContext(),
                                        AndroidUri.parse("https://t.me/exteraGramEasters/2"), False, False,
                                        browserProgress)

        try:
            method = self._get_hook_method(LaunchActivity.getClass(), "handleIntent", Intent, Boolean.TYPE,
                                           Boolean.TYPE,
                                           Boolean.TYPE, Browser.Progress, Boolean.TYPE, Boolean.TYPE)
            if method:
                self.hook_method(method, Trollas(self))
        except Exception as e:
            log(f"caught in handleIntent! {e}")

        class MessagesStorageHook():
            def __init__(self, pl):
                self.plugin = pl

            def before_hooked_method(self, param):
                original = param.args[1]  # ArrayList<Integer>
                if (original is None or original.isEmpty()):
                    return

                channel_id = param.args[0]
                if (channel_id > 0):
                    channel_id = 0

                self.plugin.markMessagesDeletedInternal(param.thisObject, channel_id, original)
                param.setResult(None)

        try:
            if (self.get_setting("antiRecall", True)):
                method = self._get_hook_method(MessagesStorage.getClass(), "markMessagesAsDeletedInternal", Long.TYPE,
                                               ArrayList,
                                               Boolean.TYPE, Integer.TYPE, Integer.TYPE)
                if method:
                    self.hook_method(method, MessagesStorageHook(self))
        except Exception as e:
            log("exception in messagesStorage: " + str(e))
            BulletinHelper.show_error("markMessagesAsDeletedInternal: " + str(e))

        class UpdDialogsHook():
            def __init__(self, pl):
                self.plugin = pl

            def before_hooked_method(self, param):
                channelId = -param.args[1]
                if channelId > 0:
                    channelId = 0
                msgIds = param.args[2]
                if (msgIds and not msgIds.isEmpty()):
                    self.plugin.markMessagesDeletedInternal(param.thisObject, channelId, msgIds)
                param.setResult(None)

        try:
            if (self.get_setting("antiRecall", True)):
                method = self._get_hook_method(MessagesStorage.getClass(), "updateDialogsWithDeletedMessages",
                                               Long.TYPE,
                                               Long.TYPE,
                                               ArrayList, ArrayList, Boolean.TYPE)
                if method:
                    self.hook_method(method, UpdDialogsHook(self))
        except Exception as e:
            log("exception in updateDialogsWithDeletedMessages: " + str(e))
            BulletinHelper.show_error("updateDialogsWithDeletedMessages: " + str(e))

        try:
            if (self.get_setting("antiRecall", True)):
                method = self._get_hook_method(MessagesStorage.getClass(), "updateDialogsWithDeletedMessagesInternal",
                                               Long.TYPE,
                                               Long.TYPE, ArrayList, ArrayList)
                if method:
                    self.hook_method(method, UpdDialogsHook(self))
        except Exception as e:
            log("exception in updateDialogsWithDeletedMessagesInternal: " + str(e))
            BulletinHelper.show_error("updateDialogsWithDeletedMessagesInternal: " + str(e))

        class NotificationsHook():
            def __init__(self, pl):
                self.plugin = pl

            def before_hooked_method(self, param):
                isReact = param.args[1]
                if (self.plugin.get_setting("antiRecall", True) and not isReact):
                    param.setResult(None)

        try:
            if (self.get_setting("antiRecall", True)):
                method = self._get_hook_method(NotificationsController.getClass(),
                                               "removeDeletedMessagesFromNotifications",
                                               LongSparseArray, Boolean.TYPE)
                if method:
                    self.hook_method(method, NotificationsHook(self))
        except Exception as e:
            log("exception in NotificationsHook: " + str(e))
            BulletinHelper.show_error("NotificationsHook: " + str(e))

        class DownloaderHook():
            def before_hooked_method(self, param):
                msg = param.args[0]
                if (msg.flags2 & FLAG_DELETED) != 0:
                    param.setResult(False)

        try:
            if (self.get_setting("antiRecall", True)):
                method = self._get_hook_method(DownloadController.getClass(), "canDownloadMedia", TLRPC.Message)
                if method:
                    self.hook_method(method, DownloaderHook())
        except Exception as e:
            log("exception in DownloaderHook: " + str(e))
            BulletinHelper.show_error("DownloaderHook: " + str(e))

        # MessagesController hooks
        class processUpdateArray():
            def __init__(self, pl):
                self.plugin = pl

            # TODO: refactor as on_update_hook
            def before_hooked_method(self, param):
                lst = param.args[0]
                if (lst is None or lst.isEmpty()):
                    return None
                newUpdates = ArrayList()

                for upd in lst.toArray():
                    if (not upd.getClass().equals(TLRPC.TL_updateDeleteMessages)
                            and not upd.getClass().equals(TLRPC.TL_updateDeleteChannelMessages)):
                        newUpdates.add(upd)
                        continue

                    if (upd.getClass().equals(TLRPC.TL_updateDeleteMessages)):
                        update = cast(TLRPC.TL_updateDeleteMessages, upd)
                        messages = update.messages
                        field = MessagesController.getClass().getDeclaredField("dialogMessagesByIds")
                        dialogMessages = field.get(param.thisObject)
                        for msgId in messages.toArray():
                            msgObj = dialogMessages.get(msgId)
                            if (msgObj is None):
                                break  # ????
                            else:
                                messageOwner = msgObj.messageOwner
                                messageOwner.flags2 = messageOwner.flags2 | FLAG_DELETED
                        self.plugin.markMessagesDeleted(0, messages)
                    elif (upd.getClass().equals(TLRPC.TL_updateDeleteChannelMessages)):
                        update = cast(TLRPC.TL_updateDeleteChannelMessages, upd)
                        messages = update.messages
                        field = MessagesController.getClass().getDeclaredField("dialogMessage")
                        dialogMessage = field.get(param.thisObject)
                        dialogMessages = dialogMessage.get(-update.channel_id)
                        for msgObj in dialogMessages.toArray():
                            messageOwner = msgObj.messageOwner
                            if (messages.contains(messageOwner.id)):
                                messageOwner.flags2 = messageOwner.flags2 | FLAG_DELETED
                        self.plugin.markMessagesDeleted(-update.channel_id, messages)

                param.args[0] = newUpdates

        try:
            if (self.get_setting("antiRecall", True)):
                method = self._get_hook_method(MessagesController.getClass(), "processUpdateArray", ArrayList,
                                               ArrayList, ArrayList,
                                               Boolean.TYPE, Integer.TYPE)
                if method:
                    self.hook_method(method, processUpdateArray(self))
        except Exception as e:
            log(str(e))
            BulletinHelper.show_error("update array: " + str(e))

        class CheckChannelError():
            def __init__(self, pl):
                self.plugin = pl

            def before_hooked_method(self, param):
                if (self.plugin.get_setting("antiRecall", True)):
                    param.setResult(None)

        try:
            method = self._get_hook_method(MessagesController.getClass(), "checkChannelError", String, Long.TYPE)
            if method:
                method.setAccessible(True)
                self.hook_method(method, CheckChannelError(self))
        except Exception as e:
            log(str(e))
            BulletinHelper.show_error("CheckChannelError: " + str(e))

        # MessagesController hooks end

        # ChatObject hooks
        class ChatObjectHook():
            def __init__(self, pl):
                self.plugin = pl

            def before_hooked_method(self, param):
                if (self.plugin.get_setting("antiRecall", True)):
                    chat = param.args[0]
                    param.setResult(chat is None or chat.left)

        try:
            method1 = self._get_hook_method(ChatObject.getClass(), "isLeftFromChat", TLRPC.Chat)
            if method1:
                self.hook_method(method1, ChatObjectHook(self))

            method2 = self._get_hook_method(ChatObject.getClass(), "isKickedFromChat", TLRPC.Chat)
            if method2:
                self.hook_method(method2, ChatObjectHook(self))

            method3 = self._get_hook_method(ChatObject.getClass(), "isNotInChat", TLRPC.Chat)
            if method3:
                self.hook_method(method3, ChatObjectHook(self))
        except Exception as e:
            log(str(e))
            BulletinHelper.show_error("ChatObjectHook: " + str(e))

        # ChatObject hooks end

        # UI (deleted mark)

        class ChatMsgCell():
            def __init__(self, pl):
                self.plugin = pl

            def process(self, param):
                messageObject = param.args[0]
                if (messageObject is None):
                    return
                owner = messageObject.messageOwner
                if (owner is None):
                    return
                flags2 = owner.flags2
                if (not ((flags2 & FLAG_DELETED) != 0) and
                        not (self.plugin.isCachedDeleted(MessageObject.getPeerId(owner.from_id), owner.id))):
                    return None

                field = ChatMessageCell.getClass().getDeclaredField("currentMessageObject")
                field.setAccessible(True)
                text = self.plugin.get_setting("deletedMark", "deleted")
                cell = param.thisObject
                field2 = ChatMessageCell.getClass().getDeclaredField("currentTimeString")
                field2.setAccessible(True)
                currTimeString = field2.get(cell)
                if (currTimeString is None):
                    return

                spannable = SpannableStringBuilder(text)
                if (self.plugin.get_setting("useRed", False)):
                    color = ForegroundColorSpan(cell.getThemedColor(Theme.key_color_red))
                    spannable.setSpan(color, 0, spannable.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                spannable.append(" ")
                currTimeString.insert(0, spannable)
                field2.set(cell, currTimeString)
                paint = Theme.chat_timePaint
                if (paint is not None):
                    ceil = int(Math.ceil(paint.measureText(spannable, 0, spannable.length())))
                    f = ChatMessageCell.getClass().getDeclaredField("timeTextWidth")
                    f.setAccessible(True)
                    timeTextWidth = f.get(cell)

                    f2 = ChatMessageCell.getClass().getDeclaredField("timeWidth")
                    f2.setAccessible(True)
                    timeWidth = f2.get(cell)

                    f.set(cell, jint(ceil + timeTextWidth))
                    f2.set(cell, jint(ceil + timeWidth))

            def after_hooked_method(self, param):
                # many deleted messages lead to lags
                # tried to run_on_ui_thread, traceback:

                # Error in runnable: 'NoneType' object is not callable
                # File ".../android_utils.py", line 56, in run
                # self._fn()

                # ¯\_(ツ)_/¯

                # so tried to fix using caching of hooks
                # need to test on supporters
                try:
                    self.process(param)
                except Exception as e:
                    log(f"caught in cell: {e}")

        try:
            if (self.get_setting("antiRecall", True)):
                method = self._get_hook_method(ChatMessageCell.getClass(), "measureTime", MessageObject)
                if method:
                    self.hook_method(method, ChatMsgCell(self))
        except Exception as e:
            log("chat msg cell exception!" + str(e))


        # updating message with "deleted" mark
        # TODO: implement: posting notification, handling it(updating msg)
        # currently too lazy for this

        # class ChatActivityOnFragmentCreate():
        #     def __init__(self, pl):
        #         self.plugin = pl
        #     def after_hooked_method(self, param):
        #         if (self.plugin.get_setting("keepDeletedInCache", True)):
        #             this = param.thisObject
        #             this.getNotificationCenter().addObserver(this, NOTIFICATION_DELETED)
        # try:
        #     self.hook_method(
        #         ChatActivity.getClass().getDeclaredMethod("onFragmentCreate"),
        #         ChatActivityOnFragmentCreate(self)
        #     )
        # except Exception as e:
        #     log(f"hell naaah {e}")

    # END ANTI-RECALL HOOKS

    def create_settings(self):
        lang = LocaleController.getInstance().getCurrentLocale().getLanguage()
        mark = self.get_setting("deletedMark", "deleted")

        strings = {
            'ru': {
                'dontRead': "Не читать сообщения",
                'dontType': "Не отправлять «печатает»",
                'dontOnline': "Не отправлять «онлайн»",
                'dontReadStories': "Не читать истории",
                'immediateOffline': "Автоматический «оффлайн»",
                'readOnInteract': "Читать при действиях",
                'useScheduled': "Использовать отложку",
                'antiRecall': "Не удалять сообщения",
                'antiRecallHeader': "Удаленные Сообщения",
                'GhostHeader': "Режим Призрака",
                'Misk': "Дополнительно",
                'removeFlagSecure': "Отключить FlagSecure",
                'red': " будет красным",
                'deletedMark': "Метка Удалёнок",
                'removeNoForward': "Убрать ограничение на сохранение",
                "noForwardInfo": "Пересылка запрещенного останется недоступной"
            },
            'en': {
                'dontRead': "Don't Read Messages",
                'dontType': "Don't Send Typing",
                'dontOnline': "Don't Send Online",
                'dontReadStories': "Don't Read Stories",
                'immediateOffline': "Go Offline Automatically",
                'readOnInteract': "Read on Interact",
                'useScheduled': "Schedule Messages",
                'antiRecall': "Save Deleted Messages",
                'antiRecallHeader': "Anti Recall",
                'GhostHeader': "Ghost Mode",
                'Misk': "Misc",
                'removeFlagSecure': "Remove FlagSecure",
                'red': " will be red",
                'deletedMark': "Deleted Mark",
                'removeNoForward': "Remove Copy And Save Restrics",
                "noForwardInfo" : "You anyway won't be able to forward messages"
            }
        }

        lang_key = 'ru' if lang.startswith('ru') else 'en'
        s = strings[lang_key]


        return [
            Header(text=s['GhostHeader']),
            Switch(
                key="dontRead",
                text=s['dontRead'],
                default=True
            ),
            Switch(
                key="dontReadStories",
                text=s['dontReadStories'],
                default=True
            ),
            Switch(
                key="dontOnline",
                text=s['dontOnline'],
                default=True
            ),
            Switch(
                key="dontType",
                text=s['dontType'],
                default=True
            ),
            Switch(
                key="immediateOffline",
                text=s['immediateOffline'],
                default=True
            ),
            Divider(),
            Switch(
                key="readOnInteract",
                text=s['readOnInteract'],
                default=True,
                on_change=lambda enabled: self.handleReadOnInteractEvent(enabled)
            ),
            Switch(
                key="useScheduled",
                text=s['useScheduled'],
                default=False,
                on_change=lambda enabled: self.handleScheduledEvent(enabled)
            ),
            Divider(),
            Header(text=s['Misk']),
            Switch(
                key="removeFlagSecure",
                text=s['removeFlagSecure'],
                default=True
            ),
            Switch(
                key="removeNoForward",
                text=s['removeNoForward'],
                subtext=s["noForwardInfo"],
                default=True
            ),
            Divider(),
            Header(text=s['antiRecallHeader']),
            Switch(
                key="antiRecall",
                text=s['antiRecall'],
                default=True
            ),
            Switch(
                key="useRed",
                text=f"\"{mark}\"" + s['red'],
                default=True
            ),
            Input(
                key="deletedMark",
                text=s['deletedMark'],
                default="deleted"
            )

        ]

    def pre_request_hook(self, request_name, account, request):
        if request.networkType == 1337:
            # this is "whitelisted" request
            # skip checks
            # log(f"sending as normal req: {request_name}")
            # if ("readMessageContents" in request_name):
            #     log(f"req id : {request.id}")
            # request.networkType = 0
            return HookResult(strategy=HookStrategy.DEFAULT)

        if request_name in readReqs and self.get_setting("dontRead", True):
            return HookResult(strategy=HookStrategy.CANCEL)
        if request_name in typingReqs and self.get_setting("dontType", True):
            return HookResult(strategy=HookStrategy.CANCEL)
        if request_name in storiesReqs and self.get_setting("dontReadStories", True):
            return HookResult(strategy=HookStrategy.CANCEL)
        if request_name == "updateStatus" and self.get_setting("dontOnline", True):
            request.offline = True
            return HookResult(strategy=HookStrategy.MODIFY, request=request)

        maybePeer = extractPeer(request)
        if ((
                request_name in sendMsgReqs or request_name == "TL_messages_sendReaction" or request_name == "TL_messages_sendVote")
                and maybePeer and self.get_setting("readOnInteract", True)):
            dialogId = getDialogId(maybePeer)
            messageId = getMessageId(request)
            try:
                # ne rabotaet anyway
                callback = IntCb(lambda maxId: markReadOnServer(maxId, False, maybePeer))
                if messageId is None:
                    get_messages_storage().getDialogMaxMessageId(dialogId, callback)
                else:
                    markReadOnServer(messageId, False, maybePeer)
            except Exception as e:
                log(f"caught: {e}")

    def post_request_hook(self, request_name: str, account: int, response: Any, error: Any):
        if request_name in onlineReqs and self.get_setting("immediateOffline", True):
            run_on_queue(lambda: self.sendOffline(), "externalNetworkQueue", 1000)

    def sendOffline(self):
        def h(r, e):
            pass

        req = TL_account.updateStatus()
        req.offline = True
        send_request(req, h)

    def handleReadOnInteractEvent(self, enabled: bool):
        if (enabled and self.get_setting("useScheduled", False)):
            BulletinHelper.show_info("Schedule Messages cannot be used with Read on Interact")
            self.set_setting("useScheduled", False)

    def handleScheduledEvent(self, enabled: bool):
        if (enabled and self.get_setting("readOnInteract", True)):
            BulletinHelper.show_info("Read On Interact cannot be used with Schedule Messages")
            self.set_setting("readOnInteract", False)