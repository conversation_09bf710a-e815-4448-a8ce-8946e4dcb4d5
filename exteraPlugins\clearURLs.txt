import re
import traceback
from typing import List, Any
from urllib.parse import urlparse, parse_qsl, urle<PERSON>de, urlunparse

from ui.bulletin import BulletinHelper
from android_utils import log as _log
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy

from java.util import Locale

__name__ = "ClearURLs"
__description__ = "Clear the sent URLs from tracking shit (requires zwylib)"
__icon__ = "zwyPluginsIcons/1"
__id__ = "clearURLs"
__version__ = "1.0.2"
__author__ = "@zwylair"
__min_version__ = "11.9.1"

DEFAULT_RULES = [
    "action_object_map",
    "action_type_map",
    "action_ref_map",
    "spm@*.aliexpress.com",
    "scm@*.aliexpress.com",
    "aff_platform",
    "aff_trace_key",
    "algo_expid@*.aliexpress.*",
    "algo_pvid@*.aliexpress.*",
    "btsid",
    "ws_ab_test",
    "pd_rd_*@amazon.*",
    "_encoding@amazon.*",
    "psc@amazon.*",
    "tag@amazon.*",
    "ref_@amazon.*",
    "pf_rd_*@amazon.*",
    "pf@amazon.*",
    "crid@amazon.*",
    "keywords@amazon.*",
    "sprefix@amazon.*",
    "sr@amazon.*",
    "ie@amazon.*",
    "node@amazon.*",
    "qid@amazon.*",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "sc_cid",
    "mkt_tok",
    "trk",
    "trkCampaign",
    "ga_*",
    "gclid",
    "gclsrc",
    "hmb_campaign",
    "hmb_medium",
    "hmb_source",
    "spReportId",
    "spJobID",
    "spUserID",
    "spMailingID",
    "itm_*",
    "s_cid",
    "elqTrackId",
    "elqTrack",
    "assetType",
    "assetId",
    "recipientId",
    "campaignId",
    "siteId",
    "mc_cid",
    "mc_eid",
    "pk_*",
    "sc_campaign",
    "sc_channel",
    "sc_content",
    "sc_medium",
    "sc_outcome",
    "sc_geo",
    "sc_country",
    "nr_email_referer",
    "vero_conv",
    "vero_id",
    "yclid",
    "_openstat",
    "mbid",
    "cmpid",
    "cid",
    "c_id",
    "campaign_id",
    "Campaign",
    "hash@ebay.*",
    "fb_action_ids",
    "fb_action_types",
    "fb_ref",
    "fb_source",
    "fbclid",
    "<EMAIL>",
    "<EMAIL>",
    "gs_l",
    "gs_lcp@google.*",
    "ved@google.*",
    "ei@google.*",
    "sei@google.*",
    "gws_rd@google.*",
    "gs_gbg@google.*",
    "gs_mss@google.*",
    "gs_rn@google.*",
    "_hsenc",
    "_hsmi",
    "__hssc",
    "__hstc",
    "hsCtaTracking",
    "<EMAIL>",
    "<EMAIL>",
    "t@*.twitter.com",
    "s@*.twitter.com",
    "ref_*@*.twitter.com",
    "t@*.x.com",
    "s@*.x.com",
    "ref_*@*.x.com",
    "t@*.fixupx.com",
    "s@*.fixupx.com",
    "ref_*@*.fixupx.com",
    "t@*.fxtwitter.com",
    "s@*.fxtwitter.com",
    "ref_*@*.fxtwitter.com",
    "t@*.vxtwitter.com",
    "s@*.vxtwitter.com",
    "ref_*@*.vxtwitter.com",
    "t@*.twittpr.com",
    "s@*.twittpr.com",
    "ref_*@*.twittpr.com",
    "t@*.fixvx.com",
    "s@*.fixvx.com",
    "ref_*@*.fixvx.com",
    "tt_medium",
    "tt_content",
    "lr@yandex.*",
    "redircnt@yandex.*",
    "feature@*.youtube.com",
    "kw@*.youtube.com",
    "si@*.youtube.com",
    "pp@*.youtube.com",
    "si@*.youtu.be",
    "wt_zmc",
    "utm_source",
    "utm_content",
    "utm_medium",
    "utm_campaign",
    "utm_term",
    "<EMAIL>",
    "igshid",
    "igsh",
    "<EMAIL>",
    "<EMAIL>",
]


class Cleaner:
    def __init__(self, rules: List = None):
        self.rules = rules if rules else DEFAULT_RULES
        self.universal_rules = set()
        self.rules_by_host = {}
        self.host_regex_map = {}
        self._compile_rules()

    @staticmethod
    def _escape_regex(text: str) -> str:
        return re.escape(text).replace(r'\*', '.+?')

    def _compile_rules(self):
        for rule in self.rules:
            if '@' not in rule:
                self.universal_rules.add(re.compile(f"^{self._escape_regex(rule)}$"))
            else:
                param, host = rule.split('@')
                param_regex = re.compile(f"^{self._escape_regex(param)}$")
                host_regex = re.compile(
                    f"^((www\\.)?){self._escape_regex(host)}$".replace(r'\.\*', '.*')
                )
                key = host_regex.pattern

                if key not in self.rules_by_host:
                    self.rules_by_host[key] = []
                    self.host_regex_map[key] = host_regex

                self.rules_by_host[key].append(param_regex)

    def _remove_params(self, url: str) -> str:
        try:
            parsed_url = urlparse(url)
        except Exception:
            return url

        host = parsed_url.hostname or ""
        query_params = parse_qsl(parsed_url.query, keep_blank_values=True)
        cleaned_params = []

        for key, value in query_params:
            if any(regex.match(key) for regex in self.universal_rules):
                continue

            matched = False
            for host_key, host_regex in self.host_regex_map.items():
                if host_regex.match(host):
                    if any(param_regex.match(key) for param_regex in self.rules_by_host[host_key]):
                        matched = True
                        break

            if not matched:
                cleaned_params.append((key, value))

        new_query = urlencode(cleaned_params)
        cleaned_url = parsed_url._replace(query=new_query)
        return urlunparse(cleaned_url)

    def process_message(self, message: str) -> str:
        return re.sub(
            r'https?://[^\s<]+[^<.,:;"\')\]\s]',
            lambda match: self._remove_params(match.group(0)),
            message
        )


class Locales:
    default = {"zwylib_was_not_found": "ZwyLib plugin required for this plugin is not found!"}
    ru = {"zwylib_was_not_found": "Требуемый плагин ZwyLib не найден!"}
    uk = {"zwylib_was_not_found": "Не знайдено обов’язковий плагін ZwyLib!"}
    en = default


def localise(key: str) -> str:
    locale = Locale.getDefault().getLanguage()
    locale_dict = getattr(Locales, locale, Locales.default)
    return locale_dict.get(key, key)


def import_zwylib(show_import_error_bulletin = True):
    global zwylib

    try:
        import zwylib
    except ImportError:
        if show_import_error_bulletin:
            show_error_bulletin(localise("zwylib_was_not_found"))


def is_zwylib_present() -> bool:
    return zwylib is not None


def show_error_bulletin(message: str):
    BulletinHelper.show_error(f"{__name__}: " + message)


def log(obj):
    _log(f"{__name__}: " + str(obj))


AUTOUPDATE_CHANNEL_ID = 2521243181
AUTOUPDATE_CHANNEL_USERNAME = "zwyPlugins"
AUTOUPDATE_MESSAGE_ID = 34

zwylib: Any = None


class ClearURLs(BasePlugin):
    cleaner = Cleaner()

    def on_plugin_load(self):
        self.add_on_send_message_hook()

        import_zwylib()
        if is_zwylib_present():
            zwylib.add_autoupdater_task(__id__, AUTOUPDATE_CHANNEL_ID, AUTOUPDATE_CHANNEL_USERNAME, AUTOUPDATE_MESSAGE_ID)

        log("Loaded")

    def on_plugin_unload(self):
        if is_zwylib_present():
            zwylib.remove_autoupdater_task(__id__)
        log("Unloaded")

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        try:
            if re.search(r'http(s)?://', params.message):
                params.message = self.cleaner.process_message(params.message)
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            return HookResult()
        except Exception:
            params.message = f"An exception occurred in plugin {__name__}:\n\n{traceback.format_exc()}"
            return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)
