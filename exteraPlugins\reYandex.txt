"""



                            ДИСКЛЕЙМЕР

Если при создании своего плагина вы решили использовать готовые кодовые решения
нашего плагина у себя, то не забудьте упомянуть в описании своего плагина
канал @MeeowPlugins в качестве кредитов за помощь в разработке плагина. Спасибо


                  ⣾⡇⣿⣿⡇⣾⣿⣿⣿⣿⣿⣿⣿⣿⣄⢻⣦⡀⠁⢸⡌⠻⣿⣿⣿⡽⣿⣿
                  ⡇⣿⠹⣿⡇⡟⠛⣉⠁⠉⠉⠻⡿⣿⣿⣿⣿⣿⣦⣄⡉⠂⠈⠙⢿⣿⣝⣿
                  ⠤⢿⡄⠹⣧⣷⣸⡇⠄⠄⠲⢰⣌⣾⣿⣿⣿⣿⣿⣿⣶⣤⣤⡀⠄⠈⠻⢮
                  ⠄⢸⣧⠄⢘⢻⣿⡇⢀⣀⠄⣸⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣧⡀⠄⢀
                  ⠄⠈⣿⡆⢸⣿⣿⣿⣬⣭⣴⣿⣿⣿⣿⣿⣿⣿⣯⠝⠛⠛⠙⢿⡿⠃⠄⢸
                  ⠄⠄⢿⣿⡀⣿⣿⣿⣾⣿⣿⣿⣿⣿⣿⣿⣿⣿⣷⣿⣿⣿⣿⡾⠁⢠⡇⢀
                  ⠄⠄⢸⣿⡇⠻⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣏⣫⣻⡟⢀⠄⣿⣷⣾
                  ⠄⠄⢸⣿⡇⠄⠈⠙⠿⣿⣿⣿⣮⣿⣿⣿⣿⣿⣿⣿⣿⡿⢠⠊⢀⡇⣿⣿
                  ⠒⠤⠄⣿⡇⢀⡲⠄⠄⠈⠙⠻⢿⣿⣿⠿⠿⠟⠛⠋⠁⣰⠇⠄⢸⣿⣿⣿



                            DISCLAIMER

If, when creating your plugin, you decided to use the ready-made code solutions
of our plugin, then do not forget to mention the @MeeowPlugins channel in the description
of your plugin as credits for help in developing your plugin. Thanks



"""

import datetime
import math
import os
import random
import re
from typing import Optional

import requests
import textwrap
import threading
import time
import plugins_manager

from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance

from android.content import Intent
from android.net import Uri

from java import jclass
from java.io import File
from java.util import ArrayList, Locale
from java.lang import Class, Integer, Object
from java.lang.reflect import Array

from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity
from org.telegram.messenger import ApplicationLoader, SendMessagesHelper, AndroidUtilities, R, NotificationCenter
from org.telegram.tgnet.tl import TL_account
from org.telegram.ui.ActionBar import AlertDialog

from android_utils import log, run_on_ui_thread
from base_plugin import BasePlugin, HookResult, HookStrategy
from client_utils import get_send_messages_helper, get_last_fragment, get_account_instance, \
    send_request, get_user_config, get_messages_controller, send_message
from markdown_utils import parse_markdown
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from ui.settings import Header, Switch, Divider, Input, Selector, Text

__id__ = "rehuyandex"
__name__ = "reYandex"
__description__ = "Now play plugin for Yandex Music.\n\nUse:\n   .now — send now play card\n   .nowt — send now play track\n\nThanks @mipohbopohih & @exteradev for the help"
__icon__ = "remusic/0"
__version__ = "1.3.2"
__author__ = "@reNightly, @itsv1eds, @qmrrchh"
__min_version__ = "11.12.0"

API_BASE_URL = "https://api_1.mipoh.ru"
TEMP_DIR_NAME = "yandexmusic_temp"

FAMILY_PLUGIN_ID = "respotify_renightly"
FAMILY_ENABLED = False

REMOTE_CONFIG_URL = "https://raw.githubusercontent.com/cherryymerryy/respotify_remote_config/refs/heads/main/config.json"
REMOTE_CONFIG = {}

DEFAULT_STREAM_STRING = "🎵 {title} — {artists}"
DEFAULT_STREAM_TEXT = "Hi, I use exteraGram"
DEFAULT_TOKEN_VALUE = "your token"

FONTS = {
    0: "Onest",
    1: "Circular",
    2: "NotoSansJP"
}
DEFAULT_COLOR = {
    "background_color": "#000000",
    "title_text_color": "#FFFFFF",
    "subtext_color": "#A0A0A0"
}
DEFAULT_COMMANDS = {
    "pic": [".now", ". now"],
    "track": [".nowt", ". nowt"],
}
SPECIAL_COMMANDS = {
    "pic": [".ynow", ". ynow"],
    "track": [".ynowt", ". ynowt"],
}
INSTANT_SUBTEXT = "Powered by"
INSTANT_MAIN_TEXT = "reMusic"


class LanguageController:
    def __init__(self):
        self.lang_code = Locale.getDefault().getLanguage()

    def get_controller(self):
        if self.lang_code == "ru":
            return self.lang_ru()
        elif self.lang_code == "pt":
            return self.lang_ptbr()
        else:
            return self.lang_en()

    class lang_en:
        _lang = 'en'

        Family_Mode_Enabled = f"[{__name__}] reFamily mode enabled."
        Family_Mode_Disabled = f"[{__name__}] reFamily mode disabled."
        Family_Mode_Changed = f"[{__name__}] reFamily mode changed."
        Family_Mode_About = "About mode"

        SendCommand_NoToken = "Set your Yandex.Music token using guide from settings"
        SendCommand_NoToken_Button = "Authorize"
        SendCommand_NoAPI = "You enabled custom API, but not set him. Disable custom API or enter your API url."
        SendCommand_NoAPI_Button = "Set API"
        SendCommand_NoResources = f"{__name__} resources not found."
        SendCommand_NoResources_Button = f"Download"

        Settings_ZwyLibNeed = "To auto-update the plugin, it is recommended to install the auxiliary plugin ZwyLib. You can find it in the @ZwyPlugins channel."
        Settings_reFamily = "A neighboring plugin from the re[] family was detected. A prefix s (.ynow/.ynowt) was added to reMusic commands to avoid conflicts between plugins."

        Settings_AuthToken_Header = "Authorization"

        Settings_API_Header = "API"
        Settings_API_Text = "API url"
        Settings_API_Subtext = "You can set your own Yandex.Music API url."

        Settings_API_Switch_Text = "Custom API"
        Settings_API_Switch_Subtext = "You can deploy your own Yandex Music API using the guide."

        Settings_APIHost_Title = "Change Default API"
        Settings_APIHost = "You can download the source code of the Yandex.Music API by @mipohbopohih using the button below, deploy it on your own server, and enter the host in the field above."
        Settings_APIHost_Go = "Source code"

        Settings_LoginGuide_Text = "Help"
        Settings_LoginGuide_Title = "Authorization in reMusic"
        Settings_LoginGuide_Go = "guide"

        Settings_LoginGuide = "Go to token guide, log in to your Yandex account, and paste your token below."
        Settings_Username_Text = "Yandex.Music token"
        Settings_Username_Subtext = "Paste your token from login guide here."

        Settings_CardSettings_Header = "Customization"

        Settings_BackgroundMode_Text = "Background"
        Settings_BackgroundMode_Item_1 = "Track Cover"
        Settings_BackgroundMode_Item_2 = "Cover Color"

        Settings_Font_Text = "Font"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_YandexLink_Text = "Insert link to ..."
        Settings_YandexLink_Item_1 = "—"
        Settings_YandexLink_Item_2 = "Track"
        Settings_YandexLink_Item_3 = "Album"

        Settings_SongLinkInclude_Text = "Link to other platforms"
        Settings_SongLinkInclude_Subtext = "Adds a link to the song page on song.link"
        Settings_FastCardRender_Text = "Pre-render cards"
        Settings_FastCardRender_Subtext = "May increase battery consumption"

        Setting_AdvancedMode_Text = "Advanced Settings"
        Setting_AdvancedMode_Subtext = "Additional customization settings"
        Setting_AdvancedMode_Title = "Customization"

        Settings_BackgroundColor_Text = "Background Color"
        Settings_BackgroundColor_Subtext = "Background color if 'Cover Color' is selected in 'Background'"

        Settings_AccentColor_Text = "Accent Color"
        Settings_AccentColor_Subtext = "Text color used in track title and active progress bar"

        Settings_SecondaryColor_Text = "Secondary Color"
        Settings_SecondaryColor_Subtext = "Text color used for artist line, inactive progress bar, and timers"

        Settings_InstantCardSubtext_Text = "Secondary Text"
        Settings_InstantCardSubtext_Subtext = "Text displayed at the top of the bottom block of pre-rendered cards"

        Settings_InstantCardMainText_Text = "Main Text"
        Settings_InstantCardMainText_Subtext = "Text displayed at the bottom of the bottom block of pre-rendered cards"

        Settings_Stream_Header = "Profile Streaming"

        Setting_Stream_Title = "Stream settings"
        Settings_Stream_Text = "Stream track to profile"
        Settings_Stream_Subtext = "Updates your bio/location with the currently playing track"

        Settings_StreamAlert_Title = "⚠️⚠️WARNING⚠️⚠️"
        Settings_StreamAlert_Text = "This feature may work inconsistently due to Telegram's profile change limits. As a result, your profile information may not update immediately. Use at your own risk."

        Setting_TrackStream_Text = "Stream to..."
        Setting_TrackStream_Item1 = "Bio"
        Setting_TrackStream_Item2 = "Location (Recommended)"

        Settings_InStream_Text = "Default Text"
        Settings_InStream_Subtext = "Text displayed when the player is unavailable or no track is playing"

        Settings_FormatInStream_Text = "Format"
        Settings_FormatInStream_Subtext = "Customize track display. {title} — track name, {artists} — artist(s)"

        Settings_Other_Header = "Other"

        Setting_Other_SourceCheck = "File Integrity Check"
        Setting_Other_ForceDownload = "Download Full Resource Package"
        Setting_Other_Donate = "Support Development"

        Alert_HEX_Title = "HEX Error"
        Alert_HEX_Text = "Invalid HEX color code"

        Alert_SourceError_Title = "Integrity Check Error"
        Alert_SourceError_FontsNotFound = "Font files not found"
        Alert_SourceError_FontNotFound = "Font {0} not found"
        Alert_SourceError_FontApplyError = "Failed to apply font {0}: {1}"

        Alert_SourceSuccess_Title = "Success"
        Alert_SourceSuccess_Text = "No issues detected during resource check"

        Alert_SourceDownload_Title = "Downloading Resources"
        Alert_SourceCheck_Title = "Checking Resources"

        Alert_Donate_Title = "Support Development"
        Alert_Donate_Text = "Below you can copy the TON address of the reMusic developers and support the development with your donation."
        Alert_Donate_Button = "Copy"

        Alert_Trigger_Title = "⚠️ rePlugins Family Error"
        Alert_Trigger_Text = "Command {0} is ambiguous for reSpotify & reMusic plugins.\n\nTo get information from the service you need, use the commands:\n   .snow/.snowt - for reSpotify \n   .ynow/.ynowt - for reMusic"

        Card_PlayerInactive = "Player not started"
        Card_PlayingIn = "Playing on"

        Message_CaptionLink_Text = "[Yandex]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Yandex player unavailable or not started"

        Bulletin_NowPlay = "Now playing: {0}"

        Bulletin_ErrorMessage = "[reMusic] Error: {0}"
        Bulletin_ErrorMessageCopy = "[reMusic] An error occurred"
        Bulletin_FailedProcessImage = "[reMusic] Image processing error"
        Bulletin_FailedGetYouTubeLink = "[reMusic] Failed to get YouTube/SoundCloud link"
        Bulletin_ErrorSendingAudio = "[reMusic] Audio sending error"
        Bulletin_InvalidCobaltResponse = "Invalid response from Cobalt API"
        Bulletin_NoItemsToDownload = "No items to download"
        Bulletin_CobaltErrorCode = "[reMusic]: Cobalt Error Code — {0}"

    class lang_ru:
        _lang = 'ru'

        Family_Mode_Enabled = f"[{__name__}] Режим reFamily активирован."
        Family_Mode_Disabled = f"[{__name__}] Режим reFamily выключен."
        Family_Mode_Changed = f"[{__name__}] Режим reFamily изменён."
        Family_Mode_About = "О режиме"

        SendCommand_NoToken = "Установите свой токен Yandex.Music, используя инструкцию в настройках."
        SendCommand_NoToken_Button = "Авторизоваться"
        SendCommand_NoAPI = "Вы включили пользовательский API, но не указали его. Отключите пользовательский API или введите URL вашего API."
        SendCommand_NoAPI_Button = "Изменить"
        SendCommand_NoResources = f"Ресурсы {__name__} не найдены."
        SendCommand_NoResources_Button = "Скачать"

        Settings_ZwyLibNeed = "Для автоматического обновления плагина рекомендуется установить вспомогательный плагин ZwyLib. Вы можете найти его в канале @ZwyPlugins."
        Settings_reFamily = "Обнаружен соседний плагин из семейства re[]. К командам reMusic был добавлен префикс s (.ynow/.ynowt) для предотвращения конфликтов между плагинами."

        Settings_AuthToken_Header = "Авторизация"

        Settings_API_Header = "API"
        Settings_API_Text = "API хост"
        Settings_API_Subtext = "Вы можете установить своё собственную ссылку на API Яндекс.Музыки разработанное мипохом."

        Settings_API_Switch_Text = "Кастомное API"
        Settings_API_Switch_Subtext = "Вы можете развернуть своё API Яндекс.Музыки используя гайд."

        Settings_APIHost_Title = "Смена стандартного API"
        Settings_APIHost = "Вы можете скачать исходный код API Яндекс.Музыки от @mipohbopohih по кнопке ниже, развернуть его на своём сервере и указать хост в поле выше."
        Settings_APIHost_Go = "Исходный код"

        Settings_LoginGuide_Text = "Помощь"
        Settings_LoginGuide_Title = "Авторизация в reMusic"
        Settings_LoginGuide_Go = "инструкция"

        Settings_LoginGuide = "Перейдите к инструкции по получению токена, войдите в аккаунт Яндекса и вставьте токен ниже."
        Settings_Username_Text = "Токен Яндекс.Музыки"
        Settings_Username_Subtext = "Вставьте сюда токен из инструкции по авторизации."

        Settings_CardSettings_Header = "Внешний вид"

        Settings_BackgroundMode_Text = "Фон"
        Settings_BackgroundMode_Item_1 = "Обложка трека"
        Settings_BackgroundMode_Item_2 = "Цвет обложки"

        Settings_Font_Text = "Шрифт"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_YandexLink_Text = "Вставьте ссылку на ..."
        Settings_YandexLink_Item_1 = "—"
        Settings_YandexLink_Item_2 = "Трек"
        Settings_YandexLink_Item_3 = "Альбом"

        Settings_SongLinkInclude_Text = "Ссылка на другие платформы"
        Settings_SongLinkInclude_Subtext = "Добавляет ссылку на страницу песни на song.link"
        Settings_FastCardRender_Text = "Предзагрузка карточек"
        Settings_FastCardRender_Subtext = "Может увеличить расход батареи"

        Setting_AdvancedMode_Text = "Расширенные настройки"
        Setting_AdvancedMode_Subtext = "Дополнительные параметры кастомизации"
        Setting_AdvancedMode_Title = "Настройка внешнего вида"

        Settings_BackgroundColor_Text = "Цвет фона"
        Settings_BackgroundColor_Subtext = "Цвет фона при выборе 'Цвет обложки' в параметре 'Фон'"

        Settings_AccentColor_Text = "Акцентный цвет"
        Settings_AccentColor_Subtext = "Цвет текста в названии трека и активном индикаторе прогресса"

        Settings_SecondaryColor_Text = "Вторичный цвет"
        Settings_SecondaryColor_Subtext = "Цвет текста для строки с артистом, неактивного прогресс-бара и таймеров"

        Settings_InstantCardSubtext_Text = "Вторичный текст"
        Settings_InstantCardSubtext_Subtext = "Текст в верхней части нижнего блока предзагруженных карточек"

        Settings_InstantCardMainText_Text = "Основной текст"
        Settings_InstantCardMainText_Subtext = "Текст в нижней части нижнего блока предзагруженных карточек"

        Settings_Stream_Header = "Поток в профиль"

        Setting_Stream_Title = "Настройки стрима"
        Settings_Stream_Text = "Стрим трека в профиль"
        Settings_Stream_Subtext = "Обновляет био/локацию текущим треком"

        Settings_StreamAlert_Title = "⚠️⚠️ВНИМАНИЕ⚠️⚠️"
        Settings_StreamAlert_Text = "Эта функция может работать нестабильно из-за ограничений Telegram на частую смену профиля. Ваши данные могут обновляться с задержкой. Используйте на свой страх и риск."

        Setting_TrackStream_Text = "Стримить в..."
        Setting_TrackStream_Item1 = "Био"
        Setting_TrackStream_Item2 = "Локацию (рекомендуется)"

        Settings_InStream_Text = "Текст по умолчанию"
        Settings_InStream_Subtext = "Отображается, если плеер недоступен или ничего не воспроизводится"

        Settings_FormatInStream_Text = "Формат"
        Settings_FormatInStream_Subtext = "Формат отображения трека. {title} — название трека, {artists} — артист(ы)"

        Settings_Other_Header = "Прочее"

        Setting_Other_SourceCheck = "Проверка целостности файлов"
        Setting_Other_ForceDownload = "Скачать все ресурсы заново"
        Setting_Other_Donate = "Поддержать разработку"

        Alert_HEX_Title = "Ошибка HEX"
        Alert_HEX_Text = "Недопустимый HEX-код цвета"

        Alert_SourceError_Title = "Ошибка проверки"
        Alert_SourceError_FontsNotFound = "Файлы шрифтов не найдены"
        Alert_SourceError_FontNotFound = "Шрифт {0} не найден"
        Alert_SourceError_FontApplyError = "Не удалось применить шрифт {0}: {1}"

        Alert_SourceSuccess_Title = "Успешно"
        Alert_SourceSuccess_Text = "Ошибок при проверке ресурсов не найдено"

        Alert_SourceDownload_Title = "Скачивание ресурсов"
        Alert_SourceCheck_Title = "Проверка ресурсов"

        Alert_Donate_Title = "Поддержка разработки"
        Alert_Donate_Text = "Ниже вы можете скопировать TON-адрес разработчиков reMusic и поддержать проект донатом."
        Alert_Donate_Button = "Скопировать"

        Alert_Trigger_Title = "⚠️ rePlugins Family Error"
        Alert_Trigger_Text = "Команда {0} неоднозначна для плагинов reSpotify & reMusic.\n\nДля получения информации из необходимого Вам сервиса, используйте команды:\n   .snow/.snowt - для reSpotify \n   .ynow/.ynowt - для reMusic"

        Card_PlayerInactive = "Плеер не запущен"
        Card_PlayingIn = "Играет на"

        Message_CaptionLink_Text = "[Яндекс]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Плеер Яндекс.Музыки недоступен или не запущен"

        Bulletin_NowPlay = "Сейчас играет: {0}"

        Bulletin_ErrorMessage = "[reMusic] Ошибка: {0}"
        Bulletin_ErrorMessageCopy = "[reMusic] Произошла ошибка"
        Bulletin_FailedProcessImage = "[reMusic] Ошибка обработки изображения"
        Bulletin_FailedGetYouTubeLink = "[reMusic] Не удалось получить ссылку на YouTube/SoundCloud"
        Bulletin_ErrorSendingAudio = "[reMusic] Ошибка отправки аудио"
        Bulletin_InvalidCobaltResponse = "Некорректный ответ от Cobalt API"
        Bulletin_NoItemsToDownload = "Нет элементов для загрузки"
        Bulletin_CobaltErrorCode = "[reMusic]: Код ошибки Cobalt — {0}"

    class lang_ptbr:
        _lang = 'pt'

        Settings_ZwyLibNeed = "Para atualizar automaticamente o plugin, é recomendável instalar o plugin auxiliar ZwyLib. Você pode encontrá-lo no canal @ZwyPlugins."
        Settings_reFamily = "Foi detectado um plugin vizinho da família re[]. Um prefixo s (.ynow/.ynowt) foi adicionado aos comandos do reMusic para evitar conflitos entre plugins."

        Family_Mode_Enabled = f"[{__name__}] modo familiar ativado."
        Family_Mode_Disabled = f"[{__name__}] modo familiar desativado."
        Family_Mode_Changed = f"[{__name__}] modo familiar alterado."
        Family_Mode_About = "Sobre o modo familiar"

        SendCommand_NoToken = "Defina seu token do Yandex.Music usando o guia nas configurações."
        SendCommand_NoToken_Button = "Authorize"
        SendCommand_NoAPI = "Você ativou a API personalizada, mas não a configurou. Desative a API personalizada ou insira a URL da sua API."
        SendCommand_NoResources = f"Recursos de {__name__} não encontrados."
        SendCommand_NoResources_Button = f"Download"

        Settings_AuthToken_Header = "Autorização"

        Settings_API_Header = "API"
        Settings_API_Text = "API url"
        Settings_API_Subtext = "You can set your own Yandex.Music API url."

        Settings_API_Switch_Text = "API Personalizada"
        Settings_API_Switch_Subtext = "Você pode implantar sua própria API do Yandex Music usando o guia."

        Settings_APIHost_Title = "Alterar API Padrão"
        Settings_APIHost = "Você pode baixar o código-fonte da API do Yandex.Music criado por @mipohbopohih usando o botão abaixo, implantá-lo no seu próprio servidor e informar o host no campo acima."
        Settings_APIHost_Go = "Source code"

        Settings_LoginGuide_Text = "Ajuda"
        Settings_LoginGuide_Title = "Autorização no reMusic"
        Settings_LoginGuide_Go = "guia"

        Settings_LoginGuide = "Acesse o guia do token, faça login na sua conta Yandex e cole seu token abaixo."
        Settings_Username_Text = "Token do Yandex.Music"
        Settings_Username_Subtext = "Cole aqui seu token do guia de login."

        Settings_CardSettings_Header = "Personalização"

        Settings_BackgroundMode_Text = "Plano de fundo"
        Settings_BackgroundMode_Item_1 = "Capa da faixa"
        Settings_BackgroundMode_Item_2 = "Cor da capa"

        Settings_Font_Text = "Fonte"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_YandexLink_Text = "Inserir link para ..."
        Settings_YandexLink_Item_1 = "—"
        Settings_YandexLink_Item_2 = "Faixa"
        Settings_YandexLink_Item_3 = "Álbum"

        Settings_SongLinkInclude_Text = "Link para outras plataformas"
        Settings_SongLinkInclude_Subtext = "Adiciona um link da música para a página no song.link"
        Settings_FastCardRender_Text = "Pré-carregar cartões"
        Settings_FastCardRender_Subtext = "Pode aumentar o consumo de bateria"

        Setting_AdvancedMode_Text = "Modo avançado"
        Setting_AdvancedMode_Subtext = "Configurações adicionais de personalização"
        Setting_AdvancedMode_Title = "Personalização"

        Settings_BackgroundColor_Text = "Cor de fundo"
        Settings_BackgroundColor_Subtext = "Cor de fundo usada quando 'Cor da capa' está selecionada em 'Plano de fundo'"

        Settings_AccentColor_Text = "Cor de destaque"
        Settings_AccentColor_Subtext = "Cor do texto no título da faixa e na barra de progresso ativa"

        Settings_SecondaryColor_Text = "Cor secundária"
        Settings_SecondaryColor_Subtext = "Cor do texto para o artista, barra de progresso inativa e cronômetros"

        Settings_InstantCardSubtext_Text = "Texto secundário"
        Settings_InstantCardSubtext_Subtext = "Texto exibido no topo do bloco inferior dos cartões pré-carregados"

        Settings_InstantCardMainText_Text = "Texto principal"
        Settings_InstantCardMainText_Subtext = "Texto exibido na parte inferior do bloco inferior dos cartões pré-carregados"

        Settings_Stream_Header = "Streaming no perfil"

        Setting_Stream_Title = "Configurações de stream"
        Settings_Stream_Text = "Transmitir faixa no perfil"
        Settings_Stream_Subtext = "Atualiza sua biografia/localização com a faixa atual"

        Settings_StreamAlert_Title = "⚠️⚠️AVISO⚠️⚠️"
        Settings_StreamAlert_Text = "Esta função pode funcionar de forma inconsistente devido às limitações do Telegram para alterar informações de perfil. Sua biografia pode não ser atualizada imediatamente. Use por sua conta e risco."

        Setting_TrackStream_Text = "Transmitir para..."
        Setting_TrackStream_Item1 = "Biografia"
        Setting_TrackStream_Item2 = "Localização (Recomendado)"

        Settings_InStream_Text = "Texto padrão"
        Settings_InStream_Subtext = "Texto exibido quando o player está indisponível ou nada está sendo reproduzido"

        Settings_FormatInStream_Text = "Formato"
        Settings_FormatInStream_Subtext = "Personalize como a faixa será exibida. {title} — nome da faixa, {artists} — artista(s)"

        Settings_Other_Header = "Outros"

        Setting_Other_SourceCheck = "Verificar integridade dos arquivos"
        Setting_Other_ForceDownload = "Baixar todos os recursos novamente"
        Setting_Other_Donate = "Apoiar o desenvolvimento"

        Alert_HEX_Title = "Erro HEX"
        Alert_HEX_Text = "Código de cor HEX inválido"

        Alert_SourceError_Title = "Erro de verificação"
        Alert_SourceError_FontsNotFound = "Arquivos de fontes não encontrados"
        Alert_SourceError_FontNotFound = "Fonte {0} não encontrada"
        Alert_SourceError_FontApplyError = "Erro ao aplicar fonte {0}: {1}"

        Alert_SourceSuccess_Title = "Sucesso"
        Alert_SourceSuccess_Text = "Nenhum problema encontrado durante a verificação dos recursos"

        Alert_SourceDownload_Title = "Baixando recursos"
        Alert_SourceCheck_Title = "Verificando recursos"

        Alert_Donate_Title = "Apoiar o desenvolvimento"
        Alert_Donate_Text = "Abaixo você pode copiar o endereço TON dos desenvolvedores do reMusic e apoiar o projeto com sua doação."
        Alert_Donate_Button = "Copiar"

        Alert_Trigger_Title = "⚠️ Erro da Família rePlugins"
        Alert_Trigger_Text = "O comando {0} é ambíguo entre os plugins reSpotify e reMusic.\n\nPara obter informações do serviço desejado, use os comandos:\n   .snow/.snowt - para o reSpotify \n   .ynow/.ynowt - para o reMusic"

        Card_PlayerInactive = "Player não iniciado"
        Card_PlayingIn = "Tocando em"

        Message_CaptionLink_Text = "[Yandex]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Player do Yandex indisponível ou não iniciado"

        Bulletin_NowPlay = "Tocando agora: {0}"

        Bulletin_ErrorMessage = "[reMusic] Erro: {0}"
        Bulletin_ErrorMessageCopy = "[reMusic] Ocorreu um erro"
        Bulletin_FailedProcessImage = "[reMusic] Erro ao processar a imagem"
        Bulletin_FailedGetYouTubeLink = "[reMusic] Falha ao obter link do YouTube/SoundCloud"
        Bulletin_ErrorSendingAudio = "[reMusic] Erro ao enviar áudio"
        Bulletin_InvalidCobaltResponse = "Resposta inválida da API Cobalt"
        Bulletin_NoItemsToDownload = "Nenhum item para baixar"
        Bulletin_CobaltErrorCode = "[reMusic]: Código de erro Cobalt — {0}"


string = LanguageController().get_controller()


class Track:
    def __init__(
            self,
            active: bool,
            id: int = None,
            title: str = None,
            artist: list = None,
            album: str = None,
            thumb: str = None,
            duration: int = None,
            progress: int = None,
            link: str = None,
            download_url: str = None,
    ):
        self.active = active
        self.id = id
        self.title = title
        self.artist = artist
        self.album = album
        self.thumb = thumb
        self.duration = duration
        self.progress = progress
        self.link = link
        self.download_url = download_url


class YandexMusic:
    def __init__(self, token):
        self.token = token.strip()
        self.headers = {"User-Agent": "Mozilla/5.0", "Accept": "application/json", "ya-token": self.token}
        self.now_track = Track(active=False)
        self.memory_id = "Default"
        self.polling = False
        threading.Thread(target=self.now(), args=(True,), daemon=True).start()

    def get_current_track(self):
        if not self.token:
            return Track(active=False)
        try:
            r = requests.get(f"{API_BASE_URL}/get_current_track_beta", headers=self.headers, timeout=10, verify=False)
            log(f"[reMusic] get_current_track status={r.status_code}, body={r.text[:200]}")
            data = r.json()
            log(f"[reMusic] get_current_track data keys={list(data.keys())}")
            if r.status_code != 200 or 'track' not in data:
                log("[reMusic] no 'track' key or bad status")
                return Track(active=False)
            t = data['track']
            log(f"[reMusic] raw track: {t}")
            tid = t.get('track_id')
            # Build Track from beta response 't'
            raw_artist = t.get('artist', '')
            if isinstance(raw_artist, str):
                artists = [x.strip() for x in raw_artist.split(',') if x.strip()]
            elif isinstance(raw_artist, list):
                artists = raw_artist
            else:
                artists = []
            album = t.get('album')
            thumb = t.get('img')
            duration = int(t.get('duration', 0))
            progress_ms_raw = data.get('progress_ms', 0)
            try:
                progress_ms = int(progress_ms_raw)
            except:
                progress_ms = 0
            # Convert milliseconds to seconds
            progress = progress_ms // 1000
            track = Track(
                active=True,
                id=tid,
                title=t.get('title'),
                artist=artists,
                album=album,
                thumb=thumb,
                duration=duration,
                progress=progress,
                link=f"https://music.yandex.ru/track/{tid}",
                download_url=t.get('download_link')
            )
            log(f"[reMusic] built track: {track.title} — {track.artist}, album={track.album}, duration={track.duration}, progress={track.progress}")
            return track
        except Exception as e:
            log(f"[reMusic] get_current_track exception: {e}")
            return Track(active=False)

    def now(self, force_request=False):
        while any([self.polling, force_request]):
            try:
                data = self.get_current_track()
                self.now_track = data

                if not data.active:
                    log("[reMusic] data not active")
                    time.sleep(2.5)
            except Exception as e:
                try:
                    self.now_track.active
                except:
                    self.now_track = Track(active=False)
                if "retries exceeded" not in str(e):
                    log(f"[exteraGram reMusic]: Internal Error {e}")
            if force_request:
                return self.now_track
            time.sleep(2.5)
        return None

    def start_polling(self):
        if "MusicNowTrackTracker" not in [thread.name for thread in threading.enumerate()]:
            threading.Thread(target=self.now, name="MusicNowTrackTracker").start()

    def disable_polling(self):
        self.polling = False


def show_with_copy(message, sub_message):
    def copy():
        if AndroidUtilities.addToClipboard(sub_message):
            BulletinHelper.show_copied_to_clipboard()

    BulletinHelper.show_with_button(message, R.raw.error, "Copy", lambda: copy())


class ReMusicPlugin(BasePlugin):
    class Progress:
        def __init__(self):
            fragment = get_last_fragment()
            ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
            builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_SPINNER)
            self.progress_dialog = builder

        def show(self):
            if self.progress_dialog is None:
                self.__init__()
            self.progress_dialog.show()

        def dismiss(self):
            if self.progress_dialog is not None:
                try:
                    dlg = self.progress_dialog.getDialog() if hasattr(self.progress_dialog,
                                                                      'getDialog') else self.progress_dialog
                    if dlg:
                        dlg.dismiss()
                except Exception as e:
                    log(f"[reSpotify] progress dialog error: {e}")
                finally:
                    self.progress_dialog = None

    class NotificationsHook:
        def __init__(self, plugin):
            self.plugin = plugin

        def after_hooked_method(self, param):
            update_id: int = param.args[0]
            if update_id == NotificationCenter.pluginsUpdated:
                plugin_id: str = param.args[1][0]
                log(f"[{__name__}] Hooked 'pluginsUpdated' notification for '{plugin_id}'")

                if plugin_id == FAMILY_PLUGIN_ID:
                    self.plugin.set_family_mode_enabled()
                else:
                    log(f"[{__name__}] Notification 'pluginsUpdated' for '{plugin_id}' ignored")

    def __init__(self):
        super().__init__()
        self.yandex: YandexMusic = None
        self.progress = None
        self.for_metadata = None
        self._temp_dir = None
        self.alert_builder_instance: AlertDialogBuilder = None

    def on_plugin_load(self):
        global FAMILY_ENABLED
        self._temp_dir = self._get_temp_dir()
        if self._temp_dir:
            log("reMusic plugin loaded successfully")
        else:
            log("Failed to initialize temp directory for reMusic")

        self.progress = self.Progress()
        self.hook_plugin_update()

        threading.Thread(target=self.read_remote_config, daemon=True).start()

        self.yandex = YandexMusic(self.get_setting('yandex_api_token', DEFAULT_TOKEN_VALUE))
        FAMILY_ENABLED = self.search_family_plugin(FAMILY_PLUGIN_ID)
        log(f"[{__name__}] reFamily on_plugin_load: {FAMILY_ENABLED}")
        self.autocard_render(self.get_setting('fast_card_render', False))
        self.add_on_send_message_hook(priority=42)
        self.yandex.polling = self.get_setting("update_bio", False) or self.get_setting("fast_card_render", False)
        self.yandex.start_polling()
        threading.Thread(target=self._streamer, daemon=True).start()

    def on_plugin_unload(self):
        self._unactive()
        self.yandex.disable_polling()
        self.set_default_stream_text(True)
        self.yandex = None

    def create_settings(self):
        is_token_valid = self.get_setting("yandex_api_token", DEFAULT_TOKEN_VALUE) not in [None, "", DEFAULT_TOKEN_VALUE]
        settings = [
            Header(text=string.Settings_AuthToken_Header),
            Input(
                key="yandex_api_token",
                text=string.Settings_Username_Text,
                default=DEFAULT_TOKEN_VALUE,
                subtext=string.Settings_Username_Subtext,
                on_change=lambda new_value: self.update_yandex_object(new_value),
                icon="msg_pin_code"
            ),
            Text(
                text=string.Settings_LoginGuide_Text,
                icon="msg_info",
                on_click=lambda view: self.show_my_info_alert(
                    title=string.Settings_LoginGuide_Title,
                    message=string.Settings_LoginGuide,
                    positive_button="OK",
                    neutral_button=string.Settings_LoginGuide_Go,
                    neutral_link="https://yandex-music.readthedocs.io/en/main/token.html",
                    neutral_type="link"
                )
            )
        ]

        if not is_token_valid:
            return settings

        advanced = self.get_setting('advanced_mode', False)
        update_bio = self.get_setting("update_bio", False)
        custom_api = self.get_setting("custom_yandex_api_url", False)

        settings += [
            Divider(
                text=string.Settings_reFamily
            ) if FAMILY_ENABLED else None,

            Header(text=string.Settings_API_Header),
            Switch(
                key="custom_yandex_api_url",
                text=string.Settings_API_Switch_Text,
                default=False,
                subtext=string.Settings_API_Switch_Subtext,
                icon="msg_language"
            ),
            Input(
                key="yandex_api_url",
                text=string.Settings_API_Text,
                default=API_BASE_URL,
                subtext=string.Settings_API_Subtext,
                on_change=lambda new_value: self.update_yandex_api(new_value),
                icon="msg_instant_link"
            ) if custom_api else None,
            Text(
                text=string.Settings_LoginGuide_Text,
                icon="msg_info",
                on_click=lambda view: self.show_my_info_alert(
                    title=string.Settings_APIHost_Title,
                    message=string.Settings_APIHost,
                    positive_button="OK",
                    neutral_button=string.Settings_APIHost_Go,
                    neutral_link="https://github.com/MIPOHBOPOHIH/YMMBFA",
                    neutral_type="link"
                )
            ) if custom_api else None,

            Divider(),

            Header(text=string.Settings_CardSettings_Header),
            Selector(
                key="background",
                text=string.Settings_BackgroundMode_Text,
                default=1,
                items=[
                    string.Settings_BackgroundMode_Item_1,
                    string.Settings_BackgroundMode_Item_2
                ],
                icon="msg_photos"
            ),
            Selector(
                key="font",
                text=string.Settings_Font_Text,
                default=0,
                items=[
                    string.Settings_Font_Item1,
                    string.Settings_Font_Item2,
                    string.Settings_Font_Item3
                ],
                icon="msg_photo_text_regular"
            ),
            Selector(
                key="yandex_link",
                text=string.Settings_YandexLink_Text,
                default=1,
                items=[
                    string.Settings_YandexLink_Item_1,
                    string.Settings_YandexLink_Item_2,
                    string.Settings_YandexLink_Item_3
                ],
                icon="msg_link2"
            ),
            Switch(
                key="songlink_link_include",
                text=string.Settings_SongLinkInclude_Text,
                default=True,
                subtext=string.Settings_SongLinkInclude_Subtext,
                on_change=lambda new_value: self.LogCBO(),
                icon="msg_language"
            ),
            Switch(
                key="fast_card_render",
                text=string.Settings_FastCardRender_Text,
                default=False,
                subtext=string.Settings_FastCardRender_Subtext,
                on_change=lambda new_value: self.autocard_render(new_value),
                icon="boosts_solar"
            ),

            Divider(),

            Switch(
                key="advanced_mode",
                text=string.Setting_AdvancedMode_Text,
                default=False,
                subtext=string.Setting_AdvancedMode_Subtext,
                icon="msg_palette"
            ),
            Text(
                text=string.Setting_AdvancedMode_Title,
                icon="msg_download_settings",
                create_sub_fragment=self.create_customization_settings
            ) if advanced else None,

            Divider(),

            Header(text=string.Settings_Stream_Header),
            Switch(
                key="update_bio",
                text=string.Settings_Stream_Text,
                default=False,
                subtext=string.Settings_Stream_Subtext,
                on_change=lambda new_value: self._unactive(new_value),
                icon="msg_online"
            ),
            Text(
                text=string.Setting_Stream_Title,
                icon="msg_download_settings",
                create_sub_fragment=self.create_stream_settings
            ) if update_bio else None,

            Header(text=string.Settings_Other_Header),
            Text(
                text=string.Setting_Other_SourceCheck,
                icon="msg_noise_on",
                on_click=lambda view: self.source_check()
            ),
            Text(
                text=string.Setting_Other_ForceDownload,
                icon="msg_download",
                on_click=lambda view: self.force_source_downloader()
            ),
            Text(
                text=string.Setting_Other_Donate,
                icon="msg_ton",
                accent=True,
                on_click=lambda view: self.show_my_info_alert(
                    title=string.Alert_Donate_Title,
                    message=string.Alert_Donate_Text,
                    neutral_button=string.Alert_Donate_Button,
                    neutral_link="UQBVxjueXqAEpALX_b0yr-ytXN26LOTpSBn26b9VRHKrmm5F",
                    neutral_type="copy"
                )
            )
        ]
        return settings

    def create_customization_settings(self):
        instant_card = self.get_setting("fast_card_render", False)
        return [
            Input(
                key="background_color",
                text=string.Settings_BackgroundColor_Text,
                default=DEFAULT_COLOR["background_color"],
                subtext=string.Settings_BackgroundColor_Subtext,
                icon="menu_feature_custombg",
                on_change=lambda new_value: self.HEX_check(new_value, "background_color")
            ) if self.get_setting("background", 1) == 1 else None,
            Input(
                key="title_text_color",
                text=string.Settings_AccentColor_Text,
                default=DEFAULT_COLOR["title_text_color"],
                subtext=string.Settings_AccentColor_Subtext,
                icon="msg_photo_text_framed",
                on_change=lambda new_value: self.HEX_check(new_value, "title_text_color")
            ),
            Input(
                key="subtext_color",
                text=string.Settings_SecondaryColor_Text,
                default=DEFAULT_COLOR["subtext_color"],
                subtext=string.Settings_SecondaryColor_Subtext,
                icon="msg_photo_text_framed2",
                on_change=lambda new_value: self.HEX_check(new_value, "subtext_color")
            ),
            Input(
                key="instant_subtext",
                text=string.Settings_InstantCardSubtext_Text,
                default=INSTANT_SUBTEXT,
                subtext=string.Settings_InstantCardSubtext_Subtext,
                icon="menu_feature_intro"
            ) if instant_card else None,
            Input(
                key="instant_main_text",
                text=string.Settings_InstantCardMainText_Text,
                default=INSTANT_MAIN_TEXT,
                subtext=string.Settings_InstantCardMainText_Subtext,
                icon="menu_feature_cover"
            ) if instant_card else None,
        ]

    def create_stream_settings(self):
        update_bio = self.get_setting("update_bio", False)
        return [
            Selector(
                key="stream_place",
                text=string.Setting_TrackStream_Text,
                default=0,
                items=[
                    string.Setting_TrackStream_Item1,
                    string.Setting_TrackStream_Item2,
                ],
                icon="menu_premium_location" if self.get_setting("stream_place", 0) else "msg_openprofile"
            ) if get_user_config().isPremium() and update_bio else None,
            Input(
                key="default_stream_text",
                text=string.Settings_InStream_Text,
                default=DEFAULT_STREAM_TEXT,
                subtext=string.Settings_InStream_Subtext,
                icon="msg_photo_text_framed3"
            ) if update_bio else None,
            Input(
                key="track_display_format",
                text=string.Settings_FormatInStream_Text,
                default=DEFAULT_STREAM_STRING,
                subtext=string.Settings_FormatInStream_Subtext,
                icon="msg_view_file"
            ) if update_bio else None,
        ]

    def on_send_message_hook(self, account, params):
        commands = SPECIAL_COMMANDS if FAMILY_ENABLED else DEFAULT_COMMANDS
        if hasattr(params, 'message') and isinstance(params.message, str):
            if params.message in commands["pic"]:
                token = self.get_setting("yandex_api_token", DEFAULT_TOKEN_VALUE)
                is_custom_api = self.get_setting("custom_yandex_api_url", False)
                api_url = self.get_setting("yandex_api_url", API_BASE_URL)

                if token is None or token == "" or token == DEFAULT_TOKEN_VALUE:
                    BulletinHelper.show_with_button(
                        string.SendCommand_NoToken,
                        R.raw.error,
                        string.SendCommand_NoToken_Button,
                        on_click=lambda: self._open_plugin_settings()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                if is_custom_api and (api_url is None or api_url == ""):
                    BulletinHelper.show_with_button(
                        string.SendCommand_NoAPI,
                        R.raw.error,
                        string.SendCommand_NoAPI_Button,
                        on_click=lambda: self._open_plugin_settings()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                if not self.source_checker():
                    BulletinHelper.show_with_button(
                        string.SendCommand_NoResources,
                        R.raw.error,
                        string.SendCommand_NoResources_Button,
                        on_click=lambda: self.force_source_downloader()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                run_on_ui_thread(self.progress.show())
                threading.Thread(target=self.image_processor, args=(params,)).start()
                return HookResult(strategy=HookStrategy.CANCEL)

            if params.message in commands["track"]:
                token = self.get_setting("yandex_api_token", DEFAULT_TOKEN_VALUE)
                is_custom_api = self.get_setting("custom_yandex_api_url", False)
                api_url = self.get_setting("yandex_api_url", API_BASE_URL)

                if token is None or token == "" or token == DEFAULT_TOKEN_VALUE:
                    BulletinHelper.show_with_button(
                        string.SendCommand_NoToken,
                        R.raw.error,
                        string.SendCommand_NoToken_Button,
                        on_click=lambda: self._open_plugin_settings()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                if is_custom_api and (api_url is None or api_url == ""):
                    BulletinHelper.show_with_button(
                        string.SendCommand_NoAPI,
                        R.raw.error,
                        string.SendCommand_NoAPI_Button,
                        on_click=lambda: self._open_plugin_settings()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                if not self.source_checker():
                    BulletinHelper.show_with_button(
                        string.SendCommand_NoResources,
                        R.raw.error,
                        string.SendCommand_NoResources_Button,
                        on_click=lambda: self.force_source_downloader()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                run_on_ui_thread(self.progress.show())
                try:
                    thread = threading.Thread(target=self._process_audio, args=(params.peer, params.replyToMsg, params.replyToTopMsg), daemon=True)
                    thread.start()
                    return HookResult(strategy=HookStrategy.CANCEL)
                except Exception as e:
                    run_on_ui_thread(self.progress.dismiss())
                    params.message = string.Bulletin_ErrorMessage.format(e)
                    log(f"Internal reMusic error: {e}")
                    show_with_copy(string.Bulletin_ErrorMessageCopy, e)
                    return None

            if params.message in DEFAULT_COMMANDS["pic"] + DEFAULT_COMMANDS["track"] and params.message not in commands[
                "pic"] + commands["track"]:
                self.show_my_info_alert(
                    title=string.Alert_Trigger_Title,
                    message=string.Alert_Trigger_Text.format(params.message)
                )

                return HookResult(strategy=HookStrategy.CANCEL)
            return None

        elif hasattr(params, 'caption') and isinstance(params.caption, str):

            if "reMusic_flag_metadata" in params.caption:

                if self.for_metadata:
                    track = self.for_metadata
                    self.for_metadata = None
                else:
                    track = self.yandex.now_track

                for i in range(params.document.attributes.__str__().split(",").__len__()):
                    if "title" in dir(params.document.attributes.get(i)):
                        params.document.attributes.get(i).title = track.title if track.active else "[reMusic] ERROR"
                        params.document.attributes.get(i).performer = ", ".join(
                            track.artist) if track.active else string.Message_PlayerNotActive
                        params.document.attributes.get(i).duration = track.duration if track.active else 0
                markdown = "reMusic_flag_markdown" in params.caption
                if "reMusic_flag_metadata" in params.caption:
                    params.caption = params.caption.replace(" reMusic_flag_metadata", "")
                if markdown:
                    params.caption = params.caption.replace(" reMusic_flag_markdown", "")
                    caption = parse_markdown(params.caption)
                    params.caption = caption.text
                    params.entities = ArrayList()
                    for i in caption.entities:
                        params.entities.add(i.to_tlrpc_object())
                return HookResult(HookStrategy.MODIFY, params=params)
            return HookResult()
        return None

    def hook_plugin_update(self):
        try:
            clazz = jclass("org.telegram.messenger.NotificationCenter").getClass()

            if clazz is None:
                log(f"[{__name__}] Clazz not found")
                return

            post_notification_name = clazz.getDeclaredMethod("postNotificationName", Integer.TYPE, Array.newInstance(Object, 0).getClass())
            post_notification_name.setAccessible(True)
            self.hook_method(post_notification_name, self.NotificationsHook(self))

            log(f"[{__name__}] Hook method {clazz} found")
        except Exception as e:
            log(f"[{__name__}] Hook method error: {e}")

    def read_remote_config(self):
        global REMOTE_CONFIG, API_BASE_URL
        try:
            req = requests.get(REMOTE_CONFIG_URL)
            if req.status_code == 200:
                REMOTE_CONFIG = req.json()
                log("[reMusic] Remote config received.")

                API_BASE_URL = REMOTE_CONFIG["remusic_default_api"]
            else:
                log("[reMusic] Remote config not received.")
        except Exception as e:
            log(f"[reMusic] Failed to receive remote config: {e}")

    def copy(self, value):
        if AndroidUtilities.addToClipboard(value):
            BulletinHelper.show_copied_to_clipboard()

    def show_my_info_alert(self, title="TITLE", message="MESSAGE", positive_button="OK", neutral_button=None,
                           neutral_link=None, neutral_type=None):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button(positive_button, self._dismiss_dialog(self.alert_builder_instance))
        if neutral_button:
            builder.set_neutral_button(neutral_button, lambda builder, which: self._open_link(
                neutral_link) if neutral_type == "link" else self.copy(neutral_link))
        self.alert_builder_instance = builder.show()

    def show_my_loading_alert(self, title="Дайте денег пожалуйста"):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialog(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
        builder.setTitle(title)
        builder.setCancelable(False)
        builder.setCanceledOnTouchOutside(False)
        self.alert_builder_instance = builder
        self.alert_builder_instance.show()
        self.alert_builder_instance.setProgress(0)

    def _update_dialog_progress(self, builder_instance: AlertDialogBuilder, progress: int):
        if builder_instance and builder_instance.isShowing():
            builder_instance.setProgress(progress)

    def _dismiss_dialog(self, builder_instance: AlertDialogBuilder):
        def action():
            if builder_instance is not None:
                try:
                    dlg = builder_instance.getDialog() if hasattr(builder_instance, 'getDialog') else builder_instance
                    if dlg and dlg.isShowing():
                        dlg.dismiss()
                except Exception:
                    pass
                finally:
                    self.alert_builder_instance = None

        run_on_ui_thread(action)

    def _open_plugin_settings(self):
        try:
            java_plugin = PluginsController.getInstance().plugins.get(self.id)
            if java_plugin:
                run_on_ui_thread(lambda: get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin)))
        except Exception as e:
            log(f"[exteraGram reMusic] Error opening plugin settings: {e}")

    def _open_link(self, url):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        run_on_ui_thread(lambda: ctx.startActivity(intent))

    def LogCBO(self):
        log("[reMusic] Test CBO log")

    def HEX_check(self, new, variable):
        if not re.match("^#[A-Fa-f0-9]{6}$", new):
            run_on_ui_thread(lambda: self.show_my_info_alert(
                title=string.Alert_HEX_Title,
                message=string.Alert_HEX_Text
            ))
            self.set_setting(variable, DEFAULT_COLOR[variable])

    def update_yandex_object(self, token):
        self.yandex = YandexMusic(token)

    def update_yandex_api(self, url):
        global API_BASE_URL
        API_BASE_URL = url

    def source_check(self):
        self.show_my_loading_alert(
            title=string.Alert_SourceCheck_Title
        )
        plugin_dir = [file.getName() for file in self._temp_dir.listFiles()]
        if len(plugin_dir) == 0:
            self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                    message=string.Alert_SourceError_FontsNotFound)
            return
        for font in FONTS.values():
            for font_type in ["Regular", "Bold"]:
                nigger = 100 // (len(FONTS.values()) * 2)
                if f"{font}-{font_type}.ttf" not in plugin_dir:
                    self._dismiss_dialog(self.alert_builder_instance)
                    self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                            message=string.Alert_SourceError_FontNotFound.format(
                                                f"{font}-{font_type}.ttf"))
                    return
                try:
                    test_img = Image.new("RGBA", (100, 100), (0, 0, 0, 0))
                    img_font = ImageFont.truetype(File(self._temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), 42)
                    draw = ImageDraw.Draw(test_img)
                    draw.text((0, 0), "reMusic", font=img_font, fill=(255, 255, 255))
                    test_img = None
                except Exception as e:
                    self._dismiss_dialog(self.alert_builder_instance)
                    self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                            message=string.Alert_SourceError_FontApplyError.format(
                                                f"{font}-{font_type}.ttf", e))
                    return
                niggers_counter = nigger + (nigger * (list(FONTS.values()).index(font))) * 2 + (
                    nigger if font_type == "Bold" else 0)
                run_on_ui_thread(lambda: self._update_dialog_progress(self.alert_builder_instance, niggers_counter))
        self.show_my_info_alert(title=string.Alert_SourceSuccess_Title, message=string.Alert_SourceSuccess_Text)

    def source_checker(self) -> bool:
        plugin_dir = [file.getName() for file in self._temp_dir.listFiles()]
        if len(plugin_dir) == 0:
            return False
        for font in FONTS.values():
            for font_type in ["Regular", "Bold"]:
                if f"{font}-{font_type}.ttf" not in plugin_dir:
                    return False
                try:
                    test_img = Image.new("RGBA", (100, 100), (0, 0, 0, 0))
                    img_font = ImageFont.truetype(File(self._temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), 42)
                    draw = ImageDraw.Draw(test_img)
                    draw.text((0, 0), "reMusic", font=img_font, fill=(255, 255, 255))
                except Exception as e:
                    log(f"[{__name__}] {str(e)}")
                    return False
        return True

    def force_source_downloader(self):
        threading.Thread(target=self._source_downloader, args=(self._temp_dir, True,)).start()

    def _unactive(self, value=None):
        setting_value = False

        if value:
            run_on_ui_thread(lambda: self.show_my_info_alert(
                title=string.Settings_StreamAlert_Title,
                message=string.Settings_StreamAlert_Text,
            ))
            setting_value = value
            self.yandex.polling = value
            self.yandex.start_polling()
        elif value is None:
            setting_value = self.get_setting("update_bio", False)
        else:
            self.yandex.polling = self.get_setting("fast_card_render")

        if setting_value:
            self.set_default_stream_text(False)

    def set_default_stream_text(self, force: bool):
        user_full = get_messages_controller().getUserFull(get_user_config().getClientUserId())
        stream_place = self.get_setting("stream_place", 0)
        default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)

        if default_bio == DEFAULT_STREAM_TEXT:
            user_bio = user_full.about
            if user_bio:
                default_bio = user_bio
                self.set_setting("default_stream_text", default_bio)

        if force:
            if stream_place == 0:
                max_len = 140 if get_user_config().isPremium() else 70
                req = TL_account.updateProfile()
                req.flags = 4
                req.about = default_bio[:max_len]
                send_request(req, ())
                return
            elif stream_place == 1:
                if user_full.business_location is not None and user_full.business_location.address is not None:
                    req = TL_account.updateBusinessLocation()
                    req.address = default_bio[:96]
                    req.flags = 1
                    send_request(req, ())
                    return
            else:
                log(f"[{__name__}] Stream place out of range ({stream_place})")

        if stream_place == 0:
            max_len = 140 if get_user_config().isPremium() else 70
            if user_full.about != default_bio[:max_len]:
                req = TL_account.updateProfile()
                req.flags = 4
                req.about = default_bio[:max_len]

                send_request(req, ())
        elif stream_place == 1:
            if user_full.business_location is not None and user_full.business_location.address is not None:
                if user_full.business_location.address != default_bio[:96]:
                    req = TL_account.updateBusinessLocation()
                    req.address = default_bio[:96]
                    req.flags = 1

                    send_request(req, ())

    def autocard_render(self, value):
        if value:
            threading.Thread(target=self._autocard_render).start()
            log([thread.name for thread in threading.enumerate()])
            self.yandex.polling = True
            self.yandex.start_polling()
        else:
            self.yandex.polling = self.get_setting("update_bio", False)

    def autocard_render_after_change(self):
        if not self.get_setting("fast_card_render", False): return
        self.yandex.memory_id = "Default"
        threading.Thread(target=self._autocard_render).start()

    def _autocard_render(self):
        while True:
            if self.yandex.memory_id != self.yandex.now_track.id:
                try:
                    self.yandex.memory_id = self.yandex.now_track.id
                    self._make_card()
                except Exception as e:
                    show_with_copy("An error has occurred", e)
            time.sleep(1)

    def _streamer(self):
        while self.get_setting("update_bio", False):
            try:
                if self.yandex is None:
                    break

                if "now_track" not in dir(self.yandex):
                    time.sleep(1)

                user_full = get_messages_controller().getUserFull(get_user_config().getClientUserId())
                if not user_full: continue

                track = self.yandex.now_track
                stream_place = self.get_setting("stream_place", 0)
                max_len = 140 if get_user_config().isPremium() else 70

                if track.active:
                    new_about_text = self.get_setting("track_display_format", DEFAULT_STREAM_STRING)
                    new_about_text = new_about_text.replace("{title}", track.title)
                    new_about_text = new_about_text.replace("{artists}", ", ".join(track.artist))
                    if stream_place == 0:
                        if user_full.about != new_about_text[:max_len]:
                            try:
                                req = TL_account.updateProfile()
                                req.flags = 4
                                req.about = new_about_text[:max_len]
                                send_request(req, ())
                            except:
                                time.sleep(5)
                    elif stream_place == 1:
                        if user_full.business_location.address != new_about_text[:96]:
                            try:
                                req = TL_account.updateBusinessLocation()
                                req.address = new_about_text[:96]
                                req.flags = 1
                                send_request(req, ())
                            except:
                                time.sleep(5)
                    else:
                        pass
                else:
                    default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)

                    if stream_place == 0:
                        if user_full.about != default_bio[:max_len]:
                            try:
                                req = TL_account.updateProfile()
                                req.flags = 4
                                req.about = default_bio[:max_len]
                                send_request(req, ())
                            except:
                                time.sleep(5)
                    elif stream_place == 1:
                        if user_full.business_location.address != default_bio[:96]:
                            try:
                                req = TL_account.updateBusinessLocation()
                                req.address = default_bio[:96]
                                req.flags = 1
                                send_request(req, ())
                            except:
                                time.sleep(5)

                time.sleep(5 if stream_place else 30)
            except Exception as e:
                log(f"reMusic Bio Error: {e}")
                time.sleep(10)

    def _source_downloader(self, temp_dir, force_load=False):

        try:
            plugin_dir = [file.getName() for file in temp_dir.listFiles()]
            not_need_download = any([font.endswith(".ttf") for font in plugin_dir])
            if not_need_download and not force_load:
                return
            log("[reMusic] Downloading fonts...")
            run_on_ui_thread(lambda: self.show_my_loading_alert(
                string.Alert_SourceDownload_Title
            ))
            nigger = 100 // (len(FONTS.values()) * 2)

            for font in FONTS.values():
                for font_type in ["Regular", "Bold"]:
                    if f"{font}-{font_type}.ttf" not in plugin_dir or force_load:
                        with open(File(temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), "wb") as f:
                            f.write(requests.get(
                                f"https://github.com/itsNightly/font_link/raw/refs/heads/main/{font}-{font_type}.ttf")
                                    .content)

                        niggers_counter = nigger + (nigger * (list(FONTS.values()).index(font))) * 2 + (
                            nigger if font_type == "Bold" else 0)
                        run_on_ui_thread(
                            lambda: self._update_dialog_progress(self.alert_builder_instance, niggers_counter))

            run_on_ui_thread(lambda: self._dismiss_dialog(self.alert_builder_instance))

        except Exception as e:

            run_on_ui_thread(lambda: self._dismiss_dialog(self.alert_builder_instance))
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)
            log(f"Error downloading font: {e}")

    def _get_temp_dir(self):
        base_dir = ApplicationLoader.getFilesDirFixed()
        File(base_dir, "reFamily")
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                return None
            temp_dir = File(base_dir, "reFamily")
            if not temp_dir.exists() and not temp_dir.mkdirs():
                return None
            threading.Thread(target=self._source_downloader, args=(temp_dir,)).start()
            return temp_dir
        except Exception as e:
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)
            log(f"Error getting/creating temp directory: {e}")
            return None

    def _make_card(self):
        track = self.yandex.now_track
        font_family = self.get_setting("font", 0)
        font_family = FONTS[font_family]
        width, height = 1440, 600

        advanced_mode = self.get_setting("advanced_mode", False)
        if not advanced_mode:
            background_color = DEFAULT_COLOR["background_color"]
            title_text_color = DEFAULT_COLOR["title_text_color"]
            subtext_color = DEFAULT_COLOR["subtext_color"]
        else:
            background_color = self.get_setting("background_color", DEFAULT_COLOR["background_color"])
            title_text_color = self.get_setting("title_text_color", DEFAULT_COLOR["title_text_color"])
            subtext_color = self.get_setting("subtext_color", DEFAULT_COLOR["subtext_color"])

        if not track.active:
            if string._lang == 'ru' and font_family == "Circular":
                font_family = "Onest"
            card = Image.new('RGB', (width, height), background_color)
            draw = ImageDraw.Draw(card)
            reMusicFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Regular.ttf").getAbsolutePath(), 40)
            notActiveFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Bold.ttf").getAbsolutePath(), 80)
            draw.text((width // 2, 45), "reMusic", font=reMusicFont, fill=title_text_color, align="center",
                      anchor="mm")
            draw.text((width // 2, height // 2), string.Card_PlayerInactive, font=notActiveFont, fill=title_text_color,
                      align="center", anchor="mm")
            filename = f"now_reMusic.png"
            temp_photo_path = File(self._temp_dir, filename).getAbsolutePath()
            card.save(temp_photo_path)
            return temp_photo_path
        background_setting = self.get_setting("background", 1)
        thumb = requests.get(track.thumb, stream=True).raw
        background = Image.open(thumb)
        thumbnail = background.copy()
        if background_setting == 0:
            background = background.resize((width, width)).crop((0, (width - height) // 2, width, width)).filter(
                ImageFilter.GaussianBlur(radius=14))
            background = ImageEnhance.Brightness(background).enhance(0.3)
            card = Image.new('RGB', (width, height), background_color)
            card.paste(background, (0, 0))
        elif not advanced_mode:
            img = background.resize((16, 16), Image.LANCZOS)
            pixels = img.load()
            lWidth, lHeight = img.size
            for y in range(lHeight):
                for x in range(lWidth):
                    if img.mode == 'L':
                        r = pixels[x, y]
                        r = math.pow(r / 255.0, 1 / 2.2) * 255.0
                        pixels[x, y] = int(r)
                    else:
                        r, g, b = pixels[x, y][:3]
                        r = math.pow(r / 255.0, 1 / 2.2) * 255.0
                        g = math.pow(g / 255.0, 1 / 2.2) * 255.0
                        b = math.pow(b / 255.0, 1 / 2.2) * 255.0
                        if img.mode == 'RGB':
                            pixels[x, y] = (int(r), int(g), int(b))
                        elif img.mode == 'RGBA':
                            a = pixels[x, y][3]
                            pixels[x, y] = (int(r), int(g), int(b), a)
            if img.mode == 'L':
                img = img.convert('RGB')
            elif img.mode == 'RGBA':
                rgb_img = Image.new('RGB', img.size)
                rgb_img.paste(img, mask=img.split()[3])
                img = rgb_img

            pixels = list(img.getdata())
            iWidth, iHeight = img.size

            if img.mode == 'RGB':
                total_r, total_g, total_b = 0, 0, 0
                darkness_index = 1.9
                for r, g, b in pixels:
                    total_r += int(r // darkness_index)
                    total_g += int(g // darkness_index)
                    total_b += int(b // darkness_index)
                count = iWidth * iHeight
                average = (total_r // count, total_g // count, total_b // count)
            else:
                total = sum(pixels)
                average = (total // (iWidth * iHeight),) * 3
            card = Image.new('RGB', (width, height), average)
        else:
            card = Image.new('RGB', (width, height), background_color)

        thumbnail = thumbnail.resize((450, 450))
        mask = Image.new('L', thumbnail.size, 0)
        draw = ImageDraw.Draw(mask)
        draw.rounded_rectangle((0, 0, *thumbnail.size), 30, fill=255)
        thumbnail = thumbnail.copy()
        thumbnail.putalpha(mask)
        card.paste(thumbnail, (75, 75), thumbnail)
        draw = ImageDraw.Draw(card)
        local_font_family = None
        if re.findall(r"[А-Яа-яЁё]", track.title) and font_family == "Circular":
            local_font_family = "Onest"
        titleFont = ImageFont.truetype(File(self._temp_dir,
                                            f"{local_font_family if local_font_family else font_family}-Bold.ttf").getAbsolutePath(),
                                       60)
        x, y = 590, 85
        artistsPlusY = 0
        lines = textwrap.wrap(track.title, width=21)
        if len(lines) > 1:
            lines[1] = lines[1] + "..." if len(lines) > 2 else lines[1]
            artistsPlusY = 70
        else:
            pass
        lines = lines[:2]
        for line in lines:
            draw.text((x, y), line, font=titleFont, fill=title_text_color)
            y += 70
        local_font_family = None
        if re.findall(r"[А-Яа-яЁё]", "".join(track.title)) and font_family == "Circular":
            local_font_family = "Onest"
        artistFont = ImageFont.truetype(File(self._temp_dir,
                                             f"{local_font_family if local_font_family else font_family}-Regular.ttf").getAbsolutePath(),
                                        40)
        artists = textwrap.wrap(" • ".join(track.artist), width=32)
        if len(artists) > 1:
            if "•" in artists[0][-2:]:
                artists[0] = artists[0][:artists[0].rfind("•") - 1]
            artists[0] = artists[0]
        artists = artists[0]
        draw.text((590, 170 + artistsPlusY), artists, subtext_color, font=artistFont)
        if not (self.get_setting("fast_card_render", False)):
            progressBarEmpty = Image.new('RGBA', (width - 665, 10), (0, 0, 0, 0))
            progressDraw = ImageDraw.Draw(progressBarEmpty)
            progressDraw.rounded_rectangle((0, 0, *progressBarEmpty.size), 7, fill=subtext_color)
            progressDraw.rounded_rectangle((0, 0, progressBarEmpty.width * (track.progress / track.duration), 10), 7,
                                           fill=title_text_color)
            card.paste(progressBarEmpty, (590, 460), progressBarEmpty)
            timersFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Regular.ttf").getAbsolutePath(), 30)
            draw.text((590, 490), f"{datetime.datetime.fromtimestamp(track.progress).strftime('%M:%S')}", subtext_color,
                      font=timersFont, anchor="la")
            draw.text((1365, 490), f"{datetime.datetime.fromtimestamp(track.duration).strftime('%M:%S')}",
                      subtext_color, font=timersFont, anchor="ra")
        else:
            local_font_family = None

            if advanced_mode:
                subtext = self.get_setting("instant_subtext", "powered by")
                maintext = self.get_setting("instant_main_text", "reMusic")
            else:
                subtext = "powered by"
                maintext = "reMusic"

            subtext = subtext[:26] + "..." if len(subtext) > 26 else subtext
            maintext = maintext[:21] + "..." if len(maintext) > 21 else maintext

            ru_flag_subtext = True if re.findall(r"[А-Яа-яЁё]", subtext) else False
            ru_flag_maintext = True if re.findall(r"[А-Яа-яЁё]", maintext) else False

            if ru_flag_subtext and font_family == "Circular":
                local_font_family = "Onest"

            infoFont = ImageFont.truetype(File(self._temp_dir,
                                               f"{local_font_family if local_font_family else font_family}-Regular.ttf").getAbsolutePath(),
                                          42)
            local_font_family = None

            if ru_flag_maintext and font_family == "Circular":
                local_font_family = "Onest"

            deviceFont = ImageFont.truetype(File(self._temp_dir,
                                                 f"{local_font_family if local_font_family else font_family}-Bold.ttf").getAbsolutePath(),
                                            52)
            draw.text((590, 415), subtext, subtext_color, font=infoFont, anchor="ls")
            draw.text((590, 485), maintext, title_text_color, font=deviceFont, anchor="ls")

        filename = f"now_reMusic.png"
        temp_photo_path = File(self._temp_dir, filename).getAbsolutePath()
        card.save(temp_photo_path)
        return temp_photo_path

    def _delete_file_delayed(self, path, delay=60):
        def action():
            try:
                time.sleep(delay)
                if os.path.exists(path):
                    os.remove(path)
                    log(f"Deleted temp file: {path}")
            except Exception as e:
                log(f"Delayed delete error: {e}")

        threading.Thread(target=action, daemon=True).start()

    def image_processor(self, msg_params):
        params = {
            "message": None,
            "peer": msg_params.peer
        }

        if not self.yandex.polling:
            track = self.yandex.now(force_request=True)
        else:
            track = self.yandex.now_track

        try:
            answer = self._make_card() if not (self.get_setting("fast_card_render", False)) else True
            if answer:
                temp_file_path = File(self._temp_dir, "now_reMusic.png").getAbsolutePath()

            send_helper = get_send_messages_helper()
            generated_photo = send_helper.generatePhotoSizes(temp_file_path, None)

            if not generated_photo:
                run_on_ui_thread(self.progress.dismiss())
                show_with_copy(string.Bulletin_ErrorMessageCopy, string.Bulletin_FailedProcessImage)
                return HookResult(strategy=HookStrategy.CANCEL)

            params["photo"] = generated_photo
            params["path"] = temp_file_path
            params["replyToMsg"] = msg_params.replyToMsg
            params["replyToTopMsg"] = msg_params.replyToTopMsg
            if track.active:
                yandex_link = self.get_setting("yandex_link", 1)
                songlink = self.get_setting("songlink_link_include", True)
                caption = None
                if any([yandex_link != 0, songlink]):
                    caption = random.choice(["[🎵](5188621441926438751) | ", "[🎶](5188705588925702510) | "])
                    if all([yandex_link != 0, songlink]):
                        link = track.link if (yandex_link == 1 or track is None) else track.album
                        caption += string.Message_CaptionLink_Text.format(
                            link) + string.Message_CaptionDivider + string.Message_CaptionSongLink_Text.format(
                            f"https://song.link/ya/{track.id}")
                    else:
                        if yandex_link:
                            link = track.link if (yandex_link == 1 or track is None) else track.album
                            caption += string.Message_CaptionLink_Text.format(link)
                        elif songlink != 0:
                            caption += string.Message_CaptionSongLink_Text.format(f"https://song.link/ya/{track.id}")
                    caption = parse_markdown(caption)
                    params["caption"] = caption.text
                    params["entities"] = set()
                    for i in caption.entities:
                        params["entities"].add(i.to_tlrpc_object())
            else:
                params["caption"] = string.Message_PlayerNotActive

        except Exception as e:
            params["message"] = string.Bulletin_ErrorMessage.format(e)
            log(f"Internal reMusic error: {e}")
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)

        run_on_ui_thread(self.progress.dismiss())
        send_message(params)

    def _process_audio(self, dialog_id, reply_to_msg, reply_to_top_msg):
        track: Optional[Track]
        if not self.yandex.polling or self.yandex.now_track is None: 
            self.yandex.now_track = self.yandex.get_current_track()
        track = self.yandex.now_track

        if not track.active:
            run_on_ui_thread(self.progress.dismiss())
            BulletinHelper.show_error(
                "Сейчас трек не играет" if Locale.getDefault().getLanguage() == 'ru' else "No track playing"
            )
            return

        url = track.download_url
        if not url:
            run_on_ui_thread(self.progress.dismiss())
            BulletinHelper.show_error("No download link")
            return

        try:
            ext = os.path.splitext(url)[1] or ".mp3"
            filename = f"{track.title}{ext}"
            file_path = File(self._temp_dir, filename).getAbsolutePath()
            resp = requests.get(url, stream=True, timeout=60)
            resp.raise_for_status()

            with open(file_path, 'wb') as f:
                for chunk in resp.iter_content(8192): f.write(chunk)

            ext_root = ApplicationLoader.applicationContext.getExternalCacheDir()
            plugin_dir = File(ext_root, TEMP_DIR_NAME)

            if not plugin_dir.exists() and not plugin_dir.mkdirs():
                pass

            ext_path = File(plugin_dir, File(file_path).getName()).getAbsolutePath()

            with open(file_path, 'rb') as fin, open(ext_path, 'wb') as fout:
                fout.write(fin.read())

            account = get_account_instance()
            mime = "audio/mpeg"
            markdown_need = False
            yandex_link = self.get_setting("yandex_link", 1)
            songlink = self.get_setting("songlink_link_include", True)
            caption = None
            if any([yandex_link != 0, songlink]):
                markdown_need = True
                caption = random.choice(["[🎵](5188621441926438751) | ", "[🎶](5188705588925702510) | "])
                if all([yandex_link != 0, songlink]):
                    link = track.link if (yandex_link == 1 or track is None) else track.album
                    caption += string.Message_CaptionLink_Text.format(
                        link) + string.Message_CaptionDivider + string.Message_CaptionSongLink_Text.format(
                        f"https://song.link/ya/{track.id}")
                else:
                    if yandex_link != 0:
                        link = track.link if (yandex_link == 1 or track is None) else track.album
                        caption += string.Message_CaptionLink_Text.format(link)
                    elif songlink != 0:
                        caption += string.Message_CaptionSongLink_Text.format(f"https://song.link/ya/{track.id}")
            SendMessagesHelper.prepareSendingDocument(account, ext_path, ext_path, None,
                                                      f"{caption} reMusic_flag_metadata{' reMusic_flag_markdown' if markdown_need else ''}", mime, dialog_id,
                                                      reply_to_msg, reply_to_top_msg, None, None, None, True, 0, None, None,
                                                      0, False)

            run_on_ui_thread(self.progress.dismiss())
            self._delete_file_delayed(file_path)
            self._delete_file_delayed(ext_path)
        except Exception as e:
            run_on_ui_thread(self.progress.dismiss())
            show_with_copy("An error has occurred", e)

    def search_family_plugin(self, prompt: str) -> bool:
        prompt = prompt.lower()
        plugins = [p for p in plugins_manager.PluginsManager._plugins.values()]
        plugins_names = [p.name.lower() for p in plugins_manager.PluginsManager._plugins.values()]
        plugins_ids = [p.id.lower() for p in plugins_manager.PluginsManager._plugins.values()]

        if prompt in plugins_names:
            plugin = [p for p in plugins if prompt == p.name.lower() and p.enabled]
            if len(plugin) == 1 and plugin[0]:
                return True
            else:
                return False

        elif prompt in plugins_ids:
            plugin = [p for p in plugins if prompt == p.id.lower() and p.enabled]
            if len(plugin) == 1 and plugin[0]:
                return True
            else:
                return False

        else:
            return False

    def set_family_mode_enabled(self):
        global FAMILY_ENABLED

        old_value: bool = FAMILY_ENABLED
        new_value: bool = self.search_family_plugin(FAMILY_PLUGIN_ID)
        FAMILY_ENABLED = new_value

        log(f"[{__name__}] old_value: {old_value}, new_value: {new_value}")

        if old_value is True and new_value is False:
            self.show_family_bulletin(False)
        elif old_value is False and new_value is True:
            self.show_family_bulletin(True)
        else:
            self.show_family_bulletin(False, True)

    def show_family_bulletin(self, enabled: bool, changed: bool = False):
        if changed:
            BulletinHelper.show_info(string.Family_Mode_Changed)
            return

        BulletinHelper.show_with_button(
            string.Family_Mode_Enabled if enabled else string.Family_Mode_Disabled,
            R.raw.info,
            string.Family_Mode_About,
            on_click=lambda: self.show_my_info_alert(
                title=string.Alert_Trigger_Title,
                message=string.Alert_Trigger_Text.format(".now")
            )
        )

"""



                            ДИСКЛЕЙМЕР

Если при создании своего плагина вы решили использовать готовые кодовые решения 
нашего плагина у себя, то не забудьте упомянуть в описании своего плагина 
канал @MeeowPlugins в качестве кредитов за помощь в разработке плагина. Спасибо 


                  ⣾⡇⣿⣿⡇⣾⣿⣿⣿⣿⣿⣿⣿⣿⣄⢻⣦⡀⠁⢸⡌⠻⣿⣿⣿⡽⣿⣿ 
                  ⡇⣿⠹⣿⡇⡟⠛⣉⠁⠉⠉⠻⡿⣿⣿⣿⣿⣿⣦⣄⡉⠂⠈⠙⢿⣿⣝⣿ 
                  ⠤⢿⡄⠹⣧⣷⣸⡇⠄⠄⠲⢰⣌⣾⣿⣿⣿⣿⣿⣿⣶⣤⣤⡀⠄⠈⠻⢮ 
                  ⠄⢸⣧⠄⢘⢻⣿⡇⢀⣀⠄⣸⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣧⡀⠄⢀ 
                  ⠄⠈⣿⡆⢸⣿⣿⣿⣬⣭⣴⣿⣿⣿⣿⣿⣿⣿⣯⠝⠛⠛⠙⢿⡿⠃⠄⢸ 
                  ⠄⠄⢿⣿⡀⣿⣿⣿⣾⣿⣿⣿⣿⣿⣿⣿⣿⣿⣷⣿⣿⣿⣿⡾⠁⢠⡇⢀ 
                  ⠄⠄⢸⣿⡇⠻⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣏⣫⣻⡟⢀⠄⣿⣷⣾ 
                  ⠄⠄⢸⣿⡇⠄⠈⠙⠿⣿⣿⣿⣮⣿⣿⣿⣿⣿⣿⣿⣿⡿⢠⠊⢀⡇⣿⣿ 
                  ⠒⠤⠄⣿⡇⢀⡲⠄⠄⠈⠙⠻⢿⣿⣿⠿⠿⠟⠛⠋⠁⣰⠇⠄⢸⣿⣿⣿ 



                            DISCLAIMER

If, when creating your plugin, you decided to use the ready-made code solutions 
of our plugin, then do not forget to mention the @MeeowPlugins channel in the description 
of your plugin as credits for help in developing your plugin. Thanks



"""