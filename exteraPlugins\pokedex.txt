import requests
import os
import uuid
import time
from threading import Thread
from ui.settings import Head<PERSON>, Switch, Divider
from client_utils import get_account_instance
from base_plugin import HookResult, HookStrategy
from org.telegram.messenger import SendMessages<PERSON>elper, ApplicationLoader
from java.io import File
from android_utils import log, run_on_ui_thread
from java.util import Locale, ArrayList
from org.telegram.tgnet import TLRPC

__name__ = "Pokédex"
__description__ = "Gets Pokémon information"
__icon__ = "exteraPluginsSup/1"
__version__ = "1.0.2"
__id__ = "pokedex"
__author__ = "@itsv1eds"
__min_version__ = "11.9.0"

API_URL = "https://pokeapi.co/api/v2/pokemon/"

class PokedexPlugin(BasePlugin):

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith('ru'):
            header = "Настройки плагина Покедекс"
            sb_label, sb_sub = "Показывать основную информацию", "Отображать основную информацию о покемоне"
            sh_label, sh_sub = "Показывать удерживаемые предметы", "Отображать удерживаемые предметы"
            sd_label, sd_sub = "Показывать описание", "Отображать описание вида"
            sg_label, sg_sub = "Показывать род", "Отображать род вида"
            shab_label, shab_sub = "Показывать среду обитания", "Отображать среду обитания вида"
            scol_label, scol_sub = "Показывать цвет", "Отображать цвет вида"
            seg_label, seg_sub = "Показывать яйцевые группы", "Отображать яйцевые группы вида"
            sgr_label, sgr_sub = "Показывать соотношение полов", "Отображать соотношение полов вида"
            scr_label, scr_sub = "Показывать шанс поимки", "Отображать шанс поимки вида"
            sgrw_label, sgrw_sub = "Показывать скорость роста", "Отображать скорость роста вида"
            ss_label, ss_sub = "Показывать спрайт", "Отображать спрайт покемона"
            cmd = "Команда: .dex [имя/id]"
        else:
            header = "Pokedex Plugin Settings"
            sb_label, sb_sub = "Show Basic Info", "Display basic Pokemon info"
            sh_label, sh_sub = "Show Held Items", "Display held items"
            sd_label, sd_sub = "Show Description", "Display species description"
            sg_label, sg_sub = "Show Genus", "Display species genus"
            shab_label, shab_sub = "Show Habitat", "Display species habitat"
            scol_label, scol_sub = "Show Color", "Display species color"
            seg_label, seg_sub = "Show Egg Groups", "Display species egg groups"
            sgr_label, sgr_sub = "Show Gender Ratio", "Display species gender ratio"
            scr_label, scr_sub = "Show Capture Rate", "Display species capture rate"
            sgrw_label, sgrw_sub = "Show Growth Rate", "Display species growth rate"
            ss_label, ss_sub = "Show Sprite", "Display Pokemon sprite"
            cmd = "Command: .dex [name/id]"
        return [
            Header(text=header),
            Switch(key="show_basic_info", text=sb_label, default=True, subtext=sb_sub, icon="msg_info"),
            Switch(key="show_held_items", text=sh_label, default=True, subtext=sh_sub, icon="msg_emoji_gem"),
            Switch(key="show_species_description", text=sd_label, default=True, subtext=sd_sub, icon="msg_info_filled"),
            Switch(key="show_species_genus", text=sg_label, default=True, subtext=sg_sub, icon="msg_mask"),
            Switch(key="show_species_habitat", text=shab_label, default=True, subtext=shab_sub, icon="msg_map"),
            Switch(key="show_species_color", text=scol_label, default=True, subtext=scol_sub, icon="msg_colors"),
            Switch(key="show_species_egg_groups", text=seg_label, default=True, subtext=seg_sub, icon="msg_emoji_gem"),
            Switch(key="show_species_gender_ratio", text=sgr_label, default=True, subtext=sgr_sub, icon="msg_log"),
            Switch(key="show_species_capture_rate", text=scr_label, default=True, subtext=scr_sub, icon="msg_speed_veryfast"),
            Switch(key="show_species_growth_rate", text=sgrw_label, default=True, subtext=sgrw_sub, icon="msg_speed_superfast"),
            Switch(key="show_sprite", text=ss_label, default=True, subtext=ss_sub, icon="msg_view_file"),
            Divider(text=cmd),
        ]

    def on_plugin_load(self):
        self.add_on_send_message_hook(priority=0)

    def on_plugin_unload(self):
        pass

    def _format_pokemon(self, data):
        try:
            name = data.get('name', '').capitalize()
            poke_id = data.get('id', '')
            base_exp = data.get('base_experience', '?')
            order = data.get('order', '?')
            types = [t['type']['name'].capitalize() for t in data.get('types', [])]
            abilities = [a['ability']['name'].replace('-', ' ').capitalize() for a in data.get('abilities', [])]
            height = data.get('height', 0) / 10
            weight = data.get('weight', 0) / 10
            stats = {s['stat']['name']: s['base_stat'] for s in data.get('stats', [])}
            msg = f"🎮 #{poke_id} {name}\n"
            msg += f"⭐ Base Experience / Базовый опыт: {base_exp}\n"
            msg += f"🔢 Order / Порядок: {order}\n"
            msg += f"📏 Height / Рост: {height} m\n"
            msg += f"⚖️ Weight / Вес: {weight} kg\n"
            msg += f"🔸 Types / Типы: {', '.join(types)}\n"
            msg += f"🔹 Abilities / Способности: {', '.join(abilities)}\n"
            msg += "📊 Base Stats / Основные характеристики:\n"
            for stat_name, stat_value in stats.items():
                msg += f"• {stat_name.capitalize()}: {stat_value}\n"
            return msg
        except Exception as e:
            log(f"Error formatting pokemon data: {e}")
            return "Error formatting Pokémon data."

    def _download_image(self, url):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            images_dir = File(base_dir, "pokedex_images")
            if not images_dir.exists():
                images_dir.mkdirs()
            path = File(images_dir, f"poke_{uuid.uuid4()}.png").getAbsolutePath()
            resp = requests.get(url, stream=True, timeout=10)
            resp.raise_for_status()
            with open(path, 'wb') as f:
                for chunk in resp.iter_content(chunk_size=8192):
                    f.write(chunk)
            return path
        except Exception as e:
            log(f"Error downloading image: {e}")
            return None

    def _delete_temp_file_async(self, file_path, delay_seconds=5):
        def _del():
            try:
                time.sleep(delay_seconds)
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                log(f"Error deleting temp file: {e}")
        Thread(target=_del, daemon=True).start()

    def _process_pokemon_request(self, name, toggles, dialog_id, lang):
        log(f"Pokedex: Starting request for {name} (dialog_id={dialog_id})")
        log(f"Pokedex: Toggles: {toggles}")
        is_ru = lang.startswith('ru')
        def L(eng, rus): return rus if is_ru else eng
        try:
            pokeapi_url = f"{API_URL}{name}/"
            log(f"Pokedex: Requesting PokeAPI URL: {pokeapi_url}")
            resp = requests.get(pokeapi_url, timeout=10)
            resp.raise_for_status()
            log(f"Pokedex: Received data for {name}, status OK")
            data = resp.json()
            text = ""
            if toggles['show_basic_info']:
                text += self._format_pokemon(data) + "\n"
            species_url = data.get('species', {}).get('url', '')
            desc = ""
            if species_url and toggles['show_species_description']:
                tmp = requests.get(species_url, timeout=10)
                tmp.raise_for_status()
                sd = tmp.json()
                fls = [f['flavor_text'] for f in sd.get('flavor_text_entries', []) if f.get('language', {}).get('name')=='en']
                if fls: desc = fls[0].replace('\n', ' ')
            if desc:
                text += f"\n📝 {L('Description:', 'Описание вида:')} {desc}\n"
            if toggles['show_held_items']:
                held = [i['item']['name'].replace('-', ' ').capitalize() for i in data.get('held_items', [])]
                if held: text += f"\n🎁 {L('Held Items:', 'Удерживаемые предметы:')} {', '.join(held)}"
            if species_url and any([toggles['show_species_genus'], toggles['show_species_habitat'], toggles['show_species_color'], toggles['show_species_egg_groups'], toggles['show_species_gender_ratio'], toggles['show_species_capture_rate'], toggles['show_species_growth_rate']]):
                species_resp = requests.get(species_url, timeout=10)
                species_resp.raise_for_status()
                sd = species_resp.json()
                if toggles['show_species_genus']:
                    gen = [g['genus'] for g in sd.get('genera', []) if g.get('language', {}).get('name')=='en']
                    if gen: text += f"\n📖 {L('Genus:', 'Род:')} {gen[0]}"
                if toggles['show_species_habitat']:
                    hab = sd.get('habitat', {}).get('name')
                    if hab: text += f"\n🏡 {L('Habitat:', 'Среда обитания:')} {hab.capitalize()}"
                if toggles['show_species_color']:
                    col = sd.get('color', {}).get('name')
                    if col: text += f"\n🎨 {L('Color:', 'Цвет:')} {col.capitalize()}"
                if toggles['show_species_egg_groups']:
                    eggs = [e['name'].capitalize() for e in sd.get('egg_groups', [])]
                    if eggs: text += f"\n🥚 {L('Egg Groups:', 'Яйцевые группы:')} {', '.join(eggs)}"
                if toggles['show_species_gender_ratio']:
                    gr = sd.get('gender_rate')
                    if gr is not None and gr!=-1:
                        male=100-gr*12.5;female=gr*12.5
                        text += f"\n⚧ {L('Gender Ratio:', 'Соотношение полов:')} {male:.1f}%♂/{female:.1f}%♀"
                if toggles['show_species_capture_rate']:
                    cap = sd.get('capture_rate')
                    if cap is not None: text += f"\n🎯 {L('Capture Rate:', 'Шанс поимки:')} {cap}"
                if toggles['show_species_growth_rate']:
                    grw = sd.get('growth_rate',{}).get('name')
                    if grw: text += f"\n📈 {L('Growth Rate:', 'Скорость роста:')} {grw.capitalize()}"
            # always send text result first
            msg_text = text or L("No data sections enabled in settings.", "Нет включенных разделов данных в настройках.")
            run_on_ui_thread(lambda: SendMessagesHelper.sendMessage(SendMessagesHelper.SendMessageParams.of(msg_text, dialog_id)))
            # optionally send sprite
            if toggles.get('show_sprite', False):
                sprites = data.get('sprites', {})
                other = sprites.get('other', {})
                official_art = other.get('official-artwork') or other.get('official_artwork', {})
                img = official_art.get('front_default') or sprites.get('front_default', '')
                if img:
                    try:
                        path = self._download_image(img)
                        if path:
                            acct = get_account_instance()
                            ent = TLRPC.TL_messageEntityBlockquote(); ent.collapsed=True; ent.offset=0; ent.length=int(len(msg_text.encode('utf_16_le'))/2)
                            entities = ArrayList(); entities.add(ent)
                            run_on_ui_thread(lambda: SendMessagesHelper.prepareSendingPhoto(acct, path, None, None, dialog_id, None, None, None, None, entities, None, None, 0, None, None, True, 0, 0, False, msg_text, None, 0, 0, 0))
                            self._delete_temp_file_async(path)
                    except Exception as e:
                        log(f"Pokedex: Error sending sprite: {e}")
        except Exception as e:
            msg = f"{L('Error:', 'Ошибка:')} {e}"
            ent=TLRPC.TL_messageEntityBlockquote();ent.collapsed=True;ent.offset=0;ent.length=int(len(msg.encode('utf_16_le'))/2)
            run_on_ui_thread(lambda: SendMessagesHelper.sendMessage(SendMessagesHelper.SendMessageParams.of(msg, dialog_id)))

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()
        msg = params.message.strip()
        if not msg.startswith(".dex"):
            return HookResult()
        if msg == ".dex" or not msg.startswith(".dex "):
            lang = Locale.getDefault().getLanguage()
            if lang.startswith('ru'):
                params.message = "Команда: .dex [имя/id]"
            else:
                params.message = "Command: .dex [name/id]"
            entity = TLRPC.TL_messageEntityBlockquote()
            entity.collapsed = True
            entity.offset = 0
            entity.length = int(len(params.message.encode('utf_16_le'))/2)
            params.entities = [entity]
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        name = msg.split(maxsplit=1)[1].strip().lower()
        toggles = { key: self.get_setting(key, True) for key in [
            'show_basic_info','show_held_items','show_species_description',
            'show_species_genus','show_species_habitat','show_species_color',
            'show_species_egg_groups','show_species_gender_ratio',
            'show_species_capture_rate','show_species_growth_rate','show_sprite'
        ] }
        lang = Locale.getDefault().getLanguage()
        try:
            self._process_pokemon_request(name, toggles, params.peer, lang)
        except Exception as e:
            log(f"Pokedex: Sync process error: {e}")
        return HookResult(strategy=HookStrategy.CANCEL)

PokedexPlugin()