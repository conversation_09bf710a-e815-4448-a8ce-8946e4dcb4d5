__id__ = "ai_status"
__name__ = "AI Status"
__description__ = "Автоматическая генерация био/гео/имени с помощью ИИ."
__icon__ = "RestrictedEmoji/116"
__version__ = "1.0.4"
__author__ = "@RnPlugins, by pollinations.ai"
__min_version__ = "11.12.0"

import threading, time, re, requests, json
from datetime import datetime
from org.telegram.tgnet.tl import TL_account
from client_utils import send_request, get_user_config, get_last_fragment
from ui.settings import Header, Switch, Input, Text, Divider
from ui.bulletin import BulletinHelper

AI_API_URL = "https://text.pollinations.ai/openai/v1/chat/completions"

def parse_interval(interval_str):
    pattern = r'(\d+)\s*(d|h|m|s)'
    matches = re.findall(pattern, interval_str.lower())
    if not matches:
        raise ValueError("Неверный формат")
    total = 0
    for v, u in matches:
        v = int(v)
        if u=='d': total += v*86400
        elif u=='h': total += v*3600
        elif u=='m': total += v*60
        elif u=='s': total += v
    return max(total, 30)

def clean_ai_text(text):
    if not text: return ""
    text = re.sub(r'\[.*?\]\(.*?\)', '', text)
    text = re.sub(r'http[s]?://\S+', '', text)
    text = re.sub(r'[*_~`#]', '', text)
    text = re.sub(r'\s{2,}', ' ', text)
    text = re.sub(r'\(\)', '', text)
    return text.strip()

class AIStatusPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.running = False

    def on_plugin_load(self):
        self.running = True
        threading.Thread(target=self.worker, daemon=True).start()

    def on_plugin_unload(self):
        self.running = False

    def create_settings(self):
        return [
            Header(text="Настройки Имени"),
            Switch(key="enable_name", text="Включить обновление Имени", default=False, icon="msg_openprofile"),
            Text(text="Настроить Имя/Фамилию", icon="msg_download_settings", create_sub_fragment=self.create_name_settings),
            Divider(),
            Header(text="Настройки Био"),
            Switch(key="enable_bio", text="Включить обновление Био", default=False, icon="msg_online"),
            Text(text="Настроить Био", icon="msg_download_settings", create_sub_fragment=self.create_bio_settings),
            Divider(),
            Header(text="Настройки Гео (TgPremium)"),
            Switch(key="enable_geo", text="Включить обновление Гео", default=False, icon="menu_premium_location"),
            Text(text="Настроить Гео", icon="msg_download_settings", create_sub_fragment=self.create_geo_settings),
            Divider(),
            Header(text="Дополнительно"),
            Switch(key="debug_mode", text="Отладка", subtext="Показывать отладочную информацию", default=False, icon="msg_info"),
        ]

    def create_name_settings(self):
        return [
            Input(key="name_interval", text="Интервал", default="1h", subtext="Формат: 1d 2h 3m 4s", icon="msg_recent"),
            Divider("Настройте, через какой промежуток времени текст будет меняться."),
            Input(key="name_prompt", text="Промпт для имени", default="Придумай креативное имя", subtext="Напр.: Придумай креативный никнейм", icon="msg_photo_text_regular"),
            Input(key="name_fallback", text="Текст при ошибке", default="...", icon="msg_info"),
            Divider(),
            Switch(key="enable_name_search", text="Доступ в интернет", subtext="Если включено, AI будет иметь доступ к поиску в интернете.", default=False, icon="msg_language"),
            Divider(),
            Text(text="Сгенерировать сейчас", icon="msg_header_draw", accent=True, on_click=lambda v: self.manual_generate("name")),
        ]

    def create_bio_settings(self):
        return [
            Input(key="bio_interval", text="Интервал", default="30m", subtext="Формат: 1d 2h 3m 4s", icon="msg_recent"),
            Divider("Настройте, через какой промежуток времени текст будет меняться."),
            Input(key="bio_prompt", text="Промпт", default="Сгенерируй креативный статус для телеграм", subtext="Напр.: Придумай креативный никнейм", icon="msg_photo_text_regular"),
            Input(key="bio_fallback", text="Текст при ошибке", default="ИИ недоступен", icon="msg_info"),
            Divider(),
            Switch(key="enable_bio_search", text="Доступ в интернет", subtext="Если включено, AI будет иметь доступ к поиску в интернете.", default=False, icon="msg_language"),
            Divider(),
            Text(text="Сгенерировать сейчас", icon="msg_header_draw", accent=True, on_click=lambda v: self.manual_generate("bio")),
        ]

    def create_geo_settings(self):
        return [
            Input(key="geo_interval", text="Интервал", default="30m", subtext="Формат: 1d 2h 3m 4s", icon="msg_recent"),
            Divider("Настройте, через какой промежуток времени текст будет меняться."),
            Input(key="geo_prompt", text="Промпт для гео", default="Сгенерируй креативный статус для телеграм", subtext="Напр.: Придумай креативный никнейм", icon="msg_photo_text_regular"),
            Input(key="geo_fallback", text="Текст при ошибке", default="ИИ недоступен", icon="msg_info"),
            Divider(),
            Switch(key="enable_geo_search", text="Доступ в интернет", subtext="Если включено, AI будет иметь доступ к поиску в интернете.", default=False, icon="msg_language"),
            Divider(),
            Text(text="Сгенерировать сейчас", icon="msg_header_draw", accent=True, on_click=lambda v: self.manual_generate("geo")),
        ]

    def worker(self):
        last = {"name":0,"bio":0,"geo":0}
        while self.running:
            now = time.time()
            for mode in ("name","bio","geo"):
                if self.get_setting(f"enable_{mode}", False):
                    interval = parse_interval(self.get_setting(f"{mode}_interval", "30m"))
                    if now - last[mode] >= interval:
                        getattr(self, f"update_{mode}")()
                        last[mode] = now
            time.sleep(5)

    def build_system_prompt(self):
        t = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return f"Ты генерируель короткую, чистую, БЕЗ форматирования, лишнего текста, фразу для статуса в Telegram. Не пиши «хорошо», «привет», «```», заголовки и тому подобное, пиши ТОЛЬКО одну фразу, по скольку есть ограничение по символам. Текущее время: {t}"

    def get_ai_status(self, prompt, mode):
        key = f"enable_{mode}_search"
        model = "searchgpt" if self.get_setting(key, False) else "openai-fast"
        try:
            payload = {"model": model, "messages":[{"role":"system","content":self.build_system_prompt()}, {"role":"user","content":prompt}]}
            resp = requests.post(AI_API_URL, json=payload, timeout=15)
            if resp.status_code==200:
                return clean_ai_text(resp.json().get("choices", [{}])[0].get("message",{}).get("content",""))
            if self.get_setting("debug_mode", False):
                return f"Ошибка API: {resp.status_code}"
        except Exception as e:
            if self.get_setting("debug_mode", False):
                return f"Ошибка: {e}"
        return None

    def update_name(self):
        text = self.get_ai_status(self.get_setting("name_prompt",""), "name") or self.get_setting("name_fallback","")
        parts = text.split()
        req = TL_account.updateProfile()
        req.flags = (1<<0)|(1<<1)
        req.first_name = parts[0][:64]
        req.last_name = " ".join(parts[1:])[:64] if len(parts)>1 else ""
        send_request(req, ())

    def update_bio(self):
        text = self.get_ai_status(self.get_setting("bio_prompt",""), "bio") or self.get_setting("bio_fallback","")
        max_len = 140 if get_user_config().isPremium() else 70
        req = TL_account.updateProfile()
        req.flags = 4
        req.about = text[:max_len]
        send_request(req, ())

    def update_geo(self):
        text = self.get_ai_status(self.get_setting("geo_prompt",""), "geo") or self.get_setting("geo_fallback","")
        req = TL_account.updateBusinessLocation()
        req.flags = 1
        req.address = text[:96]
        send_request(req, ())

    def manual_generate(self, mode):
        BulletinHelper.show_info(f"Генерация {mode}...", get_last_fragment())
        threading.Thread(target=lambda: (getattr(self, f"update_{mode}")(), BulletinHelper.show_success(f"{mode.capitalize()} сгенерировано.", get_last_fragment())), daemon=True).start()

    def ok(): return None