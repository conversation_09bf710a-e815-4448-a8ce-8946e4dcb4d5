# Быстрая установка Voice Transcription Plugin

## Шаг 1: Установка плагина

1. Скачайте файл `voice_transcription.plugin`
2. В ExteraGram: **Меню** → **Настройки** → **Плагины** → **Установить плагин**
3. Выберите скачанный файл
4. Дождитесь сообщения об успешной установке

## Шаг 2: Получение API ключа

1. Откройте [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Войдите в Google аккаунт
3. Нажмите **"Create API Key"**
4. Скопируйте созданный ключ

## Шаг 3: Настройка

1. В ExteraGram: **Настройки** → **Плагины** → **Voice Transcription**
2. Включите **"Включить расшифровку"**
3. Вставьте API ключ в поле **"API ключ Gemini"**
4. Сохраните настройки

## Готово! 🎉

Теперь вы можете:
- Долго нажать на любое голосовое сообщение
- Выбрать **"Расшифровать"** в меню
- Получить текстовую расшифровку

---

❓ **Нужна помощь?** Читайте полную инструкцию в [README.md](README.md) 