import json
from base_plugin import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from ui.settings import Header, Input, Switch, Divider, Text
from client_utils import get_user_config, send_message, get_messages_controller, get_messages_storage
from android_utils import run_on_ui_thread, log
from markdown_utils import parse_markdown
from org.telegram.tgnet import TLRPC
from java.util import ArrayList
from java import jint
import random

# --- Metadata ---
__id__ = "roleplay_plugin"
__name__ = "RolePlay Commands"
__version__ = "1.4"
__author__ = "@windukk and @shakyyra"
__description__ = "Плагин для ролевых команд. Больше информации в настройках"
__min_version__ = "11.9.0"
__icon__ = "Pguliptpvrbeng/1" 

# --- Constants for settings keys ---
KEY_WHITELIST_ENABLED = "whitelist_enabled"
KEY_WHITELIST_CHATS = "whitelist_chats"
KEY_RP_COMMANDS = "rp_commands"
KEY_RP_NAMES = "rp_names"
KEY_OWNER_ONLY = "owner_only"
KEY_PLAIN_ENABLED = "plain_enabled"
KEY_EMOJI_ENABLED = "emoji_enabled"
KEY_EMOJI_LIST = "emoji_list"

class RolePlayPlugin(BasePlugin):

    def on_plugin_load(self):
        """Called when the plugin is loaded."""
        if self.get_setting(KEY_RP_COMMANDS) is None:
            self.set_setting(KEY_RP_COMMANDS, json.dumps({}))
        if self.get_setting(KEY_RP_NAMES) is None:
            self.set_setting(KEY_RP_NAMES, json.dumps({}))

        if self.get_setting(KEY_WHITELIST_ENABLED) is None:
            self.set_setting(KEY_WHITELIST_ENABLED, False)
        if self.get_setting(KEY_OWNER_ONLY) is None:
            self.set_setting(KEY_OWNER_ONLY, True)  
        if self.get_setting(KEY_PLAIN_ENABLED) is None:
            self.set_setting(KEY_PLAIN_ENABLED, False)
        if self.get_setting(KEY_EMOJI_ENABLED) is None:
            self.set_setting(KEY_EMOJI_ENABLED, False)
        if self.get_setting(KEY_EMOJI_LIST) is None:
            self.set_setting(KEY_EMOJI_LIST, "")

        self.add_on_send_message_hook()

        self.add_hook("NewMessage", match_substring=True)
        self.add_hook("NewChannelMessage", match_substring=True)
        self.add_hook("ShortMessage", match_substring=True)
        self.add_hook("ShortChatMessage", match_substring=True)

        self.log(f"Plugin '{self.name}' loaded successfully.")

    def on_plugin_unload(self):
        """Called when the plugin is unloaded."""
        self.remove_hook("on_send_message_hook")
        self.log(f"Plugin '{self.name}' unloaded successfully.")

    def create_settings(self):
        """Creates the settings UI for the plugin."""
        return [
            Header(text="Ролевые команды"),
            Divider(),
            Header("Доступ"),
            Switch(
                key=KEY_OWNER_ONLY,
                text="Разрешить всем использовать RP-команды",
                icon="msg_users",
                default=self.get_setting(KEY_OWNER_ONLY, True),
                subtext="Если выключено — использовать сможете только вы"
            ),
            Switch(
                key=KEY_PLAIN_ENABLED,
                text="Триггеры без точки",
                icon="msg_filter",
                default=self.get_setting(KEY_PLAIN_ENABLED, False),
                subtext="Позволяет вызывать триггеры без символа '.' в начале"
            ),
            Switch(
                key=KEY_EMOJI_ENABLED,
                text="Случайная эмодзи перед сообщением",
                icon="msg_emoji",
                default=self.get_setting(KEY_EMOJI_ENABLED, False),
                subtext="Добавлять случайную эмодзи из списка ниже"
            ),
            Input(
                key=KEY_EMOJI_LIST,
                text="Список эмодзи (через пробел)",
                icon="msg_emoji",
                default=self.get_setting(KEY_EMOJI_LIST, ""),
                subtext="Перечислите несколько эмодзи через пробел, например: 😊 😂 💋"
            ),
            Divider(),
            Header("Белый список чатов"),
            Switch(
                key=KEY_WHITELIST_ENABLED,
                text="Включить ограничение по чатам",
                icon="msg_filter",
                default=self.get_setting(KEY_WHITELIST_ENABLED, False),
                subtext="Команды работают только в указанных ниже чатах"
            ),
            Input(
                key=KEY_WHITELIST_CHATS,
                text="ID разрешённых чатов",
                icon="msg_link",
                default=self.get_setting(KEY_WHITELIST_CHATS, ""),
                subtext="Перечислите ID через запятую (для групп с тире)"
            ),
            Divider(),
            Header("Доступные команды:"),
            Text(text=".rpadd [действие] [ответ] — добавить триггер", icon="msg_bot"),
            Text(text=".rpdel [действие] — удалить триггер", icon="msg_bot"),
            Text(text=".rpname [имя] — задать своё RP-имя", icon="msg_bot"),
            Text(text=".rpname remove — сбросить RP-имя", icon="msg_bot"),
            Text(text=".rplist — список триггеров", icon="msg_bot"),
            Text(text="Использование: .rpadd [команда] [действие]"),
            Text(text="Пример: .rpadd ноль {user} нулит {target}"),
            Text(text="{user} — автор {target} — цель.")
        ]

    def on_send_message_hook(self, account, params):
        try:
            if not hasattr(params, "message") or not isinstance(params.message, str):
                return HookResult()

            message_text = params.message.strip()
            parts = message_text.split()
            if not parts:
                return HookResult()

            first_word = parts[0].lower()
            cmds = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
            allow_plain = self.get_setting(KEY_PLAIN_ENABLED, False)
            is_plain_trigger = allow_plain and first_word in cmds

            if not message_text.startswith(".") and not is_plain_trigger:
                return HookResult()

            owner_id = get_user_config().getClientUserId()
            chat_id = params.peer
            user_id = getattr(params, "from_id", owner_id)

            if not self._is_chat_allowed(chat_id):
                return HookResult()

            owner_only = self.get_setting(KEY_OWNER_ONLY, True)

            command = parts[0].lower() if message_text.startswith('.') else '.' + parts[0].lower()

            # Admin-only commands
            if command == ".rpadd":
                if not self._is_owner(user_id):
                    self._send_md(chat_id, "🚫 Вы не являетесь владельцем плагина.")
                    return HookResult(strategy=HookStrategy.CANCEL)
                if len(parts) < 3:
                    self._send_md(chat_id, "ℹ️ Использование: .rpadd [действие] [ответ]\n\nПример: .rpadd ноль {user} нулит {target}")
                    return HookResult(strategy=HookStrategy.CANCEL)
                action = parts[1].lower()
                response = " ".join(parts[2:])
                commands = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
                commands[action] = response
                self.set_setting(KEY_RP_COMMANDS, json.dumps(commands))
                self._send_md(chat_id, f"✅ *Триггер* `{action}` *добавлен*.")
                return HookResult(strategy=HookStrategy.CANCEL)

            elif command == ".rpdel":
                if not self._is_owner(user_id):
                    self._send_md(chat_id, "🚫 Вы не являетесь владельцем плагина.")
                    return HookResult(strategy=HookStrategy.CANCEL)
                if len(parts) < 2:
                    self._send_md(chat_id, "ℹ️ Использование: .rpdel [действие]")
                    return HookResult(strategy=HookStrategy.CANCEL)
                action = parts[1].lower()
                commands = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
                if action in commands:
                    del commands[action]
                    self.set_setting(KEY_RP_COMMANDS, json.dumps(commands))
                    self._send_md(chat_id, f"🗑️ *Триггер* `{action}` *удалён*.")
                else:
                    self._send_md(chat_id, f"❌ Триггер '{action}' не найден.")
                return HookResult(strategy=HookStrategy.CANCEL)

            elif command == ".rpname":
                names = json.loads(self.get_setting(KEY_RP_NAMES, "{}"))
                if len(parts) > 1 and parts[1].lower() == "remove":
                    if str(user_id) in names:
                        del names[str(user_id)]
                        self.set_setting(KEY_RP_NAMES, json.dumps(names))
                        self._send_md(chat_id, "♻️ Имя сброшено.")
                    else:
                        self._send_md(chat_id, "⚠️ У вас нет RP-имени.")
                elif len(parts) > 1:
                    name = " ".join(parts[1:])
                    names[str(user_id)] = name
                    self.set_setting(KEY_RP_NAMES, json.dumps(names))
                    self._send_md(chat_id, f"👤 Теперь твоё RP-имя: *{name}*")

                return HookResult(strategy=HookStrategy.CANCEL)

            elif command == ".rplist":
                commands = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
                if not commands:
                    self._send_md(chat_id, "📂 Список триггеров пуст.")
                else:
                    response = "*Триггеры:*\n" + "\n".join(f"• `{key}`" for key in commands.keys())
                    self._send_md(chat_id, response)
                return HookResult(strategy=HookStrategy.CANCEL)

            # Обработка ролевых команд
            rp_command = command[1:]
            commands = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
            if rp_command in commands:
                # Access scope check
                if owner_only and not self._is_owner(user_id):
                    return HookResult()
                user_name = self._get_display_name(user_id)
                # цель по реплаю либо аргументам
                target_name = "себя"
                reply_obj = getattr(params, "replyToMsg", None)
                if reply_obj is not None:
                    target_name = self._get_target_display_name(chat_id, reply_obj)
                elif len(parts) > 1:
                    target_name = " ".join(parts[1:])
                response_template = commands[rp_command]
                final_response = response_template.replace("{user}", user_name).replace("{target}", target_name)
                final_response = self._apply_emoji(final_response)
                parsed = parse_markdown(final_response)
                params.message = parsed.text
                for ent in parsed.entities:
                    params.entities.add(ent.to_tlrpc_object())
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

        except Exception as e:
            self.log(f"Error in on_send_message_hook: {e}")
            return HookResult()
        return HookResult()

    # -------- Update processing (commands from others) ---------

    def _extract_message(self, update_obj):
        """Return (text, chat_id, sender_id) or None"""
        my_id = get_user_config().getClientUserId()

        if not hasattr(update_obj, 'message'):
            return None

        # Case1: simple str field (TL_updateShortMessage etc.)
        if isinstance(update_obj.message, str):
            msg_text = update_obj.message
            # chat_id & sender
            if isinstance(update_obj, TLRPC.TL_updateShortMessage):
                chat_id = update_obj.user_id
                sender_id = my_id if update_obj.out else update_obj.user_id
            elif isinstance(update_obj, TLRPC.TL_updateShortChatMessage):
                chat_id = -update_obj.chat_id
                sender_id = update_obj.from_id
            else:
                return None
            return msg_text, chat_id, sender_id

        # Case2: full message object
        if isinstance(update_obj.message, TLRPC.Message):
            msg_obj = update_obj.message
            msg_text = msg_obj.message

            peer = msg_obj.peer_id if hasattr(msg_obj, 'peer_id') else None
            if isinstance(peer, TLRPC.TL_peerUser):
                chat_id = peer.user_id
            elif isinstance(peer, TLRPC.TL_peerChat):
                chat_id = -peer.chat_id
            elif isinstance(peer, TLRPC.TL_peerChannel):
                chat_id = -peer.channel_id
            else:
                return None

            if hasattr(msg_obj, 'out') and msg_obj.out:
                sender_id = my_id
            else:
                from_peer = msg_obj.from_id if hasattr(msg_obj, 'from_id') else None
                if isinstance(from_peer, TLRPC.TL_peerUser):
                    sender_id = from_peer.user_id
                elif isinstance(from_peer, TLRPC.TL_peerChannel):
                    sender_id = from_peer.channel_id
                else:
                    sender_id = 0
            return msg_text, chat_id, sender_id

        return None

    def _get_display_name(self, user_id: int) -> str:
        """Возвращает красивое имя пользователя для вставки в Markdown."""
        names = json.loads(self.get_setting(KEY_RP_NAMES, "{}"))
        if str(user_id) in names:
            rp_name = names[str(user_id)]
            return f"[{rp_name}](tg://user?id={user_id})"

        user = get_messages_controller().getUser(user_id)
        if user:
            first = getattr(user, "first_name", None)
            if first:
                return f"[{first}](tg://user?id={user_id})"
            if getattr(user, "username", None):
                return f"@{user.username}"
            # Иногда first_name может быть пустым (у ботов)
            return f"[id{user_id}](tg://user?id={user_id})"
        return f"[id{user_id}](tg://user?id={user_id})"

    def _get_target_display_name(self, chat_id: int, reply_obj=None, reply_id=None):
        """Пытается извлечь пользователя цели и вернуть его имя."""
        target_id = None
        if reply_obj is not None and hasattr(reply_obj, "messageOwner"):
            mo = reply_obj.messageOwner
            peer = getattr(mo, "from_id", None)
            if isinstance(peer, TLRPC.TL_peerUser):
                target_id = peer.user_id
            elif isinstance(peer, TLRPC.TL_peerChannel):
                target_id = peer.channel_id
            elif isinstance(peer, int):
                target_id = peer
        elif reply_id:
            msg = get_messages_storage().getMessage(chat_id, reply_id)
            if msg and hasattr(msg, "from_id"):
                peer = msg.from_id
                if isinstance(peer, TLRPC.TL_peerUser):
                    target_id = peer.user_id
                elif isinstance(peer, TLRPC.TL_peerChannel):
                    target_id = peer.channel_id
                elif isinstance(peer, int):
                    target_id = peer
        if target_id:
            return self._get_display_name(target_id)
        return "себя"

    def _process_update(self, update_obj):
        try:
            # If this is a container with multiple updates (TLRPC.Updates)
            if hasattr(update_obj, 'updates') and update_obj.updates is not None:
                for upd in update_obj.updates:
                    self._process_update(upd)  # recurse
                return

            data = self._extract_message(update_obj)
            if not data:
                return

            message_text, chat_id, sender_id = data

            message_text = message_text.strip()
            parts = message_text.split()
            if not parts:
                return

            cmds = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
            allow_plain = self.get_setting(KEY_PLAIN_ENABLED, False)
            first_word = parts[0].lower()
            is_plain_trigger = allow_plain and first_word in cmds

            if not message_text.startswith('.') and not is_plain_trigger:
                return

            owner_id = get_user_config().getClientUserId()

            if not self._is_chat_allowed(chat_id):
                return

            parts = message_text.split()
            command = parts[0].lower() if message_text.startswith('.') else '.' + parts[0].lower()

            # ----- admin only cmds -----
            if command == '.rpadd' and self._is_owner(sender_id) and len(parts) >= 3:
                rp_command, rp_text = parts[1], ' '.join(parts[2:])
                cmds = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
                cmds[rp_command] = rp_text
                self.set_setting(KEY_RP_COMMANDS, json.dumps(cmds))
                self._send_md(chat_id, f"✅ *Триггер* `{rp_command}` *добавлен*.")
                return
            if command == '.rpdel' and self._is_owner(sender_id) and len(parts) >= 2:
                rp_command = parts[1]
                cmds = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
                if rp_command in cmds:
                    del cmds[rp_command]
                    self.set_setting(KEY_RP_COMMANDS, json.dumps(cmds))
                    self._send_md(chat_id, f"🗑️ *Триггер* `{rp_command}` *удалён*.")
                return

            # ----- user name -----
            if command == '.rpname' and len(parts) >= 2:
                name_arg = ' '.join(parts[1:])
                names = json.loads(self.get_setting(KEY_RP_NAMES, "{}"))
                if name_arg.lower() == 'remove':
                    names.pop(str(sender_id), None)
                    notice = "♻️ Имя сброшено."
                else:
                    names[str(sender_id)] = name_arg
                    notice = f"👤 Теперь твоё RP-имя: *{name_arg}*"
                self.set_setting(KEY_RP_NAMES, json.dumps(names))
                self._send_md(chat_id, notice)
                return

            # ----- play command -----
            rp_command = command[1:]
            cmds = json.loads(self.get_setting(KEY_RP_COMMANDS, "{}"))
            if rp_command not in cmds:
                return

            # owner-only mode check
            if self.get_setting(KEY_OWNER_ONLY, True) and not self._is_owner(sender_id):
                return

            user_name = self._get_display_name(sender_id)
            # определяем цель по реплаю
            reply_id = None
            if isinstance(update_obj, (TLRPC.TL_updateShortMessage, TLRPC.TL_updateShortChatMessage)):
                reply_id = getattr(update_obj, 'reply_to_msg_id', 0) or None
            elif hasattr(update_obj, 'message') and isinstance(update_obj.message, TLRPC.Message):
                reply_id = getattr(update_obj.message, 'reply_to_msg_id', 0) or None

            target_name = self._get_target_display_name(chat_id, None, reply_id)

            text_template = cmds[rp_command]
            rp_text = text_template.replace('{user}', user_name).replace('{target}', target_name)
            rp_text = self._apply_emoji(rp_text)
            self._send_md(chat_id, rp_text)
        except Exception as e:
            log(f"RolePlayPlugin update error: {e}")

    def on_update_hook(self, update_name, account, update) -> HookResult:
        # process in UI thread to access utils safely
        run_on_ui_thread(lambda: self._process_update(update))
        return HookResult()

    def on_updates_hook(self, container_name, account, updates_container) -> HookResult:
        run_on_ui_thread(lambda: self._process_update(updates_container))
        return HookResult()

    def _send_md(self, chat_id: int, text: str):
        """Отправляет сообщение, корректно обрабатывая Markdown-разметку."""
        text = self._apply_emoji(text)
        parsed = parse_markdown(text)
        params = {"peer": chat_id, "message": parsed.text}
        if parsed.entities:
            params["entities"] = [e.to_tlrpc_object() for e in parsed.entities]
        send_message(params)

    def _is_chat_allowed(self, chat_id: int) -> bool:
        """Returns True if commands are allowed in this chat."""
        if not self.get_setting(KEY_WHITELIST_ENABLED, False):
            return True
        raw = self.get_setting(KEY_WHITELIST_CHATS, "")
        allowed = {int(c.strip()) for c in raw.split(',') if c.strip().lstrip('-').isdigit()}
        return chat_id in allowed

    @staticmethod
    def _is_owner(user_id: int) -> bool:
        return user_id == get_user_config().getClientUserId()

    # --- Helpers ---
    def _apply_emoji(self, text: str) -> str:
        """Prepend random emoji if feature enabled."""
        if not self.get_setting(KEY_EMOJI_ENABLED, False):
            return text
        raw = self.get_setting(KEY_EMOJI_LIST, "")
        pool = [e for e in raw.split() if e]
        if not pool:
            return text
        return f"{random.choice(pool)} {text}"
