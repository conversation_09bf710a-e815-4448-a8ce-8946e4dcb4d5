
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.alert import Alert<PERSON>ialogBuilder
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
from hook_utils import find_class
from java import dynamic_proxy
import ast
import operator
from decimal import Decimal, getcontext

getcontext().prec = 30

TextView = find_class("android.widget.TextView")
GridLayout = find_class("android.widget.GridLayout")
LinearLayout = find_class("android.widget.LinearLayout")
EditText = find_class("android.widget.EditText")
Button = find_class("android.widget.Button")
Gravity = find_class("android.view.Gravity")
OnClickListenerInterface = find_class("android.view.View$OnClickListener")
GridLayoutLayoutParams = find_class("android.widget.GridLayout$LayoutParams")
AndroidUtilities = find_class("org.telegram.messenger.AndroidUtilities")
InputType = find_class("android.text.InputType")
HapticFeedbackConstants = find_class("android.view.HapticFeedbackConstants")
LinearLayoutLayoutParams = find_class("android.widget.LinearLayout$LayoutParams")
Typeface = find_class("android.graphics.Typeface")  
Color = find_class("android.graphics.Color")  

__id__ = "calculator with UI interface"
__name__ = "calculator with UI interface"
__description__ = "calculator with UI interface"
__author__ = "@SaturnFake"
__version__ = "1.1.6"
__min_version__ = "11.12.0"
__icon__ = "Plugins_Test/0"

OPERATORS = {
    ast.Add: operator.add,
    ast.Sub: operator.sub,
    ast.Mult: operator.mul,
    ast.Div: operator.truediv,
    ast.Pow: operator.pow,
    ast.USub: operator.neg,
}


def safe_eval(expression):
    try:
        tree = ast.parse(expression, mode="eval")
    except SyntaxError:
        raise ValueError("Invalid syntax")

    def eval_tree(node):
        if isinstance(node, ast.Num):
            return node.n
        elif isinstance(node, ast.BinOp):
            op = OPERATORS.get(type(node.op))
            if op is None:
                raise ValueError("Unsupported operator")
            return op(eval_tree(node.left), eval_tree(node.right))
        elif isinstance(node, ast.UnaryOp):
            op = OPERATORS.get(type(node.op))
            if op is None:
                raise ValueError("Unsupported operator")
            return op(eval_tree(node.operand))
        else:
            raise ValueError("Unsupported expression type")

    return eval_tree(tree.body)


class CalcButtonClickListener(dynamic_proxy(OnClickListenerInterface)):
    def __init__(self, plugin, input_text_view, result_text_view, button_text):
        super().__init__()
        self.plugin = plugin
        self.input_text_view = input_text_view
        self.result_text_view = result_text_view 
        self.button_text = button_text

    def onClick(self, view):
        view.performHapticFeedback(HapticFeedbackConstants.KEYBOARD_TAP)
        self.plugin.on_button_click(
            self.input_text_view, self.result_text_view, self.button_text
        )  


class CalcPlugin(BasePlugin):
    def __init__(self):
        super().__init__()

    def on_plugin_load(self):
        self.add_menu_item(
            MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text="Калькулятор",
                icon="Plugins_Test/0",
                on_click=self.open_calculator,
            )
        )

    def open_calculator(self, context):
        chat_id = context.get("dialog_id") or context.get("chatId")
        run_on_ui_thread(lambda: self.show_calculator_dialog(chat_id))

    def show_calculator_dialog(self, chat_id):
        fragment = get_last_fragment()
        activity = fragment and fragment.getParentActivity()
        if not activity:
            return

        builder = AlertDialogBuilder(activity)
        builder.set_title("Калькулятор")
        main_layout = LinearLayout(activity)
        main_layout.setOrientation(LinearLayout.VERTICAL)
        input_text_view = self.create_input_text_view(activity)
        main_layout.addView(input_text_view)
        result_text_view = self.create_result_text_view(activity)
        main_layout.addView(result_text_view)
        button_layout = self.create_button_layout(
            activity, input_text_view, result_text_view
        )  
        main_layout.addView(button_layout)
        builder.set_view(main_layout)
        builder.set_negative_button("Закрыть", lambda b, w: b.dismiss())
        builder.show()
    def create_input_text_view(self, activity):
        editTextParams = LinearLayoutLayoutParams(
            LinearLayoutLayoutParams.MATCH_PARENT, LinearLayoutLayoutParams.WRAP_CONTENT
        )
        input_text_view = EditText(activity)
        input_text_view.setHint("0")
        input_text_view.setTextSize(24)
        input_text_view.setGravity(Gravity.END)
        input_text_view.setInputType(InputType.TYPE_NULL)
        input_text_view.setFocusable(True)
        input_text_view.setFocusableInTouchMode(True)
        input_text_view.setLayoutParams(editTextParams)
        input_text_view.setTypeface(Typeface.DEFAULT_BOLD)
        input_text_view.setTextColor(Color.WHITE)
        return input_text_view

    def create_result_text_view(self, activity):
        textViewParams = LinearLayoutLayoutParams(
            LinearLayoutLayoutParams.MATCH_PARENT, LinearLayoutLayoutParams.WRAP_CONTENT
        )
        textViewParams.setMargins(
            AndroidUtilities.dp(8), 0, AndroidUtilities.dp(8), 0
        )

        result_text_view = TextView(activity)
        result_text_view.setTextSize(24)
        result_text_view.setGravity(Gravity.END)
        result_text_view.setLayoutParams(textViewParams)
        result_text_view.setTypeface(Typeface.DEFAULT_BOLD) 
        return result_text_view  
    def create_button_layout(self, activity, input_text_view, result_text_view): 
        gridLayoutParams = LinearLayoutLayoutParams(
            LinearLayoutLayoutParams.WRAP_CONTENT, LinearLayoutLayoutParams.WRAP_CONTENT
        )
        gridLayoutParams.gravity = Gravity.CENTER_HORIZONTAL
        button_layout = GridLayout(activity)
        button_layout.setColumnCount(4)
        button_layout.setLayoutParams(gridLayoutParams)

        buttons = [
            "AC",
            "%",
            "⌦",
            "/",
            "7",
            "8",
            "9",
            "*",
            "4",
            "5",
            "6",
            "-",
            "1",
            "2",
            "3",
            "+",
            "00",
            "0",
            ".",
            "=",
        ] 
        row = 0
        col = 0
        button_size = AndroidUtilities.dp(72)
        margin = AndroidUtilities.dp(2)

        for button_text in buttons:
            button = self.create_button(
                activity,
                input_text_view,
                result_text_view,
                button_text,
                button_size,
                margin,
                row,
                col,
            ) 
            button_layout.addView(button)
            col += 1
            if col > 3:
                col = 0
                row += 1

        return button_layout

    def create_button(
        self,
        activity,
        input_text_view,
        result_text_view,
        button_text,
        button_size,
        margin,
        row,
        col,
        column_span=1,
    ):  
        button = Button(activity)
        button.setText(button_text)
        button.setTextSize(20)
        button.setOnClickListener(
            CalcButtonClickListener(
                self, input_text_view, result_text_view, button_text
            )
        )  

        params = GridLayoutLayoutParams()
        params.width = button_size
        params.height = AndroidUtilities.dp(72)
        params.setMargins(margin, margin, margin, margin)
        params.rowSpec = GridLayout.spec(row)
        params.columnSpec = GridLayout.spec(col, column_span)
        button.setLayoutParams(params)
        return button

    def on_button_click(self, input_text_view, result_text_view, button_text):  
        current_text = input_text_view.getText().toString()

        if button_text == "=":
            try:
                result = str(Decimal(str(safe_eval(current_text))))
                input_text_view.setText(result)
                result_text_view.setText("")  

            except Exception as e:
                input_text_view.setText("Ошибка: " + str(e)) 
                result_text_view.setText("")  
        elif button_text == "AC":
            input_text_view.setText("")
            result_text_view.setText("")
            input_text_view.setHint("0")  
            input_text_view.setTextColor(Color.WHITE)  
        elif button_text == "⌦":
            if current_text:  
                new_text = current_text[:-1]
                input_text_view.setText(new_text)
        elif button_text == "%":
            try:
                result = str(Decimal(str(safe_eval(current_text + "/100"))))
                input_text_view.setText(result)
                result_text_view.setText("")
            except Exception as e:
                input_text_view.setText("Ошибка: " + str(e))  
                result_text_view.setText("")

        else:
        
            if (
                current_text
                and button_text in ["+", "-", "*", "/"]
                and current_text[-1] in ["+", "-", "*", "/"]
            ):
                return
            new_text = current_text + button_text
            input_text_view.setText(new_text)
            input_text_view.setHint("")

        if not input_text_view.getText().toString():  
            input_text_view.setTextColor(Color.WHITE)
            input_text_view.setHint("0")

        try:
            result = str(Decimal(str(safe_eval(input_text_view.getText().toString()))))
            result_text_view.setText("= " + result)
        except:
            result_text_view.setText("")

        if not input_text_view.getText().toString():
            result_text_view.setText("")
