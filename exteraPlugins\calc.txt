from base_plugin import BasePlugin, HookR<PERSON><PERSON>, HookStrategy
from ui.bulletin import BulletinHelper
import math

__id__ = "inline_calculator"
__name__ = "Inline Calculator"
__description__ = "!calc [expression] — evaluates a mathematical expression."
__version__ = "1.0.1"
__author__ = "immat0x1"
__min_version__ = "11.12.0"

ALLOWED_NAMES = {k: v for k, v in math.__dict__.items() if not k.startswith("__")}

SAFE_EVAL_GLOBALS = {"__builtins__": None}
SAFE_EVAL_GLOBALS.update(ALLOWED_NAMES)
SAFE_EVAL_LOCALS = {}


class CalculatorPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        command_prefix = "!calc"
        msg = params.message.strip()
        if msg.startswith(command_prefix):
            expression = msg[len(command_prefix):].strip()
            if not expression and "\n" in msg:
                lines = msg.splitlines()
                if len(lines) > 1:
                    expression = "\n".join(lines[1:]).strip()
            if not expression:
                BulletinHelper.show_error("No expression provided after !calc.")
                return HookResult(strategy=HookStrategy.CANCEL)
            try:
                result = eval(expression, SAFE_EVAL_GLOBALS, SAFE_EVAL_LOCALS)
                output_message = f"{expression} = {str(result)}"
                params.message = output_message
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            except SyntaxError:
                BulletinHelper.show_error("Calculation Error: Invalid syntax.")
                return HookResult(strategy=HookStrategy.CANCEL)
            except NameError as e:
                name = None
                try:
                    name = str(e).split("'")[1]
                except Exception:
                    name = "unknown"
                BulletinHelper.show_error(
                    "Calculation Error: Unknown name '{}'. Only math functions/constants allowed.".format(
                        name
                    )
                )
                return HookResult(strategy=HookStrategy.CANCEL)
            except ZeroDivisionError:
                BulletinHelper.show_error("Calculation Error: Division by zero.")
                return HookResult(strategy=HookStrategy.CANCEL)
            except Exception as e:
                BulletinHelper.show_error(
                    f"Calculation Error: {type(e).__name__}. Please check your expression."
                )
                return HookResult(strategy=HookStrategy.CANCEL)

        return HookResult()
