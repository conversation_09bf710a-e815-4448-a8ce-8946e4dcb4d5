import os
import uuid
import requests
from java.util import Locale, ArrayList
from org.telegram.ui.ActionBar import AlertDialog
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Switch, Input
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from android_utils import run_on_ui_thread
from client_utils import get_send_messages_helper, get_last_fragment, run_on_queue
from java.io import File
from org.telegram.messenger import ApplicationLoader
from markdown_utils import parse_markdown
from ui.bulletin import BulletinHelper

__id__ = "cat"
__name__ = "Cat"
__description__ = "Send a random cat image with .cat"
__author__ = "@exteraDev"
__min_version__ = "11.9.0"
__icon__ = "exteraDevPlugins/9"

CAT_API_URL = "https://api.thecatapi.com/v1/images/search"
CAT_API_KEY = "ylX4blBYT9FaoVd6OhvR"

class CatPlugin(BasePlugin):
    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("pt"):
            header = "Configurações do Cat"
            usage = "Uso: .cat"
            disclaimer = (
                "⚠️ Para mostrar informações detalhadas do gato, "
                "você precisa inserir uma chave de API válida, obtida em https://thecatapi.com/"
            )
            caption_switch = "Mostrar informações na legenda"
            caption_sub = "Ative para mostrar informações do gato"
            breed_switch = "Exibir raça"
            origin_switch = "Exibir origem"
            temperament_switch = "Exibir temperamento"
            life_span_switch = "Exibir expectativa de vida"
            weight_switch = "Exibir peso"
            description_switch = "Exibir descrição"
        elif lang.startswith("ru"):
            header = "Настройки Cat"
            usage = "Использование: .cat"
            disclaimer = (
                "⚠️ Для отображения подробной информации о коте "
                "необходимо ввести действующий API-ключ, полученный на https://thecatapi.com/"
            )
            caption_switch = "Показать информацию в подписи"
            caption_sub = "Включить для отображения информации о коте"
            breed_switch = "Показать породу"
            origin_switch = "Показать происхождение"
            temperament_switch = "Показать темперамент"
            life_span_switch = "Показать продолжительность жизни"
            weight_switch = "Показать вес"
            description_switch = "Показать описание"
        else:
            header = "Cat Settings"
            usage = "Usage: .cat"
            disclaimer = (
                "⚠️ To show detailed cat info, you must enter a valid API key from https://thecatapi.com/"
            )
            caption_switch = "Show information in caption"
            caption_sub = "Enable to show cat info"
            breed_switch = "Show breed"
            origin_switch = "Show origin"
            temperament_switch = "Show temperament"
            life_span_switch = "Show life span"
            weight_switch = "Show weight"
            description_switch = "Show description"

        show_caption = self.get_setting("show_caption", False)
        settings = [
            Header(text=header),
            Divider(text=disclaimer),
            Input(key="api_key", text="API Key", icon="msg2_permissions", default=CAT_API_KEY, subtext="TheCatAPI Key"),
            Switch(key="show_caption", text=caption_switch, icon="msg_markread", default=False, subtext=caption_sub),
        ]
        if show_caption:
            settings += [
                Switch(key="show_breed", text=breed_switch, default=False),
                Switch(key="show_origin", text=origin_switch, default=False),
                Switch(key="show_temperament", text=temperament_switch, default=False),
                Switch(key="show_life_span", text=life_span_switch, default=False),
                Switch(key="show_weight", text=weight_switch, default=False),
                Switch(key="show_description", text=description_switch, default=False),
            ]
        settings.append(Divider(text=usage))
        return [s for s in settings if s is not None]

    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_plugin_unload(self):
        self.remove_on_send_message_hook()

    def _get_caption_and_entities(self, data):
        show_caption = self.get_setting("show_caption", False)
        if not show_caption:
            return None, None
        show_breed = self.get_setting("show_breed", False)
        show_origin = self.get_setting("show_origin", False)
        show_temperament = self.get_setting("show_temperament", False)
        show_life_span = self.get_setting("show_life_span", False)
        show_weight = self.get_setting("show_weight", False)
        show_description = self.get_setting("show_description", False)
        breeds = data.get("breeds", [])
        breed = breeds[0] if breeds else {}
        caption_lines = []

        if show_breed and breed.get("name"):
            caption_lines.append(f"*{breed['name']}*")
        if show_origin and breed.get("origin"):
            caption_lines.append(f"🌍 *Origin*: {breed['origin']}")
        if show_temperament and breed.get("temperament"):
            caption_lines.append(f"*Temperament*: {breed['temperament']}")
        if show_life_span and breed.get("life_span"):
            caption_lines.append(f"*Life*: {breed['life_span']} years")
        if show_weight and breed.get("weight", {}).get("metric"):
            caption_lines.append(f"*Weight*: {breed['weight']['metric']} kg")
        if show_description and breed.get("description"):
            caption_lines.append(f"*Description*: {breed['description']}")
        if not caption_lines:
            return None, None
        caption = "\n".join(caption_lines)
        parsed = parse_markdown(caption)
        arr = ArrayList()
        for ent in parsed.entities:
            arr.add(ent.to_tlrpc_object())
        return parsed.text, arr

    def _download_cat_image(self, params, progress_dialog):
        try:
            api_key = self.get_setting("api_key", CAT_API_KEY)
            headers = {"x-api-key": api_key}
            resp = requests.get(
                CAT_API_URL + "?limit=1&has_breeds=1&api_key=" + api_key + "&include_breeds=1",
                headers=headers,
                timeout=10
            )
            if resp.status_code != 200:
                BulletinHelper.show_error("Failed to fetch cat image")
                if progress_dialog: progress_dialog.dismiss()
                return
            items = resp.json()
            if not items or not items[0].get("url"):
                BulletinHelper.show_error("No image found")
                if progress_dialog: progress_dialog.dismiss()
                return
            data = items[0]
            img_url = data["url"]
            img_resp = requests.get(img_url, stream=True, timeout=15)
            if img_resp.status_code != 200:
                BulletinHelper.show_error("Failed to download image")
                if progress_dialog: progress_dialog.dismiss()
                return
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "exteraGram CatImages")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            ext = img_url.split(".")[-1].split("?")[0]
            filename = f"cat_{uuid.uuid4()}.{ext}"
            temp_path = File(temp_dir, filename).getAbsolutePath()
            with open(temp_path, "wb") as f:
                for chunk in img_resp.iter_content(8192):
                    if chunk:
                        f.write(chunk)
            if not os.path.exists(temp_path) or os.path.getsize(temp_path) == 0:
                BulletinHelper.show_error("Image file not created")
                if progress_dialog: progress_dialog.dismiss()
                return
            send_helper = get_send_messages_helper()
            generated_photo = send_helper.generatePhotoSizes(temp_path, None)
            if not generated_photo:
                BulletinHelper.show_error("Failed to process image")
                os.remove(temp_path)
                if progress_dialog: progress_dialog.dismiss()
                return
            params.photo = generated_photo
            params.path = temp_path
            caption, entities = self._get_caption_and_entities(data)
            if caption:
                params.caption = caption
                params.entities = entities if entities is not None else ArrayList()
            params.message = None
            if not hasattr(params, "entities") or params.entities is None:
                params.entities = ArrayList()
            run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))
            def cleanup():
                import time
                time.sleep(10)
                try:
                    os.remove(temp_path)
                except Exception:
                    pass
            run_on_queue(cleanup)
        except Exception as e:
            BulletinHelper.show_error(f"Error: {e}")
        finally:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()
        msg = params.message.strip()
        if not msg.startswith(".cat"):
            return HookResult()
        try:
            progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
            progress_dialog.setMessage("Fetching a cat...")
            progress_dialog.show()
        except Exception:
            progress_dialog = None
        run_on_queue(lambda: self._download_cat_image(params, progress_dialog))
        return HookResult(strategy=HookStrategy.CANCEL)