from ui.settings import Header, Input, Divider
from base_plugin import BasePlugin, Hook<PERSON><PERSON><PERSON>, HookStrategy
from android_utils import log
from org.telegram.tgnet import TLRPC
from com.google.gson import Gson
import unicodedata

__id__ = "point"
__name__ = "Точка и Заглавные Первые Буквы"
__description__ = "Ставит точку в конце сообщенияи делает заглавной первую букву каждого предложения"
__author__ = "@Darksourse399"
__version__ = "1.1 FIX"
__min_version__ = "11.9.0"


  
        

class PointSentenceCasePlugin(BasePlugin):

    def on_plugin_load(self):
        log("EXTERAMSG PointSentenceCase Plugin loaded")
        self.add_on_send_message_hook()
    def on_plugin_unload(self):
        log("EXTERAMSG PointSentenceCase Plugin unloaded")

    def is_emoji(self, char):
        return any([
            '\U0001F600' <= char <= '\U0001F64F',
            '\U0001F300' <= char <= '\U0001F5FF',
            '\U0001F680' <= char <= '\U0001F6FF',
            '\U0001F700' <= char <= '\U0001F77F',
            '\U0001F780' <= char <= '\U0001F7FF',
            '\U0001F800' <= char <= '\U0001F8FF',
            '\U0001F900' <= char <= '\U0001F9FF',
            '\U0001FA00' <= char <= '\U0001FA6F',
            '\U0001FA70' <= char <= '\U0001FAFF',
            '\U00002702' <= char <= '\U000027B0',
            '\U000024C2' <= char <= '\U0001F251'
        ])

    def on_send_message_hook(self, account, params):
        try:
            if not hasattr(params, "message") or not isinstance(params.message, str):
                return HookResult()

            message_text = params.message.strip()
            if not message_text:
                return HookResult()

            sentence_end_marks = {'.', '!', '?', '…'}
            result = ""
            capitalize_next = True
            is_command = message_text.startswith('/')

            for char in message_text:
                if capitalize_next and char.isalpha():
                    result += char.upper()
                    capitalize_next = False
                else:
                    result += char.lower()
                if char in sentence_end_marks:
                    capitalize_next = True
                elif char in {',', ':', '-', '#', '/', '"'}:
                    capitalize_next = False

            last_char = result[-1] if result else ''
            is_special = not last_char.isalnum() and not self.is_emoji(last_char)

            if result and last_char not in sentence_end_marks and not is_command and not self.is_emoji(last_char) and not is_special:
                result += ""
                if result[-1] not in sentence_end_marks:
                    result += "."

            params.message = result
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        except Exception as e:
            log(f"EXTERAMSG PointSentenceCase Plugin Error: {str(e)}")
            return HookResult()
