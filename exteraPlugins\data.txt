import datetime
import calendar

from ui.settings import Head<PERSON>, Switch, Di<PERSON>r, Input, Selector
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy

__id__ = "birthday_time"
__name__ = "BirthdayTime"
__version__ = "1.2.0"
__description__ = "Показывает количество дней до твоего дня рождения."
__author__ = "@extera_plugin"
__min_version__ = "11.9.0"
__icon__ = "SpottyAnimated/39"


class BirthdayTimePlugin(BasePlugin):
    def create_settings(self):
        return [
            Header(text="Настройки плагина \"Дата рождения\""),
            Input(
                key="birthday_date",
                text="Дата рождения",
                default="",
                subtext="Укажите дату рождения в формате ММ.ДД (например, 10.25)",
            ),
            Divider(text=".dt - узнать количество дней до дня рождения"),
        ]

    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def get_time_units_string(self, value: int, unit: str) -> str:
        if unit == "день":
            if value % 10 == 1 and value % 100 != 11:
                return f"{value} день"
            elif 2 <= value % 10 <= 4 and (value % 100 < 10 or value % 100 >= 20):
                return f"{value} дня"
            else:
                return f"{value} дней"
        elif unit == "час":
            if value % 10 == 1 and value % 100 != 11:
                return f"{value} час"
            elif 2 <= value % 10 <= 4 and (value % 100 < 10 or value % 100 >= 20):
                return f"{value} часа"
            else:
                return f"{value} часов"
        elif unit == "минута":
            if value % 10 == 1 and value % 100 != 11:
                return f"{value} минута"
            elif 2 <= value % 10 <= 4 and (value % 100 < 10 or value % 100 >= 20):
                return f"{value} минуты"
            else:
                return f"{value} минут"
        elif unit == "секунда":
            if value % 10 == 1 and value % 100 != 11:
                return f"{value} секунда"
            elif 2 <= value % 10 <= 4 and (value % 100 < 10 or value % 100 >= 20):
                return f"{value} секунды"
            else:
                return f"{value} секунд"
        return ""

    def get_formatted_birthday_message(self, birth_date_str: str) -> str:
        now = datetime.datetime.now()

        try:
            birth_month, birth_day = map(int, birth_date_str.split("."))
        except ValueError:
            return "Ошибка: Неверный формат даты. Используйте MM.DD (пример: 12.25)."

        try:
            birthday = datetime.datetime(now.year, birth_month, birth_day)
        except ValueError:
            return "Ошибка: Неверная дата (например, день вне диапазона для месяца)."

        if now.month > birth_month or (now.month == birth_month and now.day > birth_day):
            birthday = datetime.datetime(now.year + 1, birth_month, birth_day)

        time_to_birthday = birthday - now

        days = time_to_birthday.days
        hours = time_to_birthday.seconds // 3600
        minutes = (time_to_birthday.seconds // 60) % 60
        seconds = time_to_birthday.seconds % 60

        days_str = self.get_time_units_string(days, "день")
        hours_str = self.get_time_units_string(hours, "час")
        minutes_str = self.get_time_units_string(minutes, "минута")
        seconds_str = self.get_time_units_string(seconds, "секунда")

        return f"До вашего дня рождения осталось: {days_str}, {hours_str}, {minutes_str}, {seconds_str}."

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()

        command_prefix = ".dt"
        if params.message.startswith(command_prefix):

            birth_date_str = self.get_setting("birthday_date")
            text = self.get_formatted_birthday_message(birth_date_str)
            params.message = text

            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        return HookResult()