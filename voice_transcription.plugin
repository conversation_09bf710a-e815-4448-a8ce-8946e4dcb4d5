import os
import time
import base64
import requests
import traceback
import threading
import re
from typing import Any, Dict, Op<PERSON>, Tu<PERSON>

from base_plugin import BasePlugin, MenuItemData, MenuItemType
from client_utils import get_file_loader, get_last_fragment
from ui.settings import Header, Input, Switch, Text, Divider
from ui.bulletin import BulletinHelper
from ui.alert import AlertDialogBuilder
from android_utils import run_on_ui_thread, log
from java.util import Locale
from org.telegram.messenger import MessageObject, FileLoader, ApplicationLoader

__id__ = "voice_transcription"
__name__ = "Voice Transcription"
__description__ = "Transcribe voice messages and audio using Google Gemini API"
__author__ = "exteraGram Dev"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent"

SYSTEM\_PROMPT\_AUDIO\_VIDEO\_SUMMARY\_GEMINI = """
Ты обрабатываешь аудио/видео от пользователя {forwarder\_name}. Твоя задача — создать понятные и полезные сводки на основе прикреплённого аудио.

Сделай 3 блока:

**СТИЛЬ И ФОРМАТИРОВАНИЕ:**
• Абзацы через `\n\n`, строки через `\n`
• БЕЗ таблиц — только текст

**ЧТО ДЕЛАТЬ:**

**1. Краткая сводка:**
• 1–8 главных тезисов
• Каждый с новой строки: `- ` + суть тезиса
• Выделяй ключевые моменты через `<b>текст</b>`
• Если нечего сократить: "\[краткая сводка недоступна]"

**2. Подробная сводка:**
• Развернутые тезисы по темам
• Формат каждого тезиса: `- ` + `<b>Заголовок</b>` + описание (1–3 предложения)
• Разделяй тезисы через `\n\n`
• Если нечего расписать: "\[подробная сводка недоступна]"

**3. Отформатированная транскрипция:**
• Текст из аудио + знаки препинания + абзацы
• Не добавляй содержание, только улучшай форму
• Если пусто: "\[транскрипция недоступна]"

**ФОРМАТ ВЫВОДА:**

<\<SHORT\_SUMMARY\_N\_START>>

* Основная <b>тема</b> обсуждения
* Ключевая <b>идея</b> или решение
  <\<SHORT\_SUMMARY\_N\_END>>

<\<DETAILED\_SUMMARY\_N\_START>>

* <b>Основная тема:</b>
  Подробное описание главной темы разговора с важными деталями.

* <b>Ключевые решения:</b>
  Конкретные выводы или планы, которые были обсуждены.
  <\<DETAILED\_SUMMARY\_N\_END>>

<\<FORMATTED\_TRANSCRIPT\_1\_START>>
Красиво отформатированный текст из прикреплённого аудио с правильными знаками препинания и абзацами.
<\<FORMATTED\_TRANSCRIPT\_1\_END>>

{transcripts\_block}

Без "---" разделителей, БЕЗ ТАБЛИЦ.
Выполни следующие задачи СТРОГО ПО ИНСТРУКЦИИ:

**ОБЩИЕ ТРЕБОВАНИЯ К ТЕКСТУ:**

* Используй только стандартные кириллические и латинские символы. Избегай необычных или нестандартных вариаций букв (например, всегда пиши "Gemini", а не "Geminі").
* **Списки**: оформляй построчно, начиная каждую строку с дефиса `- `. БЕЗ эмодзи.
* **Абзацы**: двойной перенос строки (`\n\n`) между абзацами.

**1. Краткая сводка:**

* Напиши ОЧЕНЬ КРАТКУЮ сводку (обычно 1–8 тезисов).
* Каждый тезис должен начинаться с новой строки с дефиса `- `, затем пробел и формулировка тезиса. Тезис должен быть ёмким и передавать основную мысль.
* Если краткую сводку составить невозможно или текст слишком короткий/неинформативный, вставь текст "\[краткая сводка недоступна]".

**2. Подробная сводка:**

* Напиши ПОДРОБНУЮ сводку, раскрывающую основные темы и детали из расшифровки.
* Структурируй подробную сводку по ТЕЗИСАМ. Каждый тезис должен освещать один ключевой аспект или тему сообщения.
* Каждый тезис начинай с дефиса `- ` и укажи `<b>ЗАГОЛОВОК</b>` (1–3 слова), далее — описание на той же строке или со следующей строки (используй `\n`).
* Разделяй разные тезисы двойным переносом строки (`\n\n`) для лучшей читаемости.
* Если подробную сводку составить невозможно, вставь текст "\[подробная сводка недоступна]".

**3. Отформатированная расшифровка:**

* Тщательно отформатируй ИСХОДНУЮ расшифровку, добавив знаки препинания (запятые, точки, вопросительные/восклицательные знаки), разбей на предложения и абзацы (используй `\n\n` для разделения абзацев) для лучшей читаемости.
* Сохрани исходный смысл и стиль речи. Не добавляй ничего от себя, кроме форматирования.
* Если исходная расшифровка пуста или содержит только ошибки, вставь текст "\[расшифровка недоступна]".

КРАЙНЕ ВАЖНО: Твой ответ ДОЛЖЕН содержать ТОЛЬКО ТРИ БЛОКА для КАЖДОЙ расшифровки, используя следующие теги-разделители. Замени N на номер расшифровки (начиная с 1).
ЗАПРЕЩЕНО добавлять любой другой текст, вступления, пояснения или комментарии вне этих блоков.
КАЖДЫЙ ИЗ ТРЕХ БЛОКОВ ДОЛЖЕН ПРИСУТСТВОВАТЬ В ОТВЕТЕ ВСЕГДА для каждой расшифровки.

Формат вывода для КАЖДОЙ расшифровки (замени N на номер расшифровки):

<\<SHORT\_SUMMARY\_N\_START>>
текст краткой сводки для расшифровки N.
<\<SHORT\_SUMMARY\_N\_END>>

<\<DETAILED\_SUMMARY\_N\_START>>
текст подробной сводки для расшифровки N.
<\<DETAILED\_SUMMARY\_N\_END>>

<\<FORMATTED\_TRANSCRIPT\_N\_START>>
отформатированный текст расшифровки N.
<\<FORMATTED\_TRANSCRIPT\_N\_END>>

Если расшифровок несколько, предоставь блоки для каждой по порядку.

Пример для ОДНОЙ расшифровки от пользователя "Саня":
<\<SHORT\_SUMMARY\_1\_START>>

* Обсудили планы на выходные.
* Решили пойти за покупками.
* Появилась идея съездить на природу позже.
  <\<SHORT\_SUMMARY\_1\_END>>

<\<DETAILED\_SUMMARY\_1\_START>>

* <b>Планы на выходные:</b>
  Обсуждались различные варианты проведения выходных. Саня предложил сходить в кино или съездить за город, чтобы отдохнуть.

* <b>Покупки:</b>
  Собеседник напомнил о необходимости купить продукты. В итоге было решено посвятить утро субботы походу по магазинам. Это стало приоритетной задачей.

* <b>Поездка на природу:</b>
  Хотя поход по магазинам стал основным планом, идея о поездке на природу не была полностью отвергнута, а отложена на возможное рассмотрение после решения бытовых вопросов.
  <\<DETAILED\_SUMMARY\_1\_END>>

<\<FORMATTED\_TRANSCRIPT\_1\_START>>
Саня: Привет, как дела? Давай, может, на выходных куда-нибудь сходим? Например, в кино или за город?

Собеседник: А, кстати, нам же ещё продукты надо купить.

Саня: Точно, давай тогда в субботу с утра заедем в магазин, а потом уже решим по поводу остального.

Собеседник: Хорошо, договорились.
<\<FORMATTED\_TRANSCRIPT\_1\_END>>

Пример, если для первой расшифровки невозможно составить подробную сводку:
<\<SHORT\_SUMMARY\_1\_START>>

* Пользователь выразил сомнения по поводу поездки.
  <\<SHORT\_SUMMARY\_1\_END>>

<\<DETAILED\_SUMMARY\_1\_START>>
\[подробная сводка недоступна]
<\<DETAILED\_SUMMARY\_1\_END>>

<\<FORMATTED\_TRANSCRIPT\_1\_START>>
Ну, не знаю, смогу ли я на следующей неделе. Работы просто завал. Может, давай лучше к концу месяца?
<\<FORMATTED\_TRANSCRIPT\_1\_END>>

ИСХОДНЫЕ РАСШИФРОВКИ ДЛЯ ОБРАБОТКИ:
{transcripts\_block}
Не используй разделители типа "---" и другие подобные символы в своих ответах.

СТРОГО БЕЗ ТАБЛИЦ. переводить таблицы в текст.
"""


class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        
    def get_string(self, key: str) -> str:
        strings = {
            "ru": {
                "SETTINGS_HEADER": "Настройки транскрибации",
                "API_KEY_INPUT": "API ключ Gemini",
                "API_KEY_SUBTEXT": "Получите ключ в Google AI Studio",
                "GET_API_KEY_BUTTON": "Получить API ключ",
                "ENABLE_PLUGIN": "Включить расшифровку",
                "ENABLE_PLUGIN_SUBTEXT": "Добавляет кнопку 'Краткая сводка' к голосовым сообщениям",
                "TRANSCRIBE_BUTTON": "Краткая сводка",
                "TRANSCRIBING": "Обработка голосового сообщения...",
                "API_KEY_MISSING": "❌ API ключ Gemini не задан. Укажите его в настройках плагина.",
                "NOT_VOICE_MESSAGE": "❌ Это не голосовое сообщение или аудиофайл.",
                "DOWNLOAD_ERROR": "❌ Не удалось загрузить аудиофайл.",
                "TRANSCRIPTION_ERROR": "❌ Ошибка обработки: {error}",
                "TRANSCRIPTION_TITLE": "Краткая сводка",
                "CLOSE_BUTTON": "Закрыть",
                "COPY_BUTTON": "Копировать",
                "DETAILED_BUTTON": "Подробнее",
                "TRANSCRIPT_BUTTON": "Расшифровка",
                "SHORT_BUTTON": "Краткая сводка",
                "USAGE_TITLE": "Как использовать",
                "USAGE_TEXT": "1. Получите API ключ в Google AI Studio\n2. Введите ключ в настройках плагина\n3. Нажмите и удерживайте любое голосовое сообщение\n4. Выберите 'Краткая сводка' в меню"
            },
            "en": {
                "SETTINGS_HEADER": "Transcription Settings",
                "API_KEY_INPUT": "Gemini API Key",
                "API_KEY_SUBTEXT": "Get your key from Google AI Studio",
                "GET_API_KEY_BUTTON": "Get API Key",
                "ENABLE_PLUGIN": "Enable Transcription",
                "ENABLE_PLUGIN_SUBTEXT": "Adds 'Brief Summary' button to voice messages",
                "TRANSCRIBE_BUTTON": "Brief Summary",
                "TRANSCRIBING": "Processing voice message...",
                "API_KEY_MISSING": "❌ Gemini API key not set. Please set it in plugin settings.",
                "NOT_VOICE_MESSAGE": "❌ This is not a voice message or audio file.",
                "DOWNLOAD_ERROR": "❌ Failed to download audio file.",
                "TRANSCRIPTION_ERROR": "❌ Processing error: {error}",
                "TRANSCRIPTION_TITLE": "Brief Summary",
                "CLOSE_BUTTON": "Close",
                "COPY_BUTTON": "Copy",
                "DETAILED_BUTTON": "Detailed",
                "TRANSCRIPT_BUTTON": "Transcript",
                "SHORT_BUTTON": "Brief Summary",
                "USAGE_TITLE": "How to use",
                "USAGE_TEXT": "1. Get API key from Google AI Studio\n2. Enter key in plugin settings\n3. Long press any voice message\n4. Select 'Brief Summary' from menu"
            }
        }
        
        lang_key = 'ru' if self.language.startswith('ru') else 'en'
        return strings[lang_key].get(key, key)

locali = LocalizationManager()

class VoiceTranscriptionPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.progress_dialog: Optional[AlertDialogBuilder] = None
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": f"ExteraPlugin/{__id__}/{__version__}"
        })
        # Кешируем результаты для показа разных вариантов
        self.current_short_summary = ""
        self.current_detailed_summary = ""
        self.current_transcript = ""

    def on_plugin_load(self):
        """Загрузка плагина - добавляем пункт меню"""
        log("Voice Transcription Plugin loading...")
        enabled = self.get_setting("enabled", True)
        log(f"Plugin enabled setting: {enabled}")
        
        if enabled:
            log("Adding menu item...")
            self._add_menu_item()
        else:
            log("Plugin is disabled, not adding menu item")
            
        log("Voice Transcription Plugin loaded successfully")

    def on_plugin_unload(self):
        """Выгрузка плагина - убираем диалоги"""
        if self.progress_dialog:
            try:
                run_on_ui_thread(lambda: self.progress_dialog.dismiss())
            except:
                pass
            self.progress_dialog = None

    def _add_menu_item(self):
        """Добавляет кнопку 'Краткая сводка' в контекстное меню сообщений"""
        try:
            self.add_menu_item(
                MenuItemData(
                    menu_type=MenuItemType.MESSAGE_CONTEXT_MENU,
                    text=locali.get_string("TRANSCRIBE_BUTTON"),
                    icon="msg_voice_mini",
                    on_click=self._handle_transcribe_click,
                    condition="message.isVoice() || message.isRoundVideo() || message.isMusic()"
                )
            )
            log("Voice Transcription menu item added successfully")
        except Exception as e:
            log(f"Failed to add menu item: {e}")

    def _handle_transcribe_click(self, context: Dict[str, Any]):
        """Обработка нажатия на кнопку 'Краткая сводка'"""
        try:
            # Проверяем API ключ
            api_key = self.get_setting("api_key", "").strip()
            if not api_key:
                run_on_ui_thread(lambda: BulletinHelper.show_error(locali.get_string("API_KEY_MISSING")))
                return

            # Получаем сообщение из контекста
            message = context.get("message")
            if not message:
                run_on_ui_thread(lambda: BulletinHelper.show_error(locali.get_string("NOT_VOICE_MESSAGE")))
                return

            # Более детальная проверка типа сообщения
            is_voice = self._is_supported_audio_message(message)
            log(f"Message type check - Voice: {message.isVoice()}, Music: {message.isMusic()}, RoundVideo: {message.isRoundVideo()}, Supported: {is_voice}")
            
            if not is_voice:
                run_on_ui_thread(lambda: BulletinHelper.show_error(locali.get_string("NOT_VOICE_MESSAGE")))
                return

            # Показываем диалог загрузки
            self._show_progress_dialog()

            # Запускаем расшифровку в фоновом потоке
            threading.Thread(
                target=self._process_transcription,
                args=(message, api_key),
                daemon=True
            ).start()

        except Exception as e:
            log(f"Error in transcribe click handler: {e}")
            run_on_ui_thread(lambda: BulletinHelper.show_error(f"Error: {e}"))

    def _show_progress_dialog(self):
        """Показывает диалог прогресса"""
        try:
            current_fragment = get_last_fragment()
            if not current_fragment or not current_fragment.getParentActivity():
                log("Could not get context to show progress dialog")
                return

            context = current_fragment.getParentActivity()
            self.progress_dialog = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_SPINNER)
            self.progress_dialog.set_title(locali.get_string("TRANSCRIBING"))
            
            def show_and_setup():
                self.progress_dialog.show()
                self.progress_dialog.set_cancelable(False)
            
            run_on_ui_thread(show_and_setup)
        except Exception as e:
            log(f"Error showing progress dialog: {e}")

    def _dismiss_progress_dialog(self):
        """Закрывает диалог прогресса"""
        def dismiss():
            if self.progress_dialog:
                try:
                    self.progress_dialog.dismiss()
                except:
                    pass
                self.progress_dialog = None
        run_on_ui_thread(dismiss)

    def _is_supported_audio_message(self, message: MessageObject) -> bool:
        """Проверяет, поддерживается ли данный тип аудиосообщения"""
        try:
            # Упрощенная проверка на основе успешных плагинов
            if not message.messageOwner or not message.messageOwner.media:
                return False
            
            media = message.messageOwner.media
            
            # Проверяем голосовые сообщения и круглые видео (по примеру re_extera плагина)
            if hasattr(media, 'voice') and media.voice:
                log("Found voice message")
                return True
            
            if hasattr(media, 'round') and media.round:
                log("Found round video message")
                return True
                
            # Проверяем обычные аудиофайлы через документы
            if hasattr(media, 'document') and media.document:
                document = media.document
                if hasattr(document, 'mime_type') and document.mime_type:
                    mime = str(document.mime_type).lower()
                    if mime.startswith('audio/'):
                        log("Found audio document")
                        return True
            
            return False
            
        except Exception as e:
            log(f"Error checking message type: {e}")
            return False

    def _wait_for_file(self, file_path: str, document, message) -> bool:
        """Ждет загрузки файла (по примеру Gemini плагина)"""
        if os.path.exists(file_path):
            log(f"File already exists: {file_path}")
            return True
        
        log(f"Starting file download: {file_path}")
        file_loader = get_file_loader()
        file_loader.loadFile(document, message, FileLoader.PRIORITY_HIGH, 1)
        
        # Ждем загрузки файла до 30 секунд
        for i in range(30):
            if os.path.exists(file_path):
                log(f"File downloaded successfully after {i+1} seconds: {file_path}")
                return True
            time.sleep(1)
        
        log(f"File download timed out: {file_path}")
        return False

    def _get_document_from_message(self, message: MessageObject):
        """Получает документ из сообщения в зависимости от типа"""
        try:
            # Используем стандартный метод MessageObject.getDocument() 
            # который возвращает правильный TLObject документ
            document = MessageObject.getDocument(message.messageOwner)
            if document:
                log("Found document via MessageObject.getDocument()")
                return document
            
            log("No document found via MessageObject.getDocument()")
            return None
            
        except Exception as e:
            log(f"Error getting document from message: {e}")
            return None



    def _parse_gemini_response(self, response_text: str) -> Tuple[str, str, str]:
        """Парсит ответ от Gemini и извлекает три блока"""
        try:
            # Регулярные выражения для извлечения блоков
            short_pattern = r'<<SHORT_SUMMARY_\d+_START>>(.*?)<<SHORT_SUMMARY_\d+_END>>'
            detailed_pattern = r'<<DETAILED_SUMMARY_\d+_START>>(.*?)<<DETAILED_SUMMARY_\d+_END>>'
            transcript_pattern = r'<<FORMATTED_TRANSCRIPT_\d+_START>>(.*?)<<FORMATTED_TRANSCRIPT_\d+_END>>'
            
            # Ищем блоки с флагом DOTALL для многострочного текста
            short_match = re.search(short_pattern, response_text, re.DOTALL)
            detailed_match = re.search(detailed_pattern, response_text, re.DOTALL)
            transcript_match = re.search(transcript_pattern, response_text, re.DOTALL)
            
            short_summary = short_match.group(1).strip() if short_match else "[краткая сводка недоступна]"
            detailed_summary = detailed_match.group(1).strip() if detailed_match else "[подробная сводка недоступна]"
            transcript = transcript_match.group(1).strip() if transcript_match else "[расшифровка недоступна]"
            
            log(f"Parsed blocks - Short: {len(short_summary)} chars, Detailed: {len(detailed_summary)} chars, Transcript: {len(transcript)} chars")
            
            return short_summary, detailed_summary, transcript
            
        except Exception as e:
            log(f"Error parsing Gemini response: {e}")
            # Возвращаем исходный текст как краткую сводку в случае ошибки
            return response_text.strip(), "[подробная сводка недоступна]", "[расшифровка недоступна]"

    def _process_transcription(self, message: MessageObject, api_key: str):
        """Обрабатывает расшифровку в фоновом потоке"""
        try:
            # Получаем документ из сообщения
            document = self._get_document_from_message(message)
            if not document:
                self._show_error(locali.get_string("NOT_VOICE_MESSAGE"))
                return

            # Загружаем файл
            audio_path = self._download_audio_file(document, message)
            if not audio_path:
                self._show_error(locali.get_string("DOWNLOAD_ERROR"))
                return

            # Конвертируем в base64
            audio_base64 = self._convert_to_base64(audio_path)
            if not audio_base64:
                self._show_error("Failed to convert audio to base64")
                return

            # Определяем MIME тип
            mime_type = self._get_mime_type(audio_path)

            # Отправляем на обработку в Gemini
            response_text = self._process_with_gemini(audio_base64, mime_type, api_key)
            
            # Удаляем временный файл
            try:
                os.remove(audio_path)
            except:
                pass

            if response_text:
                # Парсим ответ на три блока
                short_summary, detailed_summary, transcript = self._parse_gemini_response(response_text)
                
                # Сохраняем результаты
                self.current_short_summary = short_summary
                self.current_detailed_summary = detailed_summary
                self.current_transcript = transcript
                
                # Показываем краткую сводку с кнопками переключения
                self._show_summary_result()
            else:
                self._show_error("Empty response from Gemini")

        except Exception as e:
            log(f"Error in transcription process: {e}")
            self._show_error(f"Processing error: {e}")
        finally:
            self._dismiss_progress_dialog()

    def _download_audio_file(self, document, message: MessageObject) -> Optional[str]:
        """Загружает аудиофайл и возвращает путь к нему"""
        try:
            if document is None:
                log("Document is None, cannot download.")
                return None
            
            file_loader = get_file_loader()
            
            # Голосовые сообщения и кружки обрабатываются по-разному
            if message.isVoice() or message.isRoundVideo():
                log(f"Getting path for voice/round message using getPathToMessage")
                file_path_obj = file_loader.getPathToMessage(message.messageOwner)
            else:
                log(f"Getting path for other document type using getPathToAttach")
                file_path_obj = file_loader.getPathToAttach(document, True)

            if file_path_obj is None:
                log("Get path returned null.")
                return None
                
            file_path = file_path_obj.getAbsolutePath()
            log(f"Expected file path: {file_path}")
            
            if not file_path:
                log("File path is empty or null after getAbsolutePath.")
                return None

            # Ждем загрузки файла
            if self._wait_for_file(file_path, document, message):
                return file_path
            else:
                log("Failed to download file after waiting.")
                return None
                
        except Exception as e:
            log(f"Error downloading audio file: {e}")
            import traceback
            log(f"Traceback: {traceback.format_exc()}")
            return None

    def _convert_to_base64(self, file_path: str) -> Optional[str]:
        """Конвертирует аудиофайл в base64"""
        try:
            with open(file_path, 'rb') as f:
                audio_data = f.read()
                return base64.b64encode(audio_data).decode('utf-8')
        except Exception as e:
            log(f"Error converting to base64: {e}")
            return None

    def _get_mime_type(self, file_path: str) -> str:
        """Определяет MIME тип аудиофайла"""
        ext = os.path.splitext(file_path)[1].lower()
        
        mime_types = {
            '.ogg': 'audio/ogg',
            '.opus': 'audio/ogg',
            '.mp3': 'audio/mpeg',
            '.wav': 'audio/wav',
            '.m4a': 'audio/mp4',
            '.aac': 'audio/aac'
        }
        
        return mime_types.get(ext, 'audio/ogg')

    def _process_with_gemini(self, audio_base64: str, mime_type: str, api_key: str) -> Optional[str]:
        """Отправляет аудио на обработку через Gemini API"""
        try:
            url = f"{GEMINI_BASE_URL}?key={api_key}"
            
            # Формируем промпт с заменой переменной
            system_prompt = SYSTEM_PROMPT_AUDIO_VIDEO_SUMMARY_GEMINI.format(
                forwarder_name="Пользователь",
                transcripts_block="Аудиосообщение из Telegram."
            )
            
            payload = {
                "system_instruction": {
                    "parts": [{
                        "text": system_prompt
                    }]
                },
                "contents": [{
                    "role": "user",
                    "parts": [
                        {"text": "Создай краткую сводку, подробную сводку и отформатированную расшифровку этого голосового сообщения."},
                        {"inline_data": {
                            "mime_type": mime_type,
                            "data": audio_base64
                        }}
                    ]
                }],
                "generationConfig": {
                    "temperature": 0
                }
            }

            response = self.session.post(url, json=payload, timeout=90)
            response.raise_for_status()

            data = response.json()
            
            if "candidates" in data and data["candidates"]:
                candidate = data["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if parts and "text" in parts[0]:
                        return parts[0]["text"].strip()

            # Если есть ошибка в ответе
            if "error" in data:
                error_msg = data["error"].get("message", "Unknown API error")
                log(f"Gemini API error: {error_msg}")
                return None

            log("No text found in API response")
            return None

        except requests.exceptions.RequestException as e:
            log(f"Request error: {e}")
            return None
        except Exception as e:
            log(f"Gemini API error: {e}")
            return None

    def _show_summary_result(self):
        """Показывает краткую сводку с кнопками переключения"""
        def show_result():
            try:
                current_fragment = get_last_fragment()
                if not current_fragment or not current_fragment.getParentActivity():
                    return

                context = current_fragment.getParentActivity()
                builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
                builder.set_title(locali.get_string("TRANSCRIPTION_TITLE"))
                builder.set_message(self.current_short_summary)
                
                # Кнопка "Подробнее"
                def show_detailed(b, w):
                    b.dismiss()
                    self._show_detailed_summary()

                # Кнопка "Расшифровка"
                def show_transcript(b, w):
                    b.dismiss()
                    self._show_transcript()
                
                # Кнопка "Копировать"
                def copy_short(b, w):
                    from android.content import ClipData, Context as AndroidContext
                    clipboard = context.getSystemService(AndroidContext.CLIPBOARD_SERVICE)
                    clip = ClipData.newPlainText("Brief Summary", self.current_short_summary)
                    clipboard.setPrimaryClip(clip)
                    BulletinHelper.show_info("Copied to clipboard")
                    b.dismiss()

                builder.set_positive_button(locali.get_string("DETAILED_BUTTON"), show_detailed)
                builder.set_neutral_button(locali.get_string("TRANSCRIPT_BUTTON"), show_transcript)
                builder.set_negative_button(locali.get_string("COPY_BUTTON"), copy_short)
                builder.show()

            except Exception as e:
                log(f"Error showing summary result: {e}")
                BulletinHelper.show_error(f"Error showing result: {e}")

        run_on_ui_thread(show_result)

    def _show_detailed_summary(self):
        """Показывает подробную сводку"""
        def show_detailed():
            try:
                current_fragment = get_last_fragment()
                if not current_fragment or not current_fragment.getParentActivity():
                    return

                context = current_fragment.getParentActivity()
                builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
                builder.set_title("Подробная сводка")
                builder.set_message(self.current_detailed_summary)
                
                # Кнопка "Краткая сводка"
                def show_short(b, w):
                    b.dismiss()
                    self._show_summary_result()

                # Кнопка "Расшифровка"
                def show_transcript(b, w):
                    b.dismiss()
                    self._show_transcript()
                
                # Кнопка "Копировать"
                def copy_detailed(b, w):
                    from android.content import ClipData, Context as AndroidContext
                    clipboard = context.getSystemService(AndroidContext.CLIPBOARD_SERVICE)
                    clip = ClipData.newPlainText("Detailed Summary", self.current_detailed_summary)
                    clipboard.setPrimaryClip(clip)
                    BulletinHelper.show_info("Copied to clipboard")
                    b.dismiss()

                builder.set_positive_button(locali.get_string("SHORT_BUTTON"), show_short)
                builder.set_neutral_button(locali.get_string("TRANSCRIPT_BUTTON"), show_transcript)
                builder.set_negative_button(locali.get_string("COPY_BUTTON"), copy_detailed)
                builder.show()

            except Exception as e:
                log(f"Error showing detailed summary: {e}")

        run_on_ui_thread(show_detailed)

    def _show_transcript(self):
        """Показывает расшифровку"""
        def show_transcript():
            try:
                current_fragment = get_last_fragment()
                if not current_fragment or not current_fragment.getParentActivity():
                    return

                context = current_fragment.getParentActivity()
                builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
                builder.set_title("Расшифровка")
                builder.set_message(self.current_transcript)
                
                # Кнопка "Краткая сводка"
                def show_short(b, w):
                    b.dismiss()
                    self._show_summary_result()

                # Кнопка "Подробнее"
                def show_detailed(b, w):
                    b.dismiss()
                    self._show_detailed_summary()
                
                # Кнопка "Копировать"
                def copy_transcript(b, w):
                    from android.content import ClipData, Context as AndroidContext
                    clipboard = context.getSystemService(AndroidContext.CLIPBOARD_SERVICE)
                    clip = ClipData.newPlainText("Transcript", self.current_transcript)
                    clipboard.setPrimaryClip(clip)
                    BulletinHelper.show_info("Copied to clipboard")
                    b.dismiss()

                builder.set_positive_button(locali.get_string("SHORT_BUTTON"), show_short)
                builder.set_neutral_button(locali.get_string("DETAILED_BUTTON"), show_detailed)
                builder.set_negative_button(locali.get_string("COPY_BUTTON"), copy_transcript)
                builder.show()

            except Exception as e:
                log(f"Error showing transcript: {e}")

        run_on_ui_thread(show_transcript)

    def _show_error(self, error_message: str):
        """Показывает ошибку пользователю"""
        run_on_ui_thread(lambda: BulletinHelper.show_error(
            locali.get_string("TRANSCRIPTION_ERROR").format(error=error_message)
        ))

    def _open_api_key_link(self, view):
        """Открывает ссылку для получения API ключа"""
        try:
            from android.content import Intent
            from android.net import Uri
            
            current_fragment = get_last_fragment()
            if not current_fragment or not current_fragment.getParentActivity():
                return
                
            context = current_fragment.getParentActivity()
            intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://aistudio.google.com/app/apikey"))
            context.startActivity(intent)
        except Exception as e:
            log(f"Error opening API key link: {e}")

    def _show_usage_info(self, view):
        """Показывает информацию об использовании"""
        try:
            current_fragment = get_last_fragment()
            if not current_fragment or not current_fragment.getParentActivity():
                return

            context = current_fragment.getParentActivity()
            builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
            builder.set_title(locali.get_string("USAGE_TITLE"))
            builder.set_message(locali.get_string("USAGE_TEXT"))
            builder.set_positive_button(locali.get_string("CLOSE_BUTTON"), lambda b, w: b.dismiss())
            builder.show()
        except Exception as e:
            log(f"Error showing usage info: {e}")

    def _on_enable_change(self, enabled: bool):
        """Обработка изменения состояния плагина"""
        log(f"Plugin enable state changed to: {enabled}")
        if enabled:
            log("Adding menu item due to enable state change...")
            self._add_menu_item()
        else:
            log("Plugin disabled, menu item will be removed on next reload")
        # При отключении пункт меню автоматически удалится при перезагрузке

    def create_settings(self):
        """Создает настройки плагина"""
        return [
            Header(text=locali.get_string("SETTINGS_HEADER")),
            
            Switch(
                key="enabled",
                text=locali.get_string("ENABLE_PLUGIN"),
                subtext=locali.get_string("ENABLE_PLUGIN_SUBTEXT"),
                default=True,
                icon="msg_voice_mini",
                on_change=self._on_enable_change
            ),
            
            Input(
                key="api_key",
                text=locali.get_string("API_KEY_INPUT"),
                subtext=locali.get_string("API_KEY_SUBTEXT"),
                default="",
                icon="msg_pin_code"
            ),
            
            Text(
                text=locali.get_string("GET_API_KEY_BUTTON"),
                icon="msg_link",
                accent=True,
                on_click=self._open_api_key_link
            ),
            
            Divider(),
            
            Text(
                text=locali.get_string("USAGE_TITLE"),
                icon="msg_info",
                on_click=self._show_usage_info
            )
        ] 