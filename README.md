# Voice Transcription Plugin для ExteraGram

Плагин для расшифровки голосовых сообщений и аудиофайлов с использованием Google Gemini API.

## Возможности

- 🎤 Расшифровка голосовых сообщений
- 🎵 Расшифровка аудиофайлов (MP3, WAV, OGG, M4A)
- 🎥 Поддержка видеосообщений
- 🌍 Автоматическое определение языка
- 👥 Различение нескольких говорящих
- 📋 Копирование результата в буфер обмена
- 🌐 Поддержка русского и английского интерфейса

## Установка

1. Скачайте файл `voice_transcription.plugin`
2. В ExteraGram откройте меню → **Настройки** → **Плагины**
3. Нажмите **Установить плагин** и выберите скачанный файл
4. Плагин будет установлен и активирован автоматически

## Настройка

### Получение API ключа Gemini

1. Перейдите на [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Войдите в свой аккаунт Google
3. Нажмите **Create API Key**
4. Скопируйте созданный ключ

### Настройка плагина

1. Откройте настройки плагина:
   - **Меню** → **Настройки** → **Плагины** → **Voice Transcription**
2. Включите опцию **"Включить расшифровку"**
3. Вставьте API ключ в поле **"API ключ Gemini"**
4. Сохраните настройки

## Использование

### Расшифровка голосового сообщения

1. Найдите голосовое сообщение или аудиофайл в чате
2. **Долго нажмите** на сообщение (откроется контекстное меню)
3. Выберите **"Расшифровать"** из меню
4. Дождитесь завершения обработки
5. В появившемся окне увидите текст расшифровки
6. Нажмите **"Копировать"** для сохранения в буфер обмена

### Поддерживаемые форматы

- 🎤 Голосовые сообщения Telegram (OGG)
- 🎵 Аудиофайлы: MP3, WAV, M4A, AAC
- 🎥 Видеосообщения (извлекается аудиодорожка)

### Особенности работы

- **Автоматическое определение языка**: плагин автоматически определяет язык речи
- **Различение говорящих**: если в записи несколько человек, они будут помечены как "Спикер 1", "Спикер 2" и т.д.
- **Точная транскрибация**: результат максимально близок к оригинальной речи
- **Обработка в фоне**: расшифровка происходит в фоновом режиме, не блокируя интерфейс

## Технические детали

### Системные требования

- ExteraGram версии 11.12.0 или выше
- Подключение к интернету
- Действующий API ключ Google Gemini

### Используемые технологии

- **Google Gemini 2.5 Flash Lite**: модель для распознавания речи
- **Base64 кодирование**: для передачи аудио в API
- **Многопоточность**: обработка в фоновом потоке
- **Локализация**: поддержка русского и английского языков

### Ограничения

- Максимальная длительность аудио зависит от лимитов Gemini API
- Требуется стабильное интернет-соединение
- Расход API квоты согласно тарифам Google

## Безопасность и приватность

- ✅ Аудиофайлы отправляются только в Google Gemini API
- ✅ Локальные временные файлы автоматически удаляются
- ✅ API ключ хранится только локально на устройстве
- ✅ Исходный код открыт для проверки

## Устранение проблем

### Ошибка "API ключ не задан"
- Убедитесь, что вы ввели действующий API ключ в настройках плагина
- Проверьте, что ключ скопирован полностью без лишних пробелов

### Ошибка "Не удалось загрузить аудиофайл"
- Проверьте стабильность интернет-соединения
- Убедитесь, что файл не поврежден
- Попробуйте еще раз через некоторое время

### Кнопка "Расшифровать" не появляется
- Убедитесь, что плагин включен в настройках
- Проверьте, что вы нажимаете на голосовое сообщение или аудиофайл
- Перезапустите приложение

### Ошибки API
- Проверьте действительность API ключа
- Убедитесь, что у вас есть доступные квоты в Google AI Studio
- Проверьте подключение к интернету

## Поддержка

Если у вас возникли проблемы с плагином:

1. Проверьте раздел "Устранение проблем" выше
2. Убедитесь, что используете последнюю версию ExteraGram
3. Попробуйте переустановить плагин
4. Создайте issue в репозитории проекта с подробным описанием проблемы

## Лицензия

Плагин распространяется свободно для личного использования.

---

**Примечание**: Для работы плагина требуется действующий API ключ Google Gemini. Использование API может быть платным согласно тарифам Google. 