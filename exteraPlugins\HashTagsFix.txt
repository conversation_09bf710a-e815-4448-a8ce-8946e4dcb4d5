from base_plugin import BasePlugin
from android_utils import run_on_ui_thread, log
from hook_utils import get_private_field, set_private_field

from android.view import View

from org.telegram.ui import ChatActivity, ChatActivityContainer
from org.telegram.messenger import AndroidUtilities, ChatObject, HashtagSearchController, LocaleController, R, MediaDataController, UserConfig


from java.lang import Boolean, Integer, String
from java import jint

__name__ = "HashTagsFix"
__description__ = "Open \"This chat\" tab instead of \"Public Posts\" on click on hashtag"
__icon__ = "cats_for_conversation/12"
__version__ = "1.0.0"
__id__ = "hashTagsFix"
__author__ = "@bleizix"
__min_version__ = "11.9.1"


class HashTagsHook:

    def process(self, param):
        chatActivity = param.thisObject

        hashtag = param.args[0]
        forcePublic = param.args[1]

        savedMessagesHint = get_private_field(chatActivity, "savedMessagesHint")
        savedMessagesSearchHint = get_private_field(chatActivity, "savedMessagesSearchHint")


        if (len(hashtag) == 0 or (not hashtag.startswith("#") and not hashtag.startswith("$"))):
            return


        delay = False
        if (savedMessagesHint is not None and savedMessagesHint.shown()):
            savedMessagesHint.hide()
            delay = True

        if (savedMessagesSearchHint is not None and savedMessagesSearchHint.shown()):
            savedMessagesSearchHint.hide()
            delay = True

        if (delay):
            run_on_ui_thread(lambda: chatActivity.openHashtagSearch(hashtag), 200)
            return

        set_private_field(chatActivity, "searchingHashtag", hashtag)
        set_private_field(chatActivity, "searchingQuery", hashtag)

        channelHashtags = hashtag.count("@") > 0

        method = ChatActivity.getClass().getDeclaredMethod("checkHashtagStories", Boolean.TYPE)
        method.setAccessible(True)
        method.invoke(chatActivity, True)

        if (not chatActivity.actionBar.isSearchFieldVisible()):
            avatarContainer = get_private_field(chatActivity, "avatarContainer")

            AndroidUtilities.updateViewVisibilityAnimated(avatarContainer, False, 0.95, True)

            headerItem = get_private_field(chatActivity, "headerItem")

            if (headerItem):
                headerItem.setVisibility(View.GONE)

            attachItem = get_private_field(chatActivity, "attachItem")

            if (attachItem):
                attachItem.setVisibility(View.GONE)


            editTextItem = get_private_field(chatActivity, "editTextItem")

            if (editTextItem):
                editTextItem.setVisibility(View.GONE)

            threadMessageId = get_private_field(chatActivity, "threadMessageId")
            chatMode = get_private_field(chatActivity, "chatMode")
            searchItem = get_private_field(chatActivity, "searchItem")

            if ((threadMessageId == 0 or chatMode == ChatActivity.MODE_SAVED) and searchItem):
                searchItem.setVisibility(View.VISIBLE)

            searchIconItem = get_private_field(chatActivity, "searchIconItem")
            showSearchAsIcon = get_private_field(chatActivity, "showSearchAsIcon")

            if (searchIconItem and showSearchAsIcon):
                searchIconItem.setVisibility(View.GONE)

            audioCallIconItem = get_private_field(chatActivity, "audioCallIconItem")
            showAudioCallAsIcon = get_private_field(chatActivity, "showAudioCallAsIcon")

            if (audioCallIconItem and showAudioCallAsIcon):
                audioCallIconItem.setVisibility(View.GONE)

            
            set_private_field(chatActivity, "searchItemVisible", True)

            method = ChatActivity.getClass().getDeclaredMethod("updateSearchButtons", Integer.TYPE, Integer.TYPE, Integer.TYPE)
            method.setAccessible(True)
            method.invoke(chatActivity, jint(0), jint(0), jint(-1))

            method = ChatActivity.getClass().getDeclaredMethod("updateBottomOverlay")
            method.setAccessible(True)
            method.invoke(chatActivity)

        if (get_private_field(chatActivity, "actionBarSearchTags") and get_private_field(chatActivity, "actionBarSearchTags").shown()):
            get_private_field(chatActivity, "actionBarSearchTags").show(False)

        if (get_private_field(chatActivity,"searchUserButton")):
            get_private_field(chatActivity,"searchUserButton").setVisibility(View.GONE)


        set_private_field(chatActivity, "defaultSearchPage", jint(0)) # <--- main hook
        set_private_field(chatActivity, "openSearchKeyboard", False)


        if (get_private_field(chatActivity,"searchType") == 3): # SEARCH_CHANNEL_POSTS
            HashtagSearchController.getInstance(UserConfig.selectedAccount).clearSearchResults(3)
        else:
            HashtagSearchController.getInstance(UserConfig.selectedAccount).clearSearchResults()

        if (get_private_field(chatActivity,"searchViewPager")):
            get_private_field(chatActivity,"searchViewPager").clearViews()

        if (get_private_field(chatActivity,"searchItem")):
            set_private_field(chatActivity, "preventReopenSearchWithText", True)
            get_private_field(chatActivity,"searchItem").openSearch(False)
            set_private_field(chatActivity, "preventReopenSearchWithText", False)



        if (get_private_field(chatActivity,"searchItem")):
            get_private_field(chatActivity,"searchItem").setSearchFieldCaption(None)
            get_private_field(chatActivity,"searchItem").setSearchFieldText(hashtag, False)
            get_private_field(chatActivity,"searchItem").setSearchFieldHint(LocaleController.getString(R.string.SearchHashtagsHint))

        MediaDataController.getInstance(UserConfig.selectedAccount).searchMessagesInChat(get_private_field(chatActivity,"searchingQuery"), get_private_field(chatActivity,"dialog_id"), get_private_field(chatActivity,"mergeDialogId"),
                                                                                        get_private_field(chatActivity,"classGuid"), 0, get_private_field(chatActivity,"threadMessageId"),
                                                                                        False, get_private_field(chatActivity,"searchingUserMessages"), get_private_field(chatActivity,"searchingChatMessages"),
                                                                                        False, get_private_field(chatActivity,"searchingReaction"))

        method = ChatActivity.getClass().getDeclaredMethod("updatePinnedMessageView", Boolean.TYPE)
        method.setAccessible(True)
        method.invoke(chatActivity, True)

        get_private_field(chatActivity, "hashtagSearchEmptyView").showProgress(True)

        method = ChatActivity.getClass().getDeclaredMethod("showMessagesSearchListView", Boolean.TYPE)
        method.setAccessible(True)
        method.invoke(chatActivity, True)

        if (get_private_field(chatActivity,"hashtagSearchTabs")):
            get_private_field(chatActivity,"hashtagSearchTabs").show(not channelHashtags)

            get_private_field(chatActivity,"messagesSearchListContainer").setPadding(0, 0, 0, chatActivity.getHashtagTabsHeight())


            method = ChatActivity.getClass().getDeclaredMethod("updateSearchListEmptyView")
            method.setAccessible(True)
            method.invoke(chatActivity)


        if ((channelHashtags or forcePublic) and get_private_field(chatActivity,"searchingHashtag") and get_private_field(chatActivity,"hashtagSearchTabs") and get_private_field(chatActivity,"hashtagSearchTabs").tabs.getCurrentPosition() != get_private_field(chatActivity,"defaultSearchPage")):
            get_private_field(chatActivity,"hashtagSearchTabs").tabs.scrollToTab(get_private_field(chatActivity,"defaultSearchPage"), get_private_field(chatActivity,"defaultSearchPage"))

        HashtagSearchController.getInstance(UserConfig.selectedAccount).putToHistory(get_private_field(chatActivity,"searchingHashtag"))
        get_private_field(chatActivity,"hashtagHistoryView").update()
        view = get_private_field(chatActivity,"searchViewPager").getCurrentView()
        if (isinstance(view, ChatActivityContainer)):
            method = ChatActivity.getClass().getDeclaredMethod("updateSearchingHashtag", String)
            method.invoke(chatActivity, get_private_field(chatActivity,"searchingHashtag"))
        log("finiished!")

    def replace_hooked_method(self, param):
        try:
            self.process(param)
        except Exception as e:
            log(f"fucked up: {e}")

class HashTagsHookPlugin(BasePlugin):
    def on_plugin_load(self):
        self.hook_method(
                ChatActivity.getClass().getDeclaredMethod("openHashtagSearch", String, Boolean.TYPE),
                HashTagsHook()
            )