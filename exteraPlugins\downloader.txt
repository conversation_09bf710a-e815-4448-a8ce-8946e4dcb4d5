import os
import uuid
import time
import requests
import traceback
import threading
import socket
from java.util import Locale
from ui.settings import Header, Input, Divider, Text, Switch, Selector
from base_plugin import BasePlugin, HookResult, HookStrategy, MenuItemData, MenuItemType
from android_utils import log, run_on_ui_thread
from ui.bulletin import BulletinHelper
from ui.alert import AlertDialogBuilder
from java.io import File
from client_utils import get_account_instance, get_media_data_controller, get_last_fragment, get_messages_controller
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import ApplicationLoader, SendMessagesHelper
from java.util import ArrayList
from android.content import ClipData, Context, Intent
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity
from android.net import Uri
from android.app import DownloadManager
from android.os import Environment

__name__ = "Media Downloader"
__description__ = "Download and upload media (photo, video, audio, gif) from Internet"
__icon__ = "exteraPluginsSup/0"
__version__ = "1.7"
__id__ = "downloader"
__author__ = "@itsv1eds"
__min_version__ = "11.12.0"

DEFAULT_COBALT_API = "https://co.itsv1eds.ru"
COBALT_API = [DEFAULT_COBALT_API, "https://co.eepy.today", "https://co.otomir23.me", "https://cobalt.255x.ru"]
TEMP_DIR_NAME = "DownloaderTemp"
VIDEO_QUALITY_OPTIONS = ["144","240","360","480","720","1080","1440","2160","4320","max"]
DOWNLOAD_MODE_OPTIONS = ["auto","audio","mute"]
AUDIO_BITRATE_OPTIONS = ["64","96","128","192","256","320"]
YOUTUBE_VIDEO_CODEC_OPTIONS = ["h264","av1","vp9"]
SERVICES_INFO = "YouTube (maybe), TikTok, Instagram, Twitter/X, VK (Video/Clips), Reddit, SoundCloud, Facebook, Pinterest, RuTube"
progress_dialog = None

TRANSLATIONS = {
    "api_title": ("Настройки API", "API Settings"),
    "api_url": ("Cobalt API URL", "Cobalt API URL"),
    "api_key": ("API ключ", "API Key"),
    "api_help": ("Справка", "Help"),
    "api_desc": ("Укажите URL и API (если требуется) вашего Cobalt инстанса. Узнать больше: github.com/imputnet/cobalt", "Enter URL and API (if needed) for your Cobalt instance. Learn more: github.com/imputnet/cobalt"),
    "custom_api": ("Свой API", "Custom API"),
    "custom_api_url": ("Свой URL API", "Custom API URL"),
    "auto_fallback": ("Автопереключение API", "Auto API Fallback"),
    "auto_fallback_desc": ("Автоматически переключаться на другой API при ошибке", "Automatically switch to another API on error"),
    "proxy_title": ("Настройки прокси", "Proxy Settings"),
    "proxy_type": ("Тип прокси", "Proxy Type"),
    "proxy_type_items": (["Нет", "HTTP", "HTTPS", "SOCKS", "MTProto"], ["None", "HTTP", "HTTPS", "SOCKS", "MTProto"]),
    "proxy_url": ("URL прокси", "Proxy URL"),
    "proxy_url_desc": ("Введите прокси host:port", "Enter proxy host:port"),
    "proxy_user": ("Логин прокси", "Proxy Username"),
    "proxy_password": ("Пароль прокси", "Proxy Password"),
    "proxy_secret": ("MTProto Secret", "MTProto Secret"),
    "proxy_secret_desc": ("Секретный ключ MTProto прокси", "MTProto proxy secret key"),
    "settings_title": ("Настройки загрузки", "Download Settings"),
    "show_settings_buttons": ("Кнопка настроек в меню", "Settings button in menu"),
    "show_settings_buttons_desc": ("Добавляет кнопку открытия настроек плагина в меню", "Adds plugin settings button to menu"),
    "include_source": ("Включить ссылку источника", "Include source link"),
    "include_source_desc": ("Добавить ссылку источника в подпись сообщения", "Add source link to message caption"),
    "send_as_file": ("Отправлять как файл", "Send as file"),
    "send_as_file_desc": ("Скачанный контент будет отправлен как документ", "Downloaded media will be sent as document"),
    "disable_metadata": ("Отключить метаданные", "Disable metadata"),
    "disable_metadata_desc": ("Название, исполнитель и другие данные не будут добавлены в файл", "Title, artist, and other info will not be added to the file"),
    "advanced_settings": ("Дополнительные настройки", "Advanced Settings"),
    "advanced_settings_desc": ("Показать качество видео и битрейт аудио", "Show video quality and audio bitrate"),
    "video_quality": ("Качество видео", "Video Quality"),
    "audio_bitrate": ("Битрейт аудио", "Audio Bitrate"),
    "services": ("Поддерживаемые сервисы", "Supported services"),
    "usage_cmd": (".down/.dl [URL] - Скачивает и отправляет медиа\nПример: .dl youtube.com/watch?v=dQw4w9WgXcQ", "Command: .down/.dl [URL] - Download and send video/audio\nExample: .dl youtube.com/watch?v=dQw4w9WgXcQ"),
    "donate_title": ("Поддержать разработку", "Support development"),
    "donate_info": ("Другая информация и реквизиты", "Other info and requisites"),
    "test_proxy": ("Тест подключения", "Test Connection"),
    "test_proxy_desc": ("Проверить доступность прокси", "Test proxy availability"),
}

def Z(key):
    lang = Locale.getDefault().getLanguage()
    if lang.startswith('ru'):
        idx = 0
    else:
        idx = 1
    return TRANSLATIONS.get(key, (None, None))[idx]

class VideoDownloaderPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self._temp_dir = None
        self._cancel_requested = False
        self._drawer_settings_item = None
        self._chat_settings_item = None

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith('ru'):
            idx = 0
        else:
            idx = 1
        api_idx = self.get_setting("api_url_choice_set", 0)
        proxy_type_val = self.get_setting("proxy_type_set", 0)
        proxy_icon = "msg2_proxy_off" if proxy_type_val == 0 else "msg2_proxy_on"
        settings = [
            Header(text=Z("api_title")),
            Selector(key="api_url_choice_set", text=Z("api_url"), icon="msg2_devices", default=api_idx, items=COBALT_API + [Z("custom_api")]),
        ]
        if api_idx == len(COBALT_API):
            settings.extend([
                Input(key="api_url_set", text=Z("custom_api_url"), icon="msg_instant_link", default=self.get_setting("api_url_set", "")),
                Input(key="api_key_set", text=Z("api_key"), icon="msg_pin_code", default=self.get_setting("api_key_set", "")),
                Text(text=Z("api_help"), icon="msg_psa", accent=True, on_click=lambda view: self.show_my_info_alert(title=Z("api_title"), message=Z("api_desc"), neutral_button="GitHub", neutral_link="https://github.com/imputnet/cobalt", neutral_type="link")),
            ])
        settings.extend([
            Header(text=Z("proxy_title")),
            Selector(key="proxy_type_set", text=Z("proxy_type"), icon=proxy_icon, default=proxy_type_val, items=TRANSLATIONS["proxy_type_items"][idx]),
        ])
        if self.get_setting("proxy_type_set", 0) != 0:
            proxy_type_val = self.get_setting("proxy_type_set", 0)
            if proxy_type_val == 4:
                settings.extend([
                    Input(key="proxy_url_set", text=Z("proxy_url"), icon="msg_link", default=self.get_setting("proxy_url_set", ""), subtext=Z("proxy_url_desc")),
                    Input(key="proxy_username_set", text=Z("proxy_secret"), icon="msg_pin_code", default=self.get_setting("proxy_username_set", ""), subtext=Z("proxy_secret_desc")),
                    Text(text=Z("test_proxy"), icon="msg_message", accent=True, on_click=lambda view: self._test_current_proxy()),
                ])
            else:
                settings.extend([
                    Input(key="proxy_url_set", text=Z("proxy_url"), icon="msg_link", default=self.get_setting("proxy_url_set", ""), subtext=Z("proxy_url_desc")),
                    Input(key="proxy_username_set", text=Z("proxy_user"), icon="msg_contacts", default=self.get_setting("proxy_username_set", "")),
                    Input(key="proxy_password_set", text=Z("proxy_password"), icon="msg_pin_code", default=self.get_setting("proxy_password_set", "")),
                    Text(text=Z("test_proxy"), icon="msg_message", accent=True, on_click=lambda view: self._test_current_proxy()),
                ])
        settings.extend([
            Header(text=Z("settings_title")),
            Switch(key="auto_fallback_set", text=Z("auto_fallback"), icon="media_flip", default=self.get_setting("auto_fallback_set", True), subtext=Z("auto_fallback_desc")),
            Switch(key="show_settings_buttons_set", text=Z("show_settings_buttons"), icon="msg_reorder", default=self.get_setting("show_settings_buttons_set", True), subtext=Z("show_settings_buttons_desc"), on_change=self._on_show_settings_buttons_change),
            Switch(key="include_source_set", text=Z("include_source"), icon="msg_link", default=self.get_setting("include_source_set", True), subtext=Z("include_source_desc")),
            Switch(key="send_as_file_set", text=Z("send_as_file"), icon="msg_sendfile", default=self.get_setting("send_as_file_set", False), subtext=Z("send_as_file_desc")),
        ])
        if self.get_setting("send_as_file_set", False):
            settings.extend([
                Switch(key="disable_metadata_set", text=Z("disable_metadata"), icon="menu_intro", default=self.get_setting("disable_metadata_set", False), subtext=Z("disable_metadata_desc")),
            ])
        settings.extend([
            Switch(key="show_advanced_set", text=Z("advanced_settings"), icon="msg_settings_14", default=self.get_setting("show_advanced_set", False), subtext=Z("advanced_settings_desc")),
        ])
        if self.get_setting("show_advanced_set", False):
            settings.extend([
                Selector(key="video_quality_set", text=Z("video_quality"), icon="msg_video", default=self.get_setting("video_quality_set", 5), items=VIDEO_QUALITY_OPTIONS),
                Selector(key="audio_bitrate_set", text=Z("audio_bitrate"), icon="input_mic", default=self.get_setting("audio_bitrate_set", 5), items=AUDIO_BITRATE_OPTIONS),
            ])
        settings.extend([
            Text(text=Z("services"), accent=True, icon="menu_premium_main", on_click=lambda view: self.show_my_info_alert(title=Z("services"),message=SERVICES_INFO)),
            Divider(text=Z("usage_cmd")),
            Header(text=Z("donate_title")),
            Text(text="TON", icon="msg_ton", accent=True, on_click=lambda view: run_on_ui_thread(lambda: self._copy_to_clipboard("TON", "exteralover.ton"))),
            Text(text=Z("donate_info"), icon="msg_reactions", accent=True, on_click=lambda view: run_on_ui_thread(lambda: get_messages_controller().openByUserName("v1edsinfo", get_last_fragment(), 1))),
        ])
        
        return settings

    def _open_plugin_settings(self, java_plugin):
        try:
            get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
        except Exception as e:
            log(f"[{__id__}] Error opening plugin settings: {e}")

    def _add_settings_menu_items(self):
        try:
            self._drawer_settings_item = self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.DRAWER_MENU,
                text=Z("settings_title"),
                icon="msg_settings_14",
                priority=5,
                on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
            ))
            self._chat_settings_item = self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text=Z("settings_title"),
                icon="msg_settings_14",
                priority=5,
                on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
            ))
        except Exception as e:
            log(f"[{__id__}] Failed to add settings menu items: {e}")

    def _socks_create_connection(self, dest_host, dest_port):
        sock = None
        try:
            url_setting = self.get_setting('proxy_url_set','').strip()
            if not url_setting or ':' not in url_setting:
                return socket.create_connection((dest_host, dest_port))
            
            if '://' in url_setting:
                url_setting = url_setting.split('://', 1)[1]
            
            user = self.get_setting('proxy_username_set','').strip()
            pwd = self.get_setting('proxy_password_set','').strip()
            
            if ':' not in url_setting:
                log(f"[{__id__}] Invalid proxy URL format: {url_setting}")
                return socket.create_connection((dest_host, dest_port))
                
            host, port_str = url_setting.split(':', 1)
            try:
                port = int(port_str)
            except ValueError:
                log(f"[{__id__}] Invalid proxy port: {port_str}")
                return socket.create_connection((dest_host, dest_port))
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(15)
            sock.connect((host, port))
            
            methods = b'\x02' if user and pwd else b'\x00'
            sock.send(b'\x05\x01' + methods)
            response = sock.recv(2)
            if len(response) < 2:
                raise Exception("Invalid SOCKS5 response")
            
            version, method = response
            if version != 5:
                raise Exception(f"Unsupported SOCKS version: {version}")
            
            if method == 2:
                if not user or not pwd:
                    raise Exception("Username/password required but not provided")
                
                auth_request = b'\x01' + bytes([len(user)]) + user.encode() + bytes([len(pwd)]) + pwd.encode()
                sock.send(auth_request)
                auth_response = sock.recv(2)
                if len(auth_response) < 2 or auth_response[1] != 0:
                    raise Exception("SOCKS5 authentication failed")
            
            try:
                dest_ip = socket.inet_aton(dest_host)
                addr_type = b'\x01'
                addr_data = dest_ip
            except socket.error:
                addr_type = b'\x03'
                addr_data = bytes([len(dest_host)]) + dest_host.encode()
            
            connect_request = b'\x05\x01\x00' + addr_type + addr_data + int(dest_port).to_bytes(2, 'big')
            sock.send(connect_request)
            
            connect_response = sock.recv(10)
            if len(connect_response) < 4:
                raise Exception("Invalid SOCKS5 connect response")
            
            if connect_response[1] != 0:
                raise Exception(f"SOCKS5 connect failed with code: {connect_response[1]}")
            
            return sock
            
        except Exception as e:
            log(f"[{__id__}] SOCKS proxy error: {e}")
            if sock:
                try:
                    sock.close()
                except:
                    pass
            return socket.create_connection((dest_host, dest_port))

    def _mtproto_create_connection(self, dest_host, dest_port):
        sock = None
        try:
            url_setting = self.get_setting('proxy_url_set','').strip()
            if not url_setting or ':' not in url_setting:
                return socket.create_connection((dest_host, dest_port))
            
            if '://' in url_setting:
                url_setting = url_setting.split('://', 1)[1]
            
            secret = self.get_setting('proxy_username_set','').strip()
            pwd = self.get_setting('proxy_password_set','').strip()
            
            if ':' not in url_setting:
                log(f"[{__id__}] Invalid MTProto proxy URL format: {url_setting}")
                return socket.create_connection((dest_host, dest_port))
                
            host, port_str = url_setting.split(':', 1)
            try:
                port = int(port_str)
            except ValueError:
                log(f"[{__id__}] Invalid MTProto proxy port: {port_str}")
                return socket.create_connection((dest_host, dest_port))
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(15)
            sock.connect((host, port))
            
            if secret:
                secret_bytes = secret.encode('utf-8')
                header = b'\xef\xef\xef\xef' + bytes([len(secret_bytes)]) + secret_bytes
                sock.send(header)
                
                response = sock.recv(1)
                if response != b'\xef':
                    log(f"[{__id__}] MTProto secret authentication failed")
                    sock.close()
                    return socket.create_connection((dest_host, dest_port))
            
            dest_info = f"{dest_host}:{dest_port}".encode('utf-8')
            connect_packet = b'\xdd\xdd\xdd\xdd' + bytes([len(dest_info)]) + dest_info
            sock.send(connect_packet)
            
            response = sock.recv(1)
            if response != b'\xdd':
                log(f"[{__id__}] MTProto connection setup failed")
                sock.close()
                return socket.create_connection((dest_host, dest_port))
            
            log(f"[{__id__}] MTProto proxy connection established to {dest_host}:{dest_port}")
            return sock
            
        except Exception as e:
            log(f"[{__id__}] MTProto proxy error: {e}")
            if sock:
                try:
                    sock.close()
                except:
                    pass
            return socket.create_connection((dest_host, dest_port))

    def _test_proxy_connection(self, proxies):
        try:
            if not proxies:
                return True
                
            proxy_type = self.get_setting("proxy_type_set", 0)
            
            if proxy_type == 4:
                return self._test_mtproto_connection()
            
            test_url = "http://httpbin.org/ip"
            log(f"[{__id__}] Testing proxy connection to {test_url}")
            
            response = requests.get(test_url, proxies=proxies, timeout=10)
            if response.status_code == 200:
                log(f"[{__id__}] Proxy test successful: {response.json()}")
                return True
            else:
                log(f"[{__id__}] Proxy test failed with status: {response.status_code}")
                return False
                
        except Exception as e:
            log(f"[{__id__}] Proxy test failed: {e}")
            return False

    def _test_mtproto_connection(self):
        try:
            url_setting = self.get_setting('proxy_url_set','').strip()
            if not url_setting or ':' not in url_setting:
                return False
            
            if '://' in url_setting:
                url_setting = url_setting.split('://', 1)[1]
            
            host, port_str = url_setting.split(':', 1)
            try:
                port = int(port_str)
            except ValueError:
                return False
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((host, port))
            sock.close()
            
            log(f"[{__id__}] MTProto proxy connection test successful")
            return True
            
        except Exception as e:
            log(f"[{__id__}] MTProto proxy connection test failed: {e}")
            return False

    def _get_proxies(self):
        try:
            proxy_type = self.get_setting("proxy_type_set", 0)
            url = self.get_setting("proxy_url_set", "").strip()
            user = self.get_setting("proxy_username_set", "").strip()
            pwd = self.get_setting("proxy_password_set", "").strip()
            
            if not url or proxy_type == 0:
                return None
                
            proxies = None
            
            if proxy_type == 3:
                try:
                    import requests_html
                    hostport = url.split('://', 1)[-1] if '://' in url else url
                    if user and pwd:
                        scheme = f"socks5://{user}:{pwd}@{hostport}"
                    else:
                        scheme = f"socks5://{hostport}"
                    proxies = {"http": scheme, "https": scheme}
                    log(f"[{__id__}] SOCKS5 proxy configured: {scheme}")
                except ImportError:
                    log(f"[{__id__}] requests-html not available, using custom SOCKS implementation")
                    try:
                        m = requests.packages.urllib3.util.connection
                        original_create_connection = m.create_connection
                        m.create_connection = lambda addr, timeout=None, **kw: self._socks_create_connection(addr[0], addr[1])
                        log(f"[{__id__}] Patched urllib3 for SOCKS5 proxy")
                        return {"socks": True}
                    except Exception as e:
                        log(f"[{__id__}] Failed patching socks proxy: {e}")
                        return None
                        
            elif proxy_type in (1, 2):
                scheme = "http" if proxy_type == 1 else "https"
                
                clean_url = url
                if "://" in clean_url:
                    clean_url = clean_url.split("://", 1)[1]
                
                if user and pwd:
                    proxy_url = f"{scheme}://{user}:{pwd}@{clean_url}"
                else:
                    proxy_url = f"{scheme}://{clean_url}"
                
                proxies = {"http": proxy_url, "https": proxy_url}
                log(f"[{__id__}] HTTP/HTTPS proxy configured: {proxy_url}")
            
            elif proxy_type == 4:
                log(f"[{__id__}] MTProto proxy configured: {url}")
                try:
                    m = requests.packages.urllib3.util.connection
                    original_create_connection = m.create_connection
                    m.create_connection = lambda addr, timeout=None, **kw: self._mtproto_create_connection(addr[0], addr[1])
                    log(f"[{__id__}] Patched urllib3 for MTProto proxy")
                    return {"mtproto": True}
                except Exception as e:
                    log(f"[{__id__}] Failed patching MTProto proxy: {e}")
                    return None
            
            return proxies
            
        except Exception as e:
            log(f"[{__id__}] Error configuring proxy: {e}")
            return None

    def on_plugin_load(self):
        hook_priority = self.get_setting("hook_priority_set", 1)
        try:
            priority = int(hook_priority)
        except:
            priority = 1
        self.add_on_send_message_hook(priority)
        self._temp_dir = self._get_temp_dir()
        if self._temp_dir:
            log(f"[downloader] VideoDownloaderPlugin loaded")
            self._cleanup_old_files()
            try:
                if self.get_setting("show_settings_buttons_set", True):
                    self._add_settings_menu_items()
            except Exception as e:
                log(f"[{__id__}] Failed to add settings menu items: {e}")
        else:
            log("[downloader] Failed to initialize temp directory")

    def _get_temp_dir(self):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                return None
            temp_dir = File(base_dir, TEMP_DIR_NAME)
            if not temp_dir.exists() and not temp_dir.mkdirs():
                return None
            return temp_dir
        except Exception as e:
            log(f"Error creating temp dir: {e}")
            return None

    def _cleanup_old_files(self, max_age_hours=12):
        try:
            now = time.time()
            max_age_seconds = max_age_hours * 3600
            for file in self._temp_dir.listFiles():
                if file.isFile() and now - file.lastModified() / 1000 > max_age_seconds:
                    file.delete()
        except Exception as e:
            log(f"Cleanup error: {e}")

    def _try_download_with_api(self, api_url, video_url, payload, headers, proxies):
        try:
            resp = requests.post(f"{api_url}/", json=payload, headers=headers, timeout=30, proxies=proxies)
            resp.raise_for_status()
            data = resp.json()
            status = data.get("status")
            
            if status == "error":
                code = data.get("error", {}).get("code", "Unknown error")
                return None, f"API error: {code}"
            
            if status == "picker":
                items = data.get("picker", [])
                if not items:
                    return None, "No items to download"
                item = items[0]
                direct_url = item.get("url")
                filename = item.get("filename")
            else:
                direct_url = data.get("url")
                filename = data.get("filename")

            if not direct_url:
                return None, "Invalid API response"
                
            return {"url": direct_url, "filename": filename}, None
            
        except Exception as e:
            return None, f"Request failed: {e}"

    def _download_video(self, video_url, mode_override=None):
        try:
            api_key = self.get_setting("api_key_set", "").strip()
            proxies = self._get_proxies()
            if proxies:
                if isinstance(proxies, dict) and (proxies.get("mtproto") or proxies.get("socks")):
                    proxy_type = "MTProto" if proxies.get("mtproto") else "SOCKS"
                    proxies = None
                else:
                    if not self._test_proxy_connection(proxies):
                        proxies = None
            
            quality_setting = self.get_setting("video_quality_set", 4)
            if isinstance(quality_setting, int) and 0 <= quality_setting < len(VIDEO_QUALITY_OPTIONS):
                video_quality = VIDEO_QUALITY_OPTIONS[quality_setting]
            else:
                video_quality = str(quality_setting)
            if 'youtube.com' in video_url.lower() or 'youtu.be' in video_url.lower():
                video_quality = VIDEO_QUALITY_OPTIONS[-1]
            
            payload = {
                "url": video_url,
                "videoQuality": video_quality
            }
            
            bitrate_setting = self.get_setting("audio_bitrate_set", 5)
            if isinstance(bitrate_setting, int) and 0 <= bitrate_setting < len(AUDIO_BITRATE_OPTIONS):
                audio_bitrate = AUDIO_BITRATE_OPTIONS[bitrate_setting]
            else:
                audio_bitrate = str(bitrate_setting)
            if 'youtube.com' in video_url.lower() or 'youtu.be' in video_url.lower():
                audio_bitrate = AUDIO_BITRATE_OPTIONS[-1]
            payload["audioBitrate"] = audio_bitrate
            payload["convertGif"] = True
            
            if self.get_setting("disable_metadata_set", False):
                payload["disableMetadata"] = True

            headers = {"Accept": "application/json", "Content-Type": "application/json"}
            if api_key:
                headers["Authorization"] = f"Api-Key {api_key}"

            api_list = []
            
            api_idx = self.get_setting("api_url_choice_set", 0)
            custom = self.get_setting("api_url_set", "")
            
            if api_idx < len(COBALT_API):
                api_list.append(COBALT_API[api_idx].rstrip('/'))
            else:
                if custom:
                    api_list.append(custom.rstrip('/'))
            
            if self.get_setting("auto_fallback_set", True):
                for i, api in enumerate(COBALT_API):
                    if i != api_idx:
                        api_list.append(api.rstrip('/'))
                if api_idx >= len(COBALT_API) and custom:
                    pass
                elif api_idx < len(COBALT_API) and custom:
                    api_list.append(custom.rstrip('/'))
            
            last_error = None
            for i, api_url in enumerate(api_list):
                try:
                    result, error = self._try_download_with_api(api_url, video_url, payload, headers, proxies)
                    if result:
                        direct_url = result["url"]
                        filename = result["filename"]
                        break
                    else:
                        last_error = error
                        continue
                except Exception as e:
                    last_error = f"API {api_url} failed: {e}"
                    continue
            else:
                if len(api_list) > 1:
                    BulletinHelper.show_error(f"All {len(api_list)} APIs failed. Last error: {last_error}")
                else:
                    BulletinHelper.show_error(f"API failed: {last_error}")
                return None
            filename = filename or f"video_{uuid.uuid4()}.mp4"
            file_path = File(self._temp_dir, filename).getAbsolutePath()

            try:
                video_resp = requests.get(direct_url, stream=True, timeout=60, proxies=proxies)
                video_resp.raise_for_status()
            except Exception as e:
                if direct_url.startswith("https://"):
                    fallback_dl = direct_url.replace("https://", "http://")
                    video_resp = requests.get(fallback_dl, stream=True, timeout=60, proxies=proxies)
                    video_resp.raise_for_status()
                else:
                    raise
                
            content_length = video_resp.headers.get("content-length")
            total_length = int(content_length) if content_length else 0
            downloaded = 0

            with open(file_path, "wb") as f:
                for chunk in video_resp.iter_content(chunk_size=8192):
                    if self._cancel_requested:
                        try:
                            os.remove(file_path)
                        except Exception:
                            pass
                        return None
                    f.write(chunk)
                    if total_length:
                        downloaded += len(chunk)
                        percent = int(downloaded * 100 / total_length)
                        run_on_ui_thread(lambda p=percent: progress_dialog.set_progress(p))

            return file_path
            
        except Exception as e:
            self._dismiss_dialog()
            log(f"[downloader] Download error: {e}\n{traceback.format_exc()}")
            BulletinHelper.show_error(f"Download error: {e}")
            return None

    def send_video(self, video_path: str, dialog_id: int, caption: str = None, notify: bool = True, schedule_date: int = 0, reply_to_msg=None, reply_to_top_msg=None):
        ttl = 0
        force_document = False
        has_media_spoilers = False
        cover_path = None
        quick_reply_shortcut = None
        quick_reply_shortcut_id = 0
        effect_id = 0
        stars = 0
        log(f"[downloader] VideoDownloaderPlugin: send_video entry for path: {video_path}, dialog: {dialog_id} (from background thread)")
        ext = os.path.splitext(video_path)[1].lower()
        if ext in [".mp3", ".wav", ".ogg", ".opus", ".m4a"]:
            return self.send_audio(video_path, dialog_id, caption, notify, schedule_date, reply_to_msg, reply_to_top_msg)
        try:
            account_instance = get_account_instance()
            if account_instance is None:
                log("[downloader] Error: Could not get AccountInstance.")
                return
            entities = None
            if caption:
                mdc = get_media_data_controller()
                if mdc:
                    entities = mdc.getEntities([caption], True)

            SendMessagesHelper.prepareSendingVideo(
                account_instance,
                video_path,
                None,
                cover_path,
                None,
                dialog_id,
                reply_to_msg,
                reply_to_top_msg,
                None,
                None,
                entities,
                ttl,
                None,
                notify,
                schedule_date,
                force_document,
                has_media_spoilers,
                caption,
                quick_reply_shortcut,
                quick_reply_shortcut_id,
                effect_id,
                stars
            )
            log(f"[downloader] Video sending initiated for path: {video_path} to dialog: {dialog_id} (from background thread)")

        except Exception as e:
            log(f"[downloader] Error preparing video for sending in background thread: {e}\n{traceback.format_exc()}")
            BulletinHelper.show_error(f"Error sending video: {e}")

    def send_audio(self, audio_path: str, dialog_id: int, caption: str = None, notify: bool = True, schedule_date: int = 0, reply_to_msg=None, reply_to_top_msg=None):
        log(f"[downloader] VideoDownloaderPlugin: send_audio entry for path: {audio_path}, dialog: {dialog_id}")
        try:
            account_instance = get_account_instance()
            if account_instance is None:
                log("[downloader] Error: Could not get AccountInstance.")
                return
            import mimetypes
            mime, _ = mimetypes.guess_type(audio_path)
            if mime is None:
                ext = os.path.splitext(audio_path)[1].lower()
                if ext == ".mp3":
                    mime = "audio/mpeg"
                elif ext == ".wav":
                    mime = "audio/wav"
                elif ext in [".ogg", ".opus", ".m4a"]:
                    mime = "audio/ogg"
                else:
                    mime = "application/octet-stream"
            ext_cache_root = ApplicationLoader.applicationContext.getExternalCacheDir()
            plugin_ext_dir = File(ext_cache_root, TEMP_DIR_NAME)
            if not plugin_ext_dir.exists() and not plugin_ext_dir.mkdirs():
                log("[downloader] Failed to create external temp dir")
            external_path = File(plugin_ext_dir, File(audio_path).getName()).getAbsolutePath()
            with open(audio_path, 'rb') as f_in, open(external_path, 'wb') as f_out:
                while True:
                    chunk = f_in.read(8192)
                    if not chunk:
                        break
                    f_out.write(chunk)
            audio_path = external_path
            SendMessagesHelper.prepareSendingDocument(
                account_instance,
                audio_path,
                audio_path,
                None,
                caption,
                mime,
                dialog_id,
                reply_to_msg, reply_to_top_msg, None, None, None,
                notify, schedule_date, None, None, 0, False
            )
        except Exception as e:
            log(f"[downloader] Error preparing audio for sending: {e}, type: {type(e)}")
            log(f"[downloader] {traceback.format_exc()}")
            BulletinHelper.show_error(f"Error sending audio: {e}")

    def _delete_file_delayed(self, path, delay=60):
        def action():
            try:
                time.sleep(delay)
                if os.path.exists(path):
                    os.remove(path)
                    log(f"[downloader] Deleted temp file: {path}")
            except Exception as e:
                log(f"[downloader] Delayed delete error: {e}")

        threading.Thread(target=action, daemon=True).start()

    def _process_download_and_send(self, url, dialog_id, notify, schedule_date, mode_override=None, reply_to_msg=None, reply_to_top_msg=None):
        try:
            video_path = self._download_video(url, mode_override)
            if video_path:
                if os.path.exists(video_path):
                    log(f"[downloader] File exists, proceeding to send: {video_path}")
                    ext = os.path.splitext(video_path)[1].lower()
                    include_source = self.get_setting("include_source_set", True)
                    caption_text = f"Source: {url}" if include_source else None
                    account_instance = get_account_instance()
                    send_as_file = self.get_setting("send_as_file_set", False)
                    
                    if ext == ".gif":
                        self._send_gif_as_document(video_path, dialog_id, caption_text, notify, schedule_date, reply_to_msg, reply_to_top_msg)
                        self._delete_file_delayed(video_path)
                        self._dismiss_dialog()
                        return
                    
                    if send_as_file:
                        from java.util import Locale
                        lang = Locale.getDefault().getLanguage()
                        BulletinHelper.show_error("Извини, я немощный, не могу отправить .gif" if lang.startswith('ru') else "Sorry, I'm powerless, I cannot send .gif")
                        self._delete_file_delayed(video_path)
                        self._dismiss_dialog()
                        return
                    
                    image_exts = [".jpg", ".jpeg", ".png"]
                    audio_exts = [".mp3", ".wav", ".ogg", ".opus", ".m4a"]
                    if ext in image_exts:
                        send_as_file = self.get_setting("send_as_file_set", False)
                        if send_as_file:
                            mime = "application/octet-stream"
                            run_on_ui_thread(lambda: SendMessagesHelper.prepareSendingDocument(
                                account_instance,
                                video_path,
                                video_path,
                                None,
                                caption_text,
                                mime,
                                dialog_id,
                                reply_to_msg, reply_to_top_msg, None, None, None,
                                notify, schedule_date, None, None, 0, False
                            ))
                            self._delete_file_delayed(video_path)
                            self._dismiss_dialog()
                            return
                        if include_source:
                            entities = ArrayList()
                            ent = TLRPC.TL_messageEntityTextUrl()
                            ent.offset = 0; ent.length = len("Source"); ent.url = url
                            entities.add(ent)
                            SendMessagesHelper.prepareSendingPhoto(
                                account_instance, video_path, None, dialog_id,
                                reply_to_msg, reply_to_top_msg, None, "Source", entities,
                                None, None, 0, None, notify, schedule_date,
                                0, None, 0
                            )
                        else:
                            SendMessagesHelper.prepareSendingPhoto(
                                account_instance, video_path, None, dialog_id,
                                reply_to_msg, reply_to_top_msg, None, None, None,
                                None, None, 0, None, notify, schedule_date,
                                0, None, 0
                            )
                    elif ext in audio_exts:
                        self.send_audio(video_path, dialog_id, caption_text, notify, schedule_date, reply_to_msg, reply_to_top_msg)
                    elif ext == ".gif":
                        from java.util import Locale
                        lang = Locale.getDefault().getLanguage()
                        BulletinHelper.show_error("Извини, я немощный, не могу отправить .gif" if lang.startswith('ru') else "Sorry, I'm powerless, I cannot send .gif")
                        self._delete_file_delayed(video_path)
                        self._dismiss_dialog()
                        return
                    else:
                        if include_source:
                            entities = ArrayList()
                            ent = TLRPC.TL_messageEntityTextUrl()
                            ent.offset = 0; ent.length = len("Source"); ent.url = url
                            entities.add(ent)
                            SendMessagesHelper.prepareSendingVideo(
                                account_instance, video_path, None, None, None, dialog_id,
                                reply_to_msg, reply_to_top_msg, None, None, entities,
                                0, None, notify, schedule_date,
                                False, False, "Source", None, 0, 0, 0
                            )
                        else:
                            self.send_video(video_path, dialog_id, None, notify, schedule_date, reply_to_msg, reply_to_top_msg)
                    self._delete_file_delayed(video_path)
                    self._dismiss_dialog()
                else:
                    log(f"[downloader] Downloaded file not found after download: {video_path}")
                    BulletinHelper.show_error(f"Internal error: File not found after download")
                    self._dismiss_dialog()
                    return
            else:
                log(f"[downloader] Failed to download video for url: {url}")
                self._dismiss_dialog()
                return

        except Exception as e:
            self._dismiss_dialog()
            log(f"[downloader] Error in _process_download_and_send: {e}\n{traceback.format_exc()}")
            BulletinHelper.show_error(f"An unexpected error occurred: {e}")

    def _dismiss_dialog(self):
        global progress_dialog
        def action():
            global progress_dialog
            if progress_dialog is not None:
                try:
                    dlg = progress_dialog.get_dialog() if hasattr(progress_dialog, 'get_dialog') else progress_dialog
                    if dlg and dlg.isShowing():
                        dlg.dismiss()
                except Exception:
                    pass
                finally:
                    progress_dialog = None
        run_on_ui_thread(action)

    def _show_loading_alert(self):
        global progress_dialog
        self._cancel_requested = False
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
        builder.set_title("Downloading...")
        builder.set_negative_button("Cancel", self._on_progress_cancel)
        builder.set_cancelable(False)
        progress_dialog = builder.show()
        progress_dialog.set_progress(0)

    def _on_progress_cancel(self, builder, which):
        self._cancel_requested = True
        self._dismiss_dialog()

    def _copy_to_clipboard(self, label, text):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        clipboard = ctx.getSystemService(Context.CLIPBOARD_SERVICE)
        clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
        BulletinHelper.show_info(f"Copied {label} to clipboard")

    def _open_link(self, url):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        run_on_ui_thread(lambda: ctx.startActivity(intent))

    def show_my_info_alert(self, title="TITLE", message="MESSAGE", positive_button="OK", neutral_button=None, neutral_link=None, neutral_type=None):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button(positive_button, lambda b, w: self._dismiss_dialog(b))
        if neutral_button:
            if neutral_type == "link":
                builder.set_neutral_button(neutral_button, lambda b, w: self._open_link(neutral_link))
            else:
                builder.set_neutral_button(neutral_button, lambda b, w: self._copy_to_clipboard(neutral_button, neutral_link))
        self.alert_builder_instance = builder.show()

    def _on_show_settings_buttons_change(self, enabled: bool):
        def _toggle():
            try:
                if enabled:
                    if not self._drawer_settings_item:
                        self._drawer_settings_item = self.add_menu_item(MenuItemData(
                            menu_type=MenuItemType.DRAWER_MENU,
                            text=Z("settings_title"),
                            icon="msg_settings_14",
                            priority=5,
                            on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
                        ))
                    if not self._chat_settings_item:
                        self._chat_settings_item = self.add_menu_item(MenuItemData(
                            menu_type=MenuItemType.CHAT_ACTION_MENU,
                            text=Z("settings_title"),
                            icon="msg_settings_14",
                            priority=5,
                            on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
                        ))
                else:
                    if self._drawer_settings_item:
                        self.remove_menu_item(self._drawer_settings_item)
                        self._drawer_settings_item = None
                    if self._chat_settings_item:
                        self.remove_menu_item(self._chat_settings_item)
                        self._chat_settings_item = None
            except Exception as e:
                log(f"[{__id__}] Failed toggling settings buttons: {e}")
        run_on_ui_thread(_toggle)

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        msg = params.message.strip()
        if msg.startswith(".down ") or msg.startswith(".up ") or msg.startswith(".dl "):
            parts = msg.split()
            mode_override = None
            if len(parts) > 2:
                mode_override = parts[2].lower()
            if len(parts) < 2 or not parts[1]:
                BulletinHelper.show_error("No URL provided")
                return HookResult(strategy=HookStrategy.CANCEL)

            url = parts[1].strip()
            dialog_id = params.peer
            notify = True
            schedule_date = 0

            log(f"[downloader] Received command: {msg}. Starting background thread for URL: {url}")

            thread = threading.Thread(
                target=self._process_download_and_send,
                args=(url, dialog_id, notify, schedule_date, mode_override, params.replyToMsg, params.replyToTopMsg),
                daemon=True
            )
            thread.start()

            log("[downloader] Background thread started. Cancelling original message.")
            run_on_ui_thread(lambda: self._show_loading_alert())
            return HookResult(strategy=HookStrategy.CANCEL)

        return HookResult()

    def _download_with_downloadmanager(self, url, filename):
        try:
            download_manager = ApplicationLoader.applicationContext.getSystemService(Context.DOWNLOAD_SERVICE)
            request = DownloadManager.Request(Uri.parse(url))
            request.setTitle("Downloading media")
            request.setDescription("Downloading via DownloadManager")
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE)
            
            file_path = File(self._temp_dir, filename).getAbsolutePath()
            request.setDestinationUri(Uri.fromFile(File(file_path)))
            
            download_id = download_manager.enqueue(request)
            
            query = DownloadManager.Query()
            query.setFilterById(download_id)
            
            while True:
                cursor = download_manager.query(query)
                if cursor.moveToFirst():
                    status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS))
                    if status == DownloadManager.STATUS_SUCCESSFUL:
                        return file_path
                    elif status == DownloadManager.STATUS_FAILED:
                        raise Exception("Download failed")
                time.sleep(1)
            
        except Exception as e:
            log(f"[downloader] DownloadManager error: {e}")
            return None

    def _send_gif_as_document(self, gif_path, dialog_id, caption, notify, schedule_date, reply_to_msg=None, reply_to_top_msg=None):
        try:
            account_instance = get_account_instance()
            if account_instance is None:
                log("[downloader] Error: Could not get AccountInstance.")
                return
            
            ext_cache_root = ApplicationLoader.applicationContext.getExternalCacheDir()
            plugin_ext_dir = File(ext_cache_root, TEMP_DIR_NAME)
            if not plugin_ext_dir.exists() and not plugin_ext_dir.mkdirs():
                log("[downloader] Failed to create external temp dir")
            
            external_path = File(plugin_ext_dir, "animation.gif").getAbsolutePath()
            with open(gif_path, 'rb') as f_in, open(external_path, 'wb') as f_out:
                while True:
                    chunk = f_in.read(8192)
                    if not chunk:
                        break
                    f_out.write(chunk)
            
            SendMessagesHelper.prepareSendingDocument(
                account_instance, 
                external_path, 
                external_path, 
                None, 
                caption, 
                "image/gif", 
                dialog_id,
                reply_to_msg, 
                reply_to_top_msg, 
                None, 
                None, 
                None,
                notify, 
                schedule_date, 
                None, 
                None, 
                0, 
                False
            )
            
            log(f"[downloader] GIF sent successfully: {external_path}")
            
        except Exception as e:
            log(f"[downloader] Error sending GIF: {e}\n{traceback.format_exc()}")
            BulletinHelper.show_error(f"Error sending GIF: {e}")

    def _test_current_proxy(self):
        def test_proxy():
            try:
                proxy_type = self.get_setting("proxy_type_set", 0)
                if proxy_type == 0:
                    BulletinHelper.show_error("Прокси не настроен" if Locale.getDefault().getLanguage().startswith('ru') else "No proxy configured")
                    return
                
                if proxy_type == 4:
                    success = self._test_mtproto_connection()
                elif proxy_type == 3:
                    success = self._test_socks_connection()
                else:
                    proxies = self._get_proxies()
                    if proxies and not isinstance(proxies, dict):
                        success = self._test_proxy_connection(proxies)
                    else:
                        success = False
                
                if success:
                    BulletinHelper.show_success("Тест подключения прокси успешен!" if Locale.getDefault().getLanguage().startswith('ru') else "Proxy connection test successful!")
                else:
                    BulletinHelper.show_error("Тест подключения прокси не удался" if Locale.getDefault().getLanguage().startswith('ru') else "Proxy connection test failed")
                    
            except Exception as e:
                log(f"[{__id__}] Proxy test error: {e}")
                BulletinHelper.show_error(f"Ошибка теста прокси: {e}" if Locale.getDefault().getLanguage().startswith('ru') else f"Proxy test error: {e}")
        
        threading.Thread(target=test_proxy, daemon=True).start()

    def _test_socks_connection(self):
        try:
            url_setting = self.get_setting('proxy_url_set','').strip()
            if not url_setting or ':' not in url_setting:
                return False
            
            if '://' in url_setting:
                url_setting = url_setting.split('://', 1)[1]
            
            host, port_str = url_setting.split(':', 1)
            try:
                port = int(port_str)
            except ValueError:
                return False
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((host, port))
            sock.close()
            
            log(f"[{__id__}] SOCKS proxy connection test successful")
            return True
            
        except Exception as e:
            log(f"[{__id__}] SOCKS proxy connection test failed: {e}")
            return False
