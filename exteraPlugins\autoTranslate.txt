import requests
from ui.settings import <PERSON><PERSON>, Switch, Selector, Text, Divider, Input
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from java.util import Locale
import time
from typing import Optional, Any, List
from android_utils import log, run_on_ui_thread
from base_plugin import Menu<PERSON>temD<PERSON>, MenuItemType
from ui.bulletin import BulletinHelper
from client_utils import run_on_queue, send_message, get_last_fragment
from ui.alert import AlertDialogBuilder
from org.telegram.ui.ActionBar import AlertDialog
import re
import random
import html
from android.content import ClipData, Context
from org.telegram.messenger import R as R_tg

# Extra imports for settings shortcut feature
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity

__id__ = "autoTranslate"
__name__ = "Auto Translate"
__description__ = "Automatically translates outgoing messages using Google Translate. Toggle in settings."
__author__ = "@luvztroy"
__min_version__ = "11.9.0"
__version__ = "1.0.4"
__icon__ = "luvztroy/0"
__category__ = "utility"
__priority__ = 100

TRANSLATE_API_URL = "https://translate.googleapis.com/translate_a/single"

# Re-use a single HTTP session for all network calls to avoid TLS hand-shake latency
_http = requests.Session()

# ---- OpenRouter (OpenAI-compatible) endpoint ----
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"

class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"

    def get_string(self, key):
        return self.strings[self.language].get(key, key)

    def _get_supported_languages(self):
        return self.strings.keys()

    strings = {
        "en": {
            "SETTINGS_TITLE": "Auto Translate Settings",
            "ENABLE_TRANSLATOR": "Enable Outgoing Auto Translator",
            "ENABLE_TRANSLATOR_SUB": "Automatically translate outgoing messages to the selected language.",
            "TARGET_LANGUAGE": "Target Language",
            "BYPASS_COMMANDS": "Bypass Commands",
            "BYPASS_COMMANDS_SUB": "Skip translation for messages starting with '!'",
            "TRANSLATION_ERROR": "Translation failed! Message not sent."
        },
        "ru": {
            "SETTINGS_TITLE": "Настройки автоперевода",
            "ENABLE_TRANSLATOR": "Включить автопереводчик исходящих",
            "ENABLE_TRANSLATOR_SUB": "Автоматически переводить исходящие сообщения на выбранный язык.",
            "TARGET_LANGUAGE": "Язык перевода",
            "BYPASS_COMMANDS": "Пропускать команды",
            "BYPASS_COMMANDS_SUB": "Пропускать перевод для сообщений, начинающихся с '!'",
            "TRANSLATION_ERROR": "Ошибка перевода! Сообщение не отправлено."
        }
    }

LANG_CODES = [
    "af", "am", "ar", "as", "ay", "az", "be", "bg", "bn", "bs", "ca", "ceb", "co", "cs", "cy", "da", "de", "el", "en", "eo", "es", "et", "eu", "fa", "fi", "fil", "fr", "fy", "ga", "gd", "gl", "gu", "ha", "haw", "he", "hi", "hi-hinglish", "hmn", "hr", "ht", "hu", "hy", "id", "ig", "is", "it", "iw", "ja", "jw", "ka", "kk", "km", "kn", "ko", "ku", "ky", "la", "lb", "lo", "lt", "lv", "mg", "mi", "mk", "ml", "mn", "mr", "ms", "mt", "my", "ne", "nl", "no", "ny", "or", "pa", "pl", "ps", "pt", "ro", "ru", "si", "sk", "sl", "sm", "sn", "so", "sq", "sr", "st", "su", "sv", "sw", "ta", "te", "tg", "th", "tk", "tl", "tr", "tt", "ug", "uk", "ur", "uz", "vi", "xh", "yi", "yo", "zh", "zu"
]
LANG_NAMES = [
    "Afrikaans", "Amharic", "Arabic", "Assamese", "Aymara", "Azerbaijani", "Belarusian", "Bulgarian", "Bengali", "Bosnian", "Catalan", "Cebuano", "Corsican", "Czech", "Welsh", "Danish", "German", "Greek", "English", "Esperanto", "Spanish", "Estonian", "Basque", "Persian", "Finnish", "Filipino", "French", "Frisian", "Irish", "Scots Gaelic", "Galician", "Gujarati", "Hausa", "Hawaiian", "Hebrew", "Hindi", "Hinglish", "Hmong", "Croatian", "Haitian Creole", "Hungarian", "Armenian", "Indonesian", "Igbo", "Icelandic", "Italian", "Hebrew", "Japanese", "Javanese", "Georgian", "Kazakh", "Khmer", "Kannada", "Korean", "Kurdish", "Kyrgyz", "Latin", "Luxembourgish", "Lao", "Lithuanian", "Latvian", "Malagasy", "Maori", "Macedonian", "Malayalam", "Mongolian", "Marathi", "Malay", "Maltese", "Myanmar", "Nepali", "Dutch", "Norwegian", "Nyanja", "Odia", "Punjabi", "Polish", "Pashto", "Portuguese", "Romanian", "Russian", "Sinhala", "Slovak", "Slovenian", "Samoan", "Shona", "Somali", "Albanian", "Serbian", "Sesotho", "Sundanese", "Swedish", "Swahili", "Tamil", "Telugu", "Tajik", "Thai", "Turkmen", "Tagalog", "Turkish", "Tatar", "Uyghur", "Ukrainian", "Urdu", "Uzbek", "Vietnamese", "Xhosa", "Yiddish", "Yoruba", "Chinese", "Zulu"
]

locali = LocalizationManager()

class AutoTranslatePlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.translation_cache = {}
        self.cache_timeout = 300
        self.last_cache_cleanup = time.time()
        self.is_sending_translated = False
        self.progress_dialog: Optional[AlertDialogBuilder] = None
        # Load excluded chats list
        self.excluded_chats: set[int] = set()

        # Handles for dynamically added settings menu items
        self._drawer_settings_item = None
        self._chat_settings_item = None

        # -------- Premium emoji tag preservation --------
        # Regex for <emoji .../> or <emoji ...>...</emoji>
        self.PREMIUM_EMO_TAG_RE = re.compile(r'<emoji[^>]*?/?>|<emoji[^>]*?>.*?</emoji>', flags=re.S | re.I)

    def on_plugin_load(self):
        print("AutoTranslatePlugin loaded")
        self.add_on_send_message_hook()

        # Load excluded chats from settings
        try:
            raw = self.get_setting("excluded_chats", "")
            self.excluded_chats = set(int(x) for x in raw.split(",") if x.strip())
        except Exception as e:
            log(f"Failed to load excluded chats: {e}")
            self.excluded_chats = set()

        # Add a composer action button to quickly toggle the translator
        try:
            self.add_menu_item(
                MenuItemData(
                    menu_type=MenuItemType.COMPOSER_ACTION_MENU,
                    text="Auto Translate",
                    icon="ai_chat_solar",
                    on_click=self._toggle_enabled
                )
            )
        except Exception as e:
            log(f"Failed to add composer toggle: {e}")

        # initialise menu items based on stored setting (default true)
        self._add_settings_menu_items()

    def cleanup_cache(self):
        current_time = time.time()
        if current_time - self.last_cache_cleanup > 3600:
            expired_keys = []
            for key, (translation, timestamp) in self.translation_cache.items():
                if current_time - timestamp > self.cache_timeout:
                    expired_keys.append(key)
            for key in expired_keys:
                del self.translation_cache[key]
            self.last_cache_cleanup = current_time

    def get_cached_translation(self, text, target_lang):
        cache_key = f"{text}:{target_lang}"
        if cache_key in self.translation_cache:
            translation, timestamp = self.translation_cache[cache_key]
            if time.time() - timestamp < self.cache_timeout:
                return translation
        return None

    def cache_translation(self, text, target_lang, translation):
        cache_key = f"{text}:{target_lang}"
        self.translation_cache[cache_key] = (translation, time.time())
        self.cleanup_cache()

    def extract_emojis(self, text):
        """Extract premium <emoji> tags AND regular Unicode emoji characters.
        Returns: (text_with_placeholders, list_of_original_emoji_strings)"""

        # Pattern for Telegram premium emoji tags
        tag_pattern = r'<emoji[^>]*?>.*?</emoji>|<emoji[^>]*/?>'

        # Unicode emoji ranges (covers majority of pictographs). We keep it broad but effective.
        unicode_ranges = (
            '\U0001F1E6-\U0001F1FF'  # Flags
            '\U0001F300-\U0001F5FF'  # Misc symbols and pictographs
            '\U0001F600-\U0001F64F'  # Emoticons
            '\U0001F680-\U0001F6FF'  # Transport & map symbols
            '\U0001F700-\U0001F77F'  # Alchemical symbols
            '\U0001F780-\U0001F7FF'  # Geometric shapes extended
            '\U0001F800-\U0001F8FF'  # Supplemental arrows-C
            '\U0001F900-\U0001F9FF'  # Supplemental symbols and pictographs
            '\U0001FA70-\U0001FAFF'  # Symbols & pictographs extended-A
            '\u2600-\u26FF'          # Misc symbols
            '\u2700-\u27BF'          # Dingbats
        )

        unicode_pattern = rf'[\u200d\ufe0f]|[{unicode_ranges}]'

        emojis: List[str] = []

        # Unified pattern: Telegram emoji tags OR Unicode emoji chars
        combined_pattern = re.compile(rf'({tag_pattern}|{unicode_pattern})', flags=re.S)

        def replacer(match):
            idx = len(emojis)
            emojis.append(match.group(0))
            return f"[[EMOJI_{idx}]]"

        text_with_placeholders = combined_pattern.sub(replacer, text)

        return text_with_placeholders, emojis

    def restore_emojis(self, text, emojis):
        """Replace placeholders like [[EMOJI_0]] (and loose variants mangled by translators)
        back with their original emoji strings."""
        restored = text
        for idx, em in enumerate(emojis):
            # Match placeholders even if 'EMOJI' word got translated to other scripts
            # Accept any non-digit chars before the index.
            pattern = re.compile(rf"\[\[[^0-9]*_?{idx}\]?\]?", flags=re.UNICODE)
            restored = pattern.sub(em, restored)
        return restored

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        if self.is_sending_translated:
            # Unlock and ignore this message (it's the translated one we just sent)
            self.is_sending_translated = False
            return HookResult()

        if not self.get_setting("enable_translator", False):
            return HookResult()

        # ---- One-time AI grammar notice (only if AI not enabled) ----
        try:
            if (not self.get_setting("or_enable", False)) and self.get_setting("ai_notice_shown", "0") != "1":
                self.set_setting("ai_notice_shown", "1")  # prevent future notices
                run_on_ui_thread(lambda: BulletinHelper.show_info("Tip: You can now fix grammar with AI — enable it in plugin settings."))
        except Exception as e:
            log(f"AI notice bulletin error: {e}")

        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        original_text = params.message.strip()

        # Respect user setting whether commands (starting with / or !) should bypass translation
        bypass_commands = self.get_setting("bypass_commands", True)

        # NEW: Skip translation for external plugin commands that start with a dot (".")
        # Many other plugins trigger on messages like ".command". We simply ignore such
        # messages so they can be handled by their respective plugins without interference
        # from the auto-translator.
        if original_text.startswith('.'):
            return HookResult()

        # Simple per-chat enable / disable commands
        if original_text.lower() == "!off":
            self._exclude_chat(params.peer)
            return HookResult(strategy=HookStrategy.CANCEL)
        if original_text.lower() == "!on":
            self._include_chat(params.peer)
            return HookResult(strategy=HookStrategy.CANCEL)

        # Skip translation entirely if this chat is excluded
        if getattr(params, "peer", None) in self.excluded_chats:
            return HookResult()

        # If the message has no Unicode word characters (likely only emojis / stickers) skip translation to prevent
        # unnecessary API calls and avoid showing the spinner.
        if not re.search(r"\w", original_text, flags=re.UNICODE):
            return HookResult()

        # Quick command: !tl <lang_code>  -> set target language on the fly
        m_lang = re.match(r"^!tl\s+([a-zA-Z\-]+)$", original_text)
        if m_lang:
            code = m_lang.group(1).lower()
            if code in LANG_CODES:
                idx = LANG_CODES.index(code)
                self.set_setting("target_language", idx)
                BulletinHelper.show_info(f"Target language set to {LANG_NAMES[idx]}")
            else:
                BulletinHelper.show_info("Unknown language code")
            return HookResult(strategy=HookStrategy.CANCEL)

        # Quick command: !ex <codes>|none   — supports multiple comma/space-separated language codes
        m_ex = re.match(r"^!ex\s+(.+)$", original_text)
        if m_ex:
            arg = m_ex.group(1).strip().lower()
            if arg == "none":
                self.set_setting("exclude_languages", "")
                BulletinHelper.show_info("Excluded languages cleared")
            else:
                # split by commas and/or whitespace
                parts = re.split(r"[\s,]+", arg)
                codes = [c for c in parts if c in LANG_CODES]
                if not codes:
                    BulletinHelper.show_info("Unknown language code(s)")
                else:
                    self.set_setting("exclude_languages", ",".join(codes))
                    names = ", ".join(LANG_NAMES[LANG_CODES.index(c)] for c in codes)
                    BulletinHelper.show_info(f"Messages in {names} will be left untranslated")
            return HookResult(strategy=HookStrategy.CANCEL)

        # Quick command: !google or !yandex  -> switch translation provider on the fly
        if re.match(r"^!google$", original_text, flags=re.I):
            self.set_setting("provider", 0)
            BulletinHelper.show_info("Translation provider set to Google")
            return HookResult(strategy=HookStrategy.CANCEL)

        if re.match(r"^!yandex$", original_text, flags=re.I):
            self.set_setting("provider", 1)
            BulletinHelper.show_info("Translation provider set to Yandex")
            return HookResult(strategy=HookStrategy.CANCEL)

        # Developer mode toggle command: !dev
        if re.match(r"^!dev$", original_text, flags=re.I):
            dev = self.get_setting("dev_mode_enabled", False)
            self.set_setting("dev_mode_enabled", not dev)
            state_msg = "Developer mode enabled" if not dev else "Developer mode disabled"
            try:
                run_on_ui_thread(lambda: BulletinHelper.show_success(state_msg))
            except Exception:
                BulletinHelper.show_info(state_msg)
            return HookResult(strategy=HookStrategy.CANCEL)

        # Special prefix !keep : translate and keep translated text in input field
        keep_in_composer = False

        # ----- AI Chat command: !ai -----
        if self.get_setting("dev_mode_enabled", False):
            m_ai_query = re.match(r"^!ai(?:\s+(.*))?$", original_text, flags=re.S | re.I)
        else:
            m_ai_query = None
        if m_ai_query:
            question = (m_ai_query.group(1) or "").strip()
            if not question:
                BulletinHelper.show_info("Usage: !ai <your question>")
                return HookResult(strategy=HookStrategy.CANCEL)

            peer = getattr(params, "peer", None)
            reply_obj = getattr(params, "replyToMsg", None)
            reply_top_obj = getattr(params, "replyToTopMsg", None)

            # Delegate to async AI handler (shows spinner and sends reply)
            self._perform_ai_chat_and_send(peer, question, reply_obj, reply_top_obj)
            return HookResult(strategy=HookStrategy.CANCEL)

        # Treat messages starting with '!' (unless they are plugin control commands) OR '/'
        if bypass_commands and (original_text.startswith("/") or original_text.startswith("!")):
            # Keep plugin control commands functioning
            if original_text.startswith("!") and re.match(r"^!(tl|ex|on|off|google|yandex)\b", original_text, flags=re.I):
                pass  # let further logic process
            else:
                if original_text.startswith("/"):
                    # Let Telegram send the slash command untouched so it remains clickable
                    return HookResult()  # do nothing, no translation

                # For exclamation bypass: strip leading '!'
                cleaned_text = original_text[1:].lstrip()
                if not cleaned_text:
                    return HookResult()

                peer = getattr(params, "peer", None)
                reply_obj = getattr(params, "replyToMsg", None)
                reply_top_obj = getattr(params, "replyToTopMsg", None)

                def _send_bypass():
                    self.is_sending_translated = True
                    payload = {"peer": peer, "message": cleaned_text}
                    if reply_obj is not None:
                        payload["replyToMsg"] = reply_obj
                    if reply_top_obj is not None:
                        payload["replyToTopMsg"] = reply_top_obj
                    send_message(payload)

                run_on_queue(_send_bypass)
                return HookResult(strategy=HookStrategy.CANCEL)

        # Network connectivity check
        try:
            from org.telegram.messenger import ApplicationLoader
            if not ApplicationLoader.isNetworkOnline():
                BulletinHelper.show_error("No internet connection")
                if keep_in_composer:
                    self._set_composer_text(original_text)
                return HookResult(strategy=HookStrategy.CANCEL)
        except Exception as e:
            log(f"network check error: {e}")

        # (Removed synchronous language detection to avoid UI lag; handled later in background thread)

        try:
            current_fragment = get_last_fragment()
            if not current_fragment or not current_fragment.getParentActivity():
                log("autoTranslate: Could not get context to show dialog.")
                return HookResult(strategy=HookStrategy.CANCEL)

            # Always show spinner while translating (even for short texts)
            if not keep_in_composer:
                self.progress_dialog = AlertDialog(current_fragment.getParentActivity(), 3)
                self.progress_dialog.show()

            # Prevent translation loop by locking before starting async work
            self.is_sending_translated = True

            peer = getattr(params, "peer", None)
            reply_obj = getattr(params, "replyToMsg", None)
            reply_top_obj = getattr(params, "replyToTopMsg", None)
            run_on_queue(lambda: self._perform_translation_and_send(peer, original_text, reply_obj, reply_top_obj, keep_in_composer))

            return HookResult(strategy=HookStrategy.CANCEL)
        except Exception as e:
            log(f"autoTranslate plugin error: {str(e)}")
            if self.progress_dialog:
                run_on_ui_thread(lambda: self.progress_dialog.dismiss())
                self.progress_dialog = None
            return HookResult()

    def _perform_translation_and_send(self, peer, original_text, reply_obj, reply_top_obj, keep_in_composer=False):
        lang_index = self.get_setting("target_language", 0)
        if not 0 <= lang_index < len(LANG_CODES):
            lang_index = 0
            self.set_setting("target_language", 0)
        target_lang = LANG_CODES[lang_index]

        # Multi-language exclusion
        raw_excl = self.get_setting("exclude_languages", "")
        exclude_list = [c for c in raw_excl.split(',') if c]
        if exclude_list:
            try:
                if any(self._should_skip_language(original_text, code) for code in exclude_list):
                    self._send_final_message(peer, original_text, reply_obj, reply_top_obj, keep_in_composer)
                    return
            except Exception as e:
                log(f"Error checking excluded language list: {str(e)}")

        # Apply excluded words masking if advanced settings enabled
        masked_text, placeholders = self._mask_excluded_words(original_text)

        translated_text = self.translate_text(masked_text, target_lang)

        # Restore excluded words placeholders
        translated_text = self._restore_excluded_words(translated_text, placeholders)

        # NEW FEATURES: Preserve casing style automatically
        if original_text.isupper():
            translated_text = translated_text.upper()
        elif original_text.islower():
            translated_text = translated_text.lower()

        # Always preserve trailing ellipsis
        orig_trim = original_text.rstrip()
        fin_trim = translated_text.rstrip()
        if orig_trim.endswith("...") and not fin_trim.endswith("..."):
            translated_text = translated_text + "..."
        elif orig_trim.endswith("…") and not fin_trim.endswith("…"):
            translated_text = translated_text + "…"

        self._send_final_message(peer, translated_text, reply_obj, reply_top_obj, keep_in_composer)

    def _send_final_message(self, peer, text, reply_obj=None, reply_top_obj=None, keep_in_composer=False):
        def _ui_task():
            if self.progress_dialog:
                self.progress_dialog.dismiss()
                self.progress_dialog = None

            if text:
                self.is_sending_translated = True
                try:
                    payload = {"peer": peer, "message": text}
                    if reply_obj is not None:
                        payload["replyToMsg"] = reply_obj
                    if reply_top_obj is not None:
                        payload["replyToTopMsg"] = reply_top_obj
                    send_message(payload)
                finally:
                    # Lock stays true; will be cleared on the next hook invocation for this translated message
                    pass
            else:
                error_dialog = AlertDialogBuilder()
                error_dialog.set_title("Error")
                error_dialog.set_message(locali.get_string("TRANSLATION_ERROR"))
                error_dialog.set_positive_button("OK", None)
                error_dialog.show()

        run_on_ui_thread(_ui_task)

    def detect_language(self, text):
        try:
            params = {'client': 'gtx', 'sl': 'auto', 'tl': 'en', 'dt': 't', 'q': text}
            response = _http.get(TRANSLATE_API_URL, params=params, timeout=5)
            if response.status_code == 200:
                return response.json()[2]
        except Exception as e:
            log(f"Language detection failed: {str(e)}")
        return None

    def translate_text(self, text, target_lang):
        # Preserve premium emoji tags using HTML placeholder technique
        encoded = self._encode_premium_tags(text)

        cached = self.get_cached_translation(encoded, target_lang)
        if cached:
            out = self._decode_premium_tags(cached)
            return self._openrouter_improve(out, target_lang)

        translated = self._perform_external_translation(encoded, target_lang)
        if translated:
            self.cache_translation(encoded, target_lang, translated)
            out = self._decode_premium_tags(translated)
            return self._openrouter_improve(out, target_lang)

        # If translation failed, return original text (decoded)
        return self._openrouter_improve(self._decode_premium_tags(encoded), target_lang)

    # ---- helper that performs actual HTTP translation & caching ----
    def _translate_plain_text(self, plain_text, target_lang):
        # Strip Telegram premium emoji tags, keep normal Unicode emoji intact
        tag_regex = re.compile(r'<emoji[^>]*?>.*?</emoji>|<emoji[^>]*/?>', flags=re.S)
        sanitized = tag_regex.sub('', plain_text)

        # If nothing except spaces or symbols remains, skip translation
        if not re.search(r"\w", sanitized, flags=re.UNICODE):
            return plain_text

        # Cache on sanitized string (avoids separate entries when tags differ)
        cached = self.get_cached_translation(sanitized, target_lang)
        if cached:
            return cached

        translated = self._perform_external_translation(sanitized, target_lang)
        if translated:
            self.cache_translation(sanitized, target_lang, translated)
            return translated
        return plain_text

    # ---- isolated external API call logic ----
    def _perform_external_translation(self, text_to_translate, target_lang):
        plain_text = text_to_translate
        
        provider_index = self.get_setting("provider", 0)
        providers = ["google", "yandex"]
        provider = providers[provider_index] if provider_index in range(len(providers)) else "yandex"
        log(f"Using provider: {provider}")
        
        if target_lang == "hi-hinglish":
            try:
                hindi_translation = None
                if provider == "yandex":
                    hindi_translation = self.translate_with_yandex(plain_text, "hi")
                else:
                    params = {
                        'client': 'gtx',
                        'sl': 'auto',
                        'tl': 'hi',
                        'dt': 't',
                        'q': plain_text
                    }
                    response = _http.get(TRANSLATE_API_URL, params=params, timeout=5)
                    if response.status_code == 200:
                        result = response.json()
                        if result and isinstance(result, list) and result[0] and isinstance(result[0], list):
                            hindi_translation = result[0][0][0]

                if hindi_translation:
                    hinglish = hindi_translation
                    translit_map = {
                        # Vowels
                        'अ': 'a',  'आ': 'aa', 'इ': 'i',  'ई': 'ee', 'उ': 'u',  'ऊ': 'oo',
                        'ऋ': 'ri', 'ॠ': 'ree', 'ऌ': 'li', 'ॡ': 'lee', 'ए': 'e',  'ऐ': 'ai',
                        'ओ': 'o',  'औ': 'au', 'अं': 'an', 'अः': 'ah',

                        # Vowel marks
                        'ा': 'aa', 'ि': 'i',  'ी': 'ee', 'ु': 'u',  'ू': 'oo',
                        'ृ': 'ri', 'ॄ': 'ree', 'ॢ': 'li', 'ॣ': 'lee',
                        'े': 'e',  'ै': 'ai', 'ो': 'o',  'ौ': 'au',
                        'ं': 'n',  'ः': 'h',  '़': '',   '्': '',

                        # Consonants
                        'क': 'k',  'ख': 'kh', 'ग': 'g',  'घ': 'gh', 'ङ': 'ng',
                        'च': 'ch', 'छ': 'chh','ज': 'j',  'झ': 'jh', 'ञ': 'ny',
                        'ट': 't',  'ठ': 'th', 'ड': 'd',  'ढ': 'dh', 'ण': 'n',
                        'त': 't',  'थ': 'th', 'द': 'd',  'ध': 'dh', 'न': 'n',
                        'प': 'p',  'फ': 'ph', 'ब': 'b',  'भ': 'bh', 'म': 'm',
                        'य': 'y',  'र': 'r',  'ल': 'l',  'व': 'w',
                        'श': 'sh', 'ष': 'sh', 'स': 's',  'ह': 'h',
                        'ळ': 'l',  'क्ष': 'ksh','ज्ञ': 'gya',

                        # Punctuation
                        '।': '.',  '?': '?',  '!': '!',  ',': ',',  ';': ';', ':': ':'
                    }
                    
                    for hindi, roman in translit_map.items():
                        hinglish = hinglish.replace(hindi, roman)
                    
                    hinglish = ''.join(char for char in hinglish if ord(char) < 128)
                    hinglish = ' '.join(hinglish.split())
                    
                    return hinglish
            except Exception as e:
                log(f"Error in Hinglish translation: {str(e)}")
                return plain_text
        
        if provider == "yandex":
            fallback_enabled = self.get_setting("fallback_enabled", True)
            translation = self.translate_with_yandex(plain_text, target_lang)
            if translation and translation.strip().lower() != plain_text.strip().lower():
                return translation
            if fallback_enabled:
                # Fallback to google only if enabled
                return self._perform_external_translation_google(plain_text, target_lang)
            return None
        else:
            try:
                # Preserve emojis by using placeholders during translation
                sanitized_text, emojis = self.extract_emojis(plain_text)

                clients = ['gtx']
                translation = None
                
                for client in clients:
                    try:
                        params = {
                            'client': client,
                            'sl': 'auto',
                            'tl': target_lang,
                            'dt': 't',
                            'q': sanitized_text
                        }
                        
                        response = _http.get(TRANSLATE_API_URL, params=params, timeout=5)
                        if response.status_code == 200:
                            result = response.json()
                            
                            if client == 'translate_a_s':
                                if result and isinstance(result, list) and len(result) > 0:
                                    translation_parts = [
                                        seg[0] for seg in result[0]
                                        if isinstance(seg, list) and len(seg) > 0 and isinstance(seg[0], str)
                                    ]
                                    translation = "".join(translation_parts)
                            else:
                                if result and isinstance(result, list) and result[0] and isinstance(result[0], list):
                                    translation_parts = [
                                        seg[0] for seg in result[0]
                                        if isinstance(seg, list) and len(seg) > 0 and isinstance(seg[0], str)
                                    ]
                                    translation = "".join(translation_parts)
                            
                            if translation and translation.strip().lower() != plain_text.strip().lower():
                                # Restore emoji placeholders
                                translated = self.restore_emojis(translation, emojis)
                                return translated
                            else:
                                translation = None  # Treat as failed translation to trigger fallback
                    except Exception as e:
                        continue
                
                if not translation:
                    fallback_enabled = self.get_setting("fallback_enabled", True)
                    yandex_translation = self.translate_with_yandex(plain_text, target_lang)
                    if yandex_translation and yandex_translation.strip().lower() != plain_text.strip().lower():
                        return yandex_translation
                    if fallback_enabled:
                        return self._perform_external_translation_google(plain_text, target_lang)
                    return None
                    
            except Exception as e:
                fallback_enabled = self.get_setting("fallback_enabled", True)
                yandex_translation = self.translate_with_yandex(plain_text, target_lang)
                if yandex_translation and yandex_translation.strip().lower() != plain_text.strip().lower():
                    return yandex_translation
                if fallback_enabled:
                    return self._perform_external_translation_google(plain_text, target_lang)
                return None

    def _perform_external_translation_google(self, text, target_lang):
        try:
            sanitized_text, emojis = self.extract_emojis(text)

            params = {
                'client': 'gtx',
                'sl': 'auto',
                'tl': target_lang,
                'dt': 't',
                'q': sanitized_text
            }
            response = _http.get(TRANSLATE_API_URL, params=params, timeout=5)
            if response.status_code == 200:
                result = response.json()
                if result and isinstance(result, list) and result[0] and isinstance(result[0], list):
                    if result and isinstance(result[0], list):
                        translated_segments = [
                            seg[0] for seg in result[0]
                            if isinstance(seg, list) and len(seg) > 0 and isinstance(seg[0], str)
                        ]
                        translated = "".join(translated_segments)
                    translated = self.restore_emojis(translated, emojis)
                    return translated
        except Exception as e:
            log(f"Google fallback failed: {str(e)}")
        return None

    # ----- Exclude language helper -----
    def _should_skip_language(self, text, exclude_lang):
        # 1) Google detection
        try:
            detected = self.detect_language(text)
            if detected == exclude_lang:
                return True
        except Exception:
            pass
        # 2) Unicode script heuristic
        script_ranges = {
            'ru': (0x0400, 0x04FF),  # Cyrillic
            'uk': (0x0400, 0x04FF),
            'bg': (0x0400, 0x04FF),
            'el': (0x0370, 0x03FF),  # Greek
            'ar': (0x0600, 0x06FF),
            'he': (0x0590, 0x05FF),
            'hi': (0x0900, 0x097F),
            'ja': (0x3040, 0x30FF),
            'ko': (0xAC00, 0xD7AF),
            'zh': (0x4E00, 0x9FFF),
        }
        rng = script_ranges.get(exclude_lang)
        if rng:
            start, end = rng
            for ch in text:
                if start <= ord(ch) <= end:
                    return True
        return False

    def translate_with_yandex(self, text, target_lang):
        import uuid
        import urllib.parse
        YA_URL = "https://translate.yandex.net/api/v1/tr.json/translate"
        session_uuid = str(uuid.uuid4()).replace("-", "")
        to_lang = target_lang.lower()
        # --- Emoji preservation: replace emojis with placeholders before sending ---
        sanitized_text, emojis = self.extract_emojis(text)

        data = f"lang={to_lang}&text={urllib.parse.quote(sanitized_text)}"
        headers = {
            "User-Agent": "ru.yandex.translate/21.15.4.21402814 (Xiaomi Redmi K20 Pro; Android 11)",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        url = f"{YA_URL}?id={session_uuid}-0-0&srv=android"
        try:
            response = _http.post(url, data=data, headers=headers, timeout=5)
            if response.status_code == 200:
                obj = response.json()
                if "text" in obj:
                    translated = "".join(obj["text"])
                    # Restore any emoji placeholders
                    translated = self.restore_emojis(translated, emojis)
                    return translated
            return None
        except Exception as e:
            log(f"Yandex translation failed: {str(e)}")
            return None

    # ----- Excluded chat helpers -----
    def _save_excluded(self):
        try:
            raw = ",".join(str(x) for x in self.excluded_chats)
            self.set_setting("excluded_chats", raw)
        except Exception as e:
            log(f"Save excluded error: {e}")

    def _exclude_chat(self, peer_id):
        try:
            if peer_id is None:
                return
            self.excluded_chats.add(peer_id)
            self._save_excluded()
            run_on_ui_thread(lambda: BulletinHelper.show_error("Auto Translate disabled in this chat"))
        except Exception as e:
            log(f"Exclude chat error: {e}")

    def _include_chat(self, peer_id):
        try:
            if peer_id is None:
                return
            self.excluded_chats.discard(peer_id)
            self._save_excluded()
            run_on_ui_thread(lambda: BulletinHelper.show_success("Auto Translate enabled in this chat"))
        except Exception as e:
            log(f"Include chat error: {e}")

    # ----- Quick toggle handler -----
    def _toggle_enabled(self, context):
        try:
            enabled = self.get_setting("enable_translator", False)
            self.set_setting("enable_translator", not enabled)
            state = "enabled" if not enabled else "disabled"
            ui_call = BulletinHelper.show_success if state=="enabled" else BulletinHelper.show_error
            run_on_ui_thread(lambda: ui_call(f"Auto Translate {state}"))
        except Exception as e:
            log(f"Toggle error: {e}")

    # ----- Help dialog -----
    def _show_help_dialog(self, view=None):
        """Display a help/usage dialog with commands and language codes."""
        try:
            fragment = get_last_fragment()
            from org.telegram.messenger import ApplicationLoader
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext

            builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)

            # Build the help text
            dev = self.get_setting("dev_mode_enabled", False)
            cmd_lines = [
                "Commands:",
                "• !tl <code> — set target language (e.g., es)",
                "• !ex <codes|none> — exclude languages",
                "• !on / !off — enable / disable in this chat",
                "• !google / !yandex — switch provider",
                "• prefix '!' — send without translation",
                "• prefix '.' — pass to other plugins",
            ]

            cmd_section = "\n".join(cmd_lines) + "\n\nUse standard language codes (en, es, fr, ru, etc.)."

            help_text = cmd_section

            builder.set_title("Auto Translate Help")
            builder.set_message(help_text)
            builder.set_positive_button("OK", None)
            run_on_ui_thread(lambda: builder.show())
        except Exception as e:
            log(f"Help dialog error: {e}")

    def create_settings(self):
        return [
            Header(locali.get_string("SETTINGS_TITLE")),
            Switch(
                key="enable_translator",
                text=locali.get_string("ENABLE_TRANSLATOR"),
                default=False,
                subtext=locali.get_string("ENABLE_TRANSLATOR_SUB"),
                icon="ai_chat_solar"
            ),
            Selector(
                key="provider",
                text="Translation Provider",
                default=0,
                items=["Google", "Yandex"],
                icon="msg_translate_solar"
            ),
            # --- OpenRouter polishing controls (always visible) ---
            Switch(
                key="or_enable",
                text="Improve with Artificial Intelligence",
                default=self.get_setting("or_enable", False),
                subtext="Use AI models to polish translated text.",
                icon="ai_chat_solar"
            ),
            Input(
                key="or_model_name",
                text="Model",
                default=self.get_setting("or_model_name", "mistralai/mistral-7b-instruct:free"),
                icon="input_suggest_s",
                subtext="e.g. mistralai/mistral-7b-instruct:free"
            ) if self.get_setting("or_enable", False) else None,
            Input(
                key="or_api_key",
                text="API Key",
                default=self.get_setting("or_api_key", ""),
                icon="msg_pin_code",
                subtext="Enter your API key (sk-...)"
            ) if self.get_setting("or_enable", False) else None,
            Input(
                key="or_fix_prompt",
                text="Grammar Prompt",
                default=self.get_setting("or_fix_prompt", ""),
                icon="ai_chat_solar",
                subtext="Custom instruction for grammar correction ({text} placeholder)."
            ) if self.get_setting("or_enable", False) else None,
            Input(
                key="or_ai_prompt",
                text="AI Chat System Prompt",
                default=self.get_setting("or_ai_prompt", ""),
                icon="ai_chat_solar",
                subtext="System prompt prepended to !ai chats."
            ) if self.get_setting("dev_mode_enabled", False) else None,
            Selector(
                key="target_language",
                text=locali.get_string("TARGET_LANGUAGE"),
                default=0,
                items=LANG_NAMES,
                icon="menu_premium_chatbot"
            ),
            Text(
                text="Excluded Languages",
                icon="msg_viewreplies_solar",
                create_sub_fragment=self._create_excluded_languages_settings
            ) if self.get_setting("advanced_settings", False) else None,
            Switch(
                key="bypass_commands",
                text=locali.get_string("BYPASS_COMMANDS"),
                default=True,
                subtext=locali.get_string("BYPASS_COMMANDS_SUB"),
                icon="msg_photo_curve_solar"
            ),
            Switch(
                key="advanced_settings",
                text="Advanced Settings",
                default=False,
                subtext="Enable extra controls like excluded words list.",
                icon="msg_settings_solar"
            ),
            # Settings button always enabled; no user toggle
            Text(
                text="Help / Usage",
                icon="msg_psa",
                accent=True,
                on_click=lambda view: self._show_help_dialog()
            ),
        ]

    def _mask_excluded_words(self, text):
        """Replace protected tokens with placeholders before translation."""
        placeholders = []
        masked_text = text

        # Always preserve hashtags and @mentions.
        def tag_replacer(match):
            placeholders.append(match.group(0))
            return f"[[{len(placeholders)-1}]]"
        masked_text = re.sub(r"(#[\w\d_]+|@[\w\d_]+)", tag_replacer, masked_text)

        # 2) Preserve custom excluded words only when advanced settings are enabled (so the user can edit the list).
        if self.get_setting("advanced_settings", False):
            raw = self.get_setting("excluded_words", "")
            words = [w.strip() for w in raw.split(",") if w.strip()]
            for word in words:
                pattern = re.compile(re.escape(word), flags=re.I)
                def repl(m):
                    placeholders.append(m.group(0))
                    return f"[[{len(placeholders)-1}]]"
                masked_text = pattern.sub(repl, masked_text)

        return masked_text, placeholders

    def _restore_excluded_words(self, text, placeholders):
        restored = text
        for idx, original in enumerate(placeholders):
            pattern = re.compile(rf"\[\[{idx}\]\]", flags=re.I)
            restored = pattern.sub(original, restored)
        return restored

    def _create_excluded_words_settings(self, view=None):
        """Return a settings sub-fragment containing the Input field for excluded words."""
        return [
            Input(
                key="excluded_words",
                text="Excluded words",
                default=self.get_setting("excluded_words", ""),
                subtext="Words that will not be translated.",
                icon="msg_translate"
            )
        ]

    def _create_excluded_languages_settings(self, view=None):
        """Return settings rows for multi-select excluded languages."""
        items = [Header("Select languages to bypass translation")]

        # Current selections
        current_raw = self.get_setting("exclude_languages", "")
        current_set = set(code for code in current_raw.split(',') if code)

        def _make_on_change(lang_code: str):
            def _handler(enabled: bool):
                raw = self.get_setting("exclude_languages", "")
                s = set(c for c in raw.split(',') if c)
                if enabled:
                    s.add(lang_code)
                else:
                    s.discard(lang_code)
                self.set_setting("exclude_languages", ",".join(sorted(s)))
            return _handler

        for code, name in zip(LANG_CODES, LANG_NAMES):
            items.append(
                Switch(
                    key=f"ex_lang_{code}",
                    text=f"{name} ({code})",
                    default=(code in current_set),
                    on_change=_make_on_change(code)
                )
            )

        return items

    def _set_composer_text(self, new_text: str):
        pass

    def _translate_and_keep(self, original_text: str):
        pass

    # ------- Helper to open this plugin's settings fragment -------
    def _open_plugin_settings(self, java_plugin):
        try:
            get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
        except Exception as e:
            log(f"[{__id__}] open settings error: {e}")

    # ------- UI menu helpers -------
    def _add_settings_menu_items(self):
        try:
            self._drawer_settings_item = self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.DRAWER_MENU,
                text="Auto Translate Settings",
                icon="msg_settings_14",
                priority=5,
                on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
            ))
            self._chat_settings_item = self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text="Auto Translate Settings",
                icon="msg_settings_14",
                priority=5,
                on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
            ))
        except Exception as e:
            log(f"[{__id__}] add menu items error: {e}")

    def _toggle_settings_buttons(self, enabled: bool):
        def _apply():
            try:
                if enabled:
                    if not self._drawer_settings_item or not self._chat_settings_item:
                        self._add_settings_menu_items()
                else:
                    if self._drawer_settings_item:
                        self.remove_menu_item(self._drawer_settings_item)
                        self._drawer_settings_item = None
                    if self._chat_settings_item:
                        self.remove_menu_item(self._chat_settings_item)
                        self._chat_settings_item = None
            except Exception as e:
                log(f"[{__id__}] toggle button error: {e}")
        run_on_ui_thread(_apply)

    # -------- Premium emoji tag preservation --------
    def _encode_premium_tags(self, text: str):
        """HTML-escape premium emoji tags so translation engines leave them intact."""
        return self.PREMIUM_EMO_TAG_RE.sub(lambda m: html.escape(m.group(0)), text)

    def _decode_premium_tags(self, text: str):
        return text.replace('&lt;', '<').replace('&gt;', '>')

    # ---- OpenRouter post-processing ----
    def _openrouter_improve(self, text: str, target_lang: str) -> str:
        if not self.get_setting("or_enable", False):
            return text

        api_key = self.get_setting("or_api_key", "").strip()
        if not api_key:
            self._show_copyable_bulletin("OpenRouter API key not set in plugin settings")
            return text

        model = self.get_setting("or_model_name", "mistralai/mistral-7b-instruct:free").strip() or "mistralai/mistral-7b-instruct:free"

        # Detect if source contains any emoji (Unicode or premium tag)
        emoji_present = bool(re.search(r"[\u2190-\U0001f9ff]|<emoji", text))
        emoji_rule = "Keep emojis intact, do NOT add new ones." if emoji_present else "Do NOT add any emojis."

        custom_fix_prompt = self.get_setting("or_fix_prompt", "").strip()
        if custom_fix_prompt:
            # Allow simple placeholders replacement
            prompt = custom_fix_prompt.replace("{target_lang}", target_lang).replace("{emoji_rule}", emoji_rule)
            if "{text}" in prompt:
                prompt = prompt.replace("{text}", text)
            else:
                prompt = f"{prompt}\n\n{text}"
        else:
            prompt = (
                f"Improve the grammar and naturalness of the following text in {target_lang}. {emoji_rule} "
                f"Do NOT translate to another language. Respond with ONLY the corrected text, no explanations or bullet points. "
                f"If there are no mistakes, return the text unchanged.\n\n{text}"
            )

        system_prompt = self.get_setting("or_ai_prompt", "").strip()

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": model,
            "messages": [
                *([{"role": "system", "content": system_prompt}] if system_prompt else []),
                {"role": "user", "content": prompt}
            ]
        }
        try:
            resp = _http.post(OPENROUTER_URL, json=payload, headers=headers, timeout=15)
            if resp.status_code == 429:
                retry_after = int(resp.headers.get("Retry-After", "0"))
                msg = "OpenRouter rate-limit hit. Please wait a moment and try again."
                if retry_after > 0:
                    msg += f" (retry after {retry_after}s)"
                self._show_copyable_bulletin(msg)
                # Reset lock/spinner
                self.is_sending_translated = False
                if self.progress_dialog:
                    run_on_ui_thread(lambda: self.progress_dialog.dismiss())
                    self.progress_dialog = None
                return

            resp.raise_for_status()
            data = resp.json()
            choice = (data.get("choices") or [{}])[0]
            improved = choice.get("message", {}).get("content")
            if improved:
                return improved
            self._show_copyable_bulletin("OpenRouter returned empty response")
        except Exception as e:
            log(f"OpenRouter improve error: {e}")
            self._show_copyable_bulletin(f"OpenRouter request failed: {e}")
        return text

    def _copy_to_clipboard(self, label, text):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        clipboard = ctx.getSystemService(Context.CLIPBOARD_SERVICE)
        clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
        BulletinHelper.show_info(f"Copied {label} to clipboard")

    def _show_copyable_bulletin(self, message: str):
        """Show bulletin with a Copy button to copy message to clipboard."""
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext

        def _copy():
            self._copy_to_clipboard("Message", message)

        BulletinHelper.show_with_button(
            message,
            R_tg.raw.info,
            "Copy",
            _copy,
            fragment,
            duration=BulletinHelper.DURATION_LONG
        )

    # ----- AI Chat command -----
    def _perform_ai_chat_and_send(self, peer, question, reply_obj, reply_top_obj):
        # Show spinner while waiting for AI response
        try:
            current_fragment = get_last_fragment()
            if current_fragment and current_fragment.getParentActivity():
                self.progress_dialog = AlertDialog(current_fragment.getParentActivity(), 3)
                self.progress_dialog.show()
        except Exception as e:
            log(f"AI chat spinner error: {e}")

        # Prevent recursive hook triggering for the forthcoming AI answer
        self.is_sending_translated = True

        run_on_queue(lambda: self._perform_ai_chat_and_send_async(peer, question, reply_obj, reply_top_obj))
        return HookResult(strategy=HookStrategy.CANCEL)

    def _perform_ai_chat_and_send_async(self, peer, question, reply_obj, reply_top_obj):
        try:
            api_key = self.get_setting("or_api_key", "")
            if not api_key:
                self._show_copyable_bulletin("OpenRouter API key not set in plugin settings")
                return

            model = self.get_setting("or_model_name", "mistralai/mistral-7b-instruct:free").strip() or "mistralai/mistral-7b-instruct:free"

            system_prompt = self.get_setting("or_ai_prompt", "").strip()

            prompt = question

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": model,
                "messages": [
                    *([{"role": "system", "content": system_prompt}] if system_prompt else []),
                    {"role": "user", "content": prompt}
                ]
            }
            resp = _http.post(OPENROUTER_URL, json=payload, headers=headers, timeout=15)
            if resp.status_code == 429:
                retry_after = int(resp.headers.get("Retry-After", "0"))
                msg = "OpenRouter rate-limit hit. Please wait a moment and try again."
                if retry_after > 0:
                    msg += f" (retry after {retry_after}s)"
                self._show_copyable_bulletin(msg)
                # Reset lock/spinner
                self.is_sending_translated = False
                if self.progress_dialog:
                    run_on_ui_thread(lambda: self.progress_dialog.dismiss())
                    self.progress_dialog = None
                return

            resp.raise_for_status()
            data = resp.json()
            choice = (data.get("choices") or [{}])[0]
            improved = choice.get("message", {}).get("content")
            if improved:
                self._send_final_message(peer, improved, reply_obj, reply_top_obj, False)
            else:
                self._show_copyable_bulletin("OpenRouter returned empty response")
                # Reset flag since no message was sent
                self.is_sending_translated = False
                if self.progress_dialog:
                    run_on_ui_thread(lambda: self.progress_dialog.dismiss())
                    self.progress_dialog = None
        except Exception as e:
            log(f"OpenRouter AI chat error: {e}")
            self._show_copyable_bulletin(f"OpenRouter request failed: {e}")
            self.is_sending_translated = False
            if self.progress_dialog:
                run_on_ui_thread(lambda: self.progress_dialog.dismiss())
                self.progress_dialog = None
