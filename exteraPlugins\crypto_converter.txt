__id__ = "crypto_converter"
__name__ = "Crypto Converter"
__version__ = "1.0"
__author__ = "@q00q0q0"
__description__ = "Plugin for tracking cryptocurrency rates and conversion"
__min_version__ = "11.9.0"
__icon__ = "cryptovalue/6"

import requests
import json
import os.path
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, <PERSON><PERSON>
from base_plugin import BasePlugin, HookResult, HookStrategy
from android_utils import log
from java.util import ArrayList
from org.telegram.tgnet import TLRPC
from ui.bulletin import BulletinHelper
from ui.settings import Divider, Header, Switch, Input
from android.content import ClipboardManager
from android.content import Context

class CryptoConverter(BasePlugin):
    def __init__(self):
        super().__init__()
        self.supported_currencies = [
            "USD", "RUB", "EUR", "UAH", "GBP", "JPY", "CNY",
            "AUD", "CAD", "CHF", "INR", "BRL", "MXN", "SGD",
            "HKD", "SEK", "NOK", "KRW", "TRY", "ZAR", "PLN",
            "THB", "IDR", "MYR", "PHP", "ILS", "AED", "SAR",
            "CLP", "COP", "PEN", "ARS", "VND", "NGN", "EGP"
        ]
        self.crypto_api_url = "https://api.coingecko.com/api/v3"
        self.cache_dir = os.path.join(os.path.dirname(os.path.realpath(__file__)), "cache")
        self.config_file = os.path.join(os.path.dirname(os.path.realpath(__file__)), "cache/config_crypto_converter.json")
        os.makedirs(self.cache_dir, exist_ok=True)
        self.lang = self.get_language()
        self.load_settings()
        
    def get_language(self) -> str:
        try:
            from org.telegram.messenger import LocaleController
            lang = LocaleController.getInstance().getCurrentLocale().getLanguage()
            if lang in ["ru", "uk", "be"]:
                return "ru"
            return "en"
        except:
            return "en"
            
    def get_text(self, key: str) -> str:
        texts = {
            "help": {
                "ru": (
                    "📊 Crypto Converter\n\n"
                    "Команды:\n"
                    ".ccr {криптовалюта} - показать статистику криптовалюты\n"
                    ".ccl [сумма]{криптовалюта/валюта} {валюта/криптовалюта} - конвертация\n"
                    ".cch - показать это сообщение\n\n"
                    "Примеры:\n"
                    ".ccr btc - показать статистику Bitcoin\n"
                    ".ccl 500 rub ton - конвертировать 500 рублей в TON\n"
                    ".ccl 1 btc usd - конвертировать 1 Bitcoin в доллары"
                ),
                "en": (
                    "📊 Crypto Converter\n\n"
                    "Commands:\n"
                    ".ccr {crypto} - show cryptocurrency statistics\n"
                    ".ccl [amount]{crypto/currency} {currency/crypto} - conversion\n"
                    ".cch - show this help message\n\n"
                    "Examples:\n"
                    ".ccr btc - show Bitcoin statistics\n"
                    ".ccl 500 rub ton - convert 500 rubles to TON\n"
                    ".ccl 1 btc usd - convert 1 Bitcoin to dollars"
                )
            },
            "usage_ccr": {
                "ru": "Использование: .ccr {криптовалюта}",
                "en": "Usage: .ccr {crypto}"
            },
            "usage_ccl": {
                "ru": "Использование: .ccl [сумма]{криптовалюта/валюта} {валюта/криптовалюта}",
                "en": "Usage: .ccl [amount]{crypto/currency} {currency/crypto}"
            },
            "crypto_not_found": {
                "ru": "Криптовалюта {} не найдена",
                "en": "Cryptocurrency {} not found"
            },
            "currency_not_supported": {
                "ru": "Валюта {} не поддерживается",
                "en": "Currency {} not supported"
            },
            "currency_changed": {
                "ru": "Валюта по умолчанию изменена на {}",
                "en": "Default currency changed to {}"
            },
            "error": {
                "ru": "Ошибка: {}",
                "en": "Error: {}"
            },
            "conversion_error": {
                "ru": "Ошибка конвертации: {}",
                "en": "Conversion error: {}"
            },
            "search_error": {
                "ru": "Ошибка поиска криптовалюты: {}",
                "en": "Cryptocurrency search error: {}"
            }
        }
        return texts.get(key, {}).get(self.lang, texts.get(key, {}).get("en", key))
        
    def on_plugin_load(self):
        self.log(f"Plugin '{self.name}' loaded successfully.")
        self.add_on_send_message_hook()
        self.load_settings()
        
    def on_plugin_unload(self):
        self.log(f"Plugin '{self.name}' unloaded successfully.")
        
    def create_settings(self):
        return [
            Header("Настройки валюты"),
            Input(
                "default_currency",
                "Валюта по умолчанию",
                self.default_currency,
                "Введите код валюты (например, USD, RUB, EUR)",
                on_change=lambda value: self.on_currency_change(value.upper())
            )
        ]
        
    def on_currency_change(self, value):
        if value.upper() in self.supported_currencies:
            self.default_currency = value.upper()
            self.save_settings()
            BulletinHelper.show(self.get_text("currency_changed").format(value.upper()))
        else:
            BulletinHelper.show(self.get_text("currency_not_supported").format(value))
        
    def copy_to_clipboard(self, text):
        try:
            context = self.get_context()
            clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE)
            clipboard.setText(text)
            BulletinHelper.show("Адрес скопирован в буфер обмена")
        except Exception as e:
            self.log(f"Error copying to clipboard: {str(e)}")
        
    def load_settings(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    settings = json.load(f)
                if "default_currency" in settings:
                    currency = settings["default_currency"]
                    if currency in self.supported_currencies:
                        self.default_currency = currency
                    else:
                        self.default_currency = "USD"
                        self.save_settings()
                else:
                    self.default_currency = "USD"
                    self.save_settings()
            else:
                self.default_currency = "USD"
                self.save_settings()
        except Exception as e:
            self.log(f"Error loading settings: {str(e)}")
            self.default_currency = "USD"
            self.save_settings()
            
    def save_settings(self):
        try:
            settings = {"default_currency": self.default_currency}
            with open(self.config_file, 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            self.log(f"Error saving settings: {str(e)}")
        
    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()
            
        message = params.message.strip()
        
        if message.startswith(".ccr"):
            try:
                args = message.split()
                if len(args) != 2:
                    params.message = self.get_text("usage_ccr")
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                    
                crypto = args[1].lower()
                
                search_response = requests.get(f"{self.crypto_api_url}/search", 
                                            params={"query": crypto}).json()
                
                if "coins" not in search_response or not search_response["coins"]:
                    params.message = self.get_text("crypto_not_found").format(crypto)
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
                coin_id = search_response["coins"][0]["id"]
                
                data = requests.get(f"{self.crypto_api_url}/coins/{coin_id}/market_chart", 
                                  params={"vs_currency": self.default_currency, "days": 1}).json()
                
                if "error" in data:
                    params.message = self.get_text("error").format(data["error"])
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
                if "prices" not in data or not data["prices"]:
                    params.message = self.get_text("crypto_not_found").format(crypto)
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
                prices = data["prices"]
                current_price = prices[-1][1]
                price_24h_ago = prices[0][1]
                price_change = ((current_price - price_24h_ago) / price_24h_ago) * 100
                
                high_24h = max(price[1] for price in prices)
                low_24h = min(price[1] for price in prices)
                
                if self.lang == "ru":
                    stats = (
                        f"📊 {crypto.upper()} Статистика цен\n\n"
                        f"Текущая цена: {current_price:.2f} {self.default_currency}\n"
                        f"Изменение за 24ч: {price_change:+.2f}%\n"
                        f"Максимум за 24ч: {high_24h:.2f} {self.default_currency}\n"
                        f"Минимум за 24ч: {low_24h:.2f} {self.default_currency}\n"
                    )
                else:
                    stats = (
                        f"📊 {crypto.upper()} Price Statistics\n\n"
                        f"Current Price: {current_price:.2f} {self.default_currency}\n"
                        f"24h Change: {price_change:+.2f}%\n"
                        f"24h High: {high_24h:.2f} {self.default_currency}\n"
                        f"24h Low: {low_24h:.2f} {self.default_currency}\n"
                    )
                
                params.message = stats
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
            except Exception as e:
                params.message = self.get_text("error").format(str(e))
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
        elif message.startswith(".ccl"):
            try:
                args = message.split()
                if len(args) < 2:
                    params.message = self.get_text("usage_ccl")
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                    
                amount = 1
                if args[1][0].isdigit():
                    amount = float(args[1])
                    from_currency = args[2].lower()
                    to_currency = args[3].lower()
                else:
                    from_currency = args[1].lower()
                    to_currency = args[2].lower()
                    
                if from_currency.upper() in self.supported_currencies:
                    result = self.convert_fiat_to_crypto(amount, from_currency, to_currency)
                else:
                    result = self.convert_crypto_to_fiat(amount, from_currency, to_currency)
                    
                params.message = result
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
            except Exception as e:
                params.message = self.get_text("error").format(str(e))
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
        elif message.startswith(".cch"):
            params.message = self.get_text("help")
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
                
        return HookResult()
        
    def get_crypto_id(self, crypto: str) -> str:
        try:
            search_response = requests.get(f"{self.crypto_api_url}/search", 
                                        params={"query": crypto}).json()
            
            if "coins" not in search_response or not search_response["coins"]:
                raise Exception(self.get_text("crypto_not_found").format(crypto))
                
            return search_response["coins"][0]["id"]
            
        except Exception as e:
            raise Exception(self.get_text("search_error").format(str(e)))
        
    def convert_fiat_to_crypto(self, amount: float, fiat: str, crypto: str) -> str:
        try:
            if fiat.upper() not in self.supported_currencies:
                raise Exception(self.get_text("currency_not_supported").format(fiat))
                
            crypto_id = self.get_crypto_id(crypto)
                
            response = requests.get(f"{self.crypto_api_url}/simple/price",
                                  params={"ids": crypto_id, "vs_currencies": fiat.lower()}).json()
            
            if crypto_id not in response:
                raise Exception(self.get_text("crypto_not_found").format(crypto))
                
            if fiat.lower() not in response[crypto_id]:
                raise Exception(self.get_text("currency_not_supported").format(fiat))
                
            rate = response[crypto_id][fiat.lower()]
            result = amount / rate
            return f"{amount} {fiat.upper()} = {result:.8f} {crypto.upper()}"
            
        except Exception as e:
            raise Exception(self.get_text("conversion_error").format(str(e)))
        
    def convert_crypto_to_fiat(self, amount: float, crypto: str, fiat: str) -> str:
        try:
            if fiat.upper() not in self.supported_currencies:
                raise Exception(self.get_text("currency_not_supported").format(fiat))
                
            crypto_id = self.get_crypto_id(crypto)
                
            response = requests.get(f"{self.crypto_api_url}/simple/price",
                                  params={"ids": crypto_id, "vs_currencies": fiat.lower()}).json()
            
            if crypto_id not in response:
                raise Exception(self.get_text("crypto_not_found").format(crypto))
                
            if fiat.lower() not in response[crypto_id]:
                raise Exception(self.get_text("currency_not_supported").format(fiat))
                
            rate = response[crypto_id][fiat.lower()]
            result = amount * rate
            return f"{amount} {crypto.upper()} = {result:.2f} {fiat.upper()}"
            
        except Exception as e:
            raise Exception(self.get_text("conversion_error").format(str(e))) 