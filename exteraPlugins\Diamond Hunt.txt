# diamond_hunt.py
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.alert import <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>
from ui.settings import Header, Input
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
from hook_utils import find_class
from java import dynamic_proxy
import random

TextView = find_class("android.widget.TextView")
GridLayout = find_class("android.widget.GridLayout")
LinearLayout = find_class("android.widget.LinearLayout")
FrameLayout = find_class("android.widget.FrameLayout")
AndroidUtilities = find_class("org.telegram.messenger.AndroidUtilities")
Theme = find_class("org.telegram.ui.ActionBar.Theme")
Gravity = find_class("android.view.Gravity")
OnClickListenerInterface = find_class("android.view.View$OnClickListener")

__id__ = "diamond_hunt"
__name__ = "Diamond Hunt"
__description__ = "Игра: найдите все спрятанные алмазы."
__author__ = "@Ameba1i"
__version__ = "1.5.1"
__min_version__ = "11.12.0"
__icon__ = "AnimatedEmojies/15"

KEY_FIELD_SIZE = "diamond_hunt_field_size"
KEY_DIAMONDS = "diamond_hunt_diamond_count"
KEY_WINS = "diamond_hunt_wins"
DEFAULT_FIELD = "7"
DEFAULT_DIAMONDS = "5"

class Cell:
    def __init__(self):
        self.has_diamond = False
        self.revealed = False

class CellClickListener(dynamic_proxy(OnClickListenerInterface)):
    def __init__(self, plugin, chat_id, r, c):
        super().__init__()
        self.plugin = plugin
        self.chat_id = chat_id
        self.r = r
        self.c = c

    def onClick(self, view):
        self.plugin.on_cell_click(self.chat_id, self.r, self.c)

class DiamondPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.games = {}
        self.views = {}

    def on_plugin_load(self):
        # Создаем ключ статистики, если не существует
        if self.get_setting(KEY_WINS, None) is None:
            self.set_setting(KEY_WINS, "0")

        self.add_menu_item(MenuItemData(
            menu_type=MenuItemType.CHAT_ACTION_MENU,
            text="Diamond Hunt",
            icon="msg_mini_bomb",
            on_click=self.open_game_dialog
        ))

    def create_settings(self):
        wins = int(self.get_setting(KEY_WINS, "0"))
        return [
            Header(text="💎 Настройки Diamond Hunt"),
            Input(key=KEY_FIELD_SIZE, text="Размер поля", default=DEFAULT_FIELD, subtext="От 3 до 15 (Рекомендуется 7)"),
            Input(key=KEY_DIAMONDS, text="Кол‑во алмазов", default=DEFAULT_DIAMONDS, subtext="Столько сколько надо"),
            Header(text=f"🏆 Победы: {wins}")
        ]

    def open_game_dialog(self, context):
        chat_id = context.get("dialog_id") or context.get("chatId")
        if chat_id is not None:
            run_on_ui_thread(lambda: self.start_game(chat_id), 10)

    def start_game(self, chat_id: int):
        prev = self.games.get(chat_id)
        if prev and prev.get("dialog"):
            try: prev["dialog"].dismiss()
            except: pass

        try:
            size = max(3, min(15, int(self.get_setting(KEY_FIELD_SIZE, DEFAULT_FIELD) or DEFAULT_FIELD)))
        except:
            size = int(DEFAULT_FIELD)

        try:
            diamonds = max(1, min(size * size, int(self.get_setting(KEY_DIAMONDS, DEFAULT_DIAMONDS) or DEFAULT_DIAMONDS)))
        except:
            diamonds = int(DEFAULT_DIAMONDS)

        board = [[Cell() for _ in range(size)] for _ in range(size)]
        for r, c in random.sample([(r, c) for r in range(size) for c in range(size)], diamonds):
            board[r][c].has_diamond = True

        self.games[chat_id] = {"board": board, "remaining": diamonds, "game_over": False, "dialog": None}
        self.views[chat_id] = {}

        fragment = get_last_fragment(); activity = fragment and fragment.getParentActivity()
        if not activity: return

        builder = AlertDialogBuilder(activity)
        builder.set_title("Diamond Hunt")
        main = LinearLayout(activity); main.setOrientation(LinearLayout.VERTICAL)
        grid = GridLayout(activity); grid.setColumnCount(size); grid.setRowCount(size)
        container = FrameLayout(activity); container.addView(grid); main.addView(container)

        cell_size = AndroidUtilities.dp(40); pad = AndroidUtilities.dp(2)
        for rr in range(size):
            for cc in range(size):
                tv = TextView(activity)
                params = GridLayout.LayoutParams(GridLayout.spec(rr), GridLayout.spec(cc))
                params.width = params.height = cell_size
                params.setMargins(pad, pad, pad, pad)
                tv.setLayoutParams(params); tv.setGravity(Gravity.CENTER); tv.setTextSize(18)
                tv.setBackgroundColor(Theme.getColor(Theme.key_chat_attachPhotoBackground))
                tv.setOnClickListener(CellClickListener(self, chat_id, rr, cc))
                grid.addView(tv)
                self.views[chat_id][(rr, cc)] = tv

        builder.set_view(main)
        builder.set_negative_button("Закрыть", lambda b,w: b.dismiss())
        self.games[chat_id]["dialog"] = builder.show()

    def on_cell_click(self, chat_id:int, r:int, c:int):
        game = self.games.get(chat_id)
        if not game or game["game_over"]: return
        cell = game["board"][r][c]
        if cell.revealed: return
        cell.revealed = True
        tv = self.views[chat_id][(r, c)]

        if cell.has_diamond:
            tv.setText("💎"); game["remaining"] -= 1
            if game["remaining"] == 0:
                game["game_over"] = True
                # Увеличиваем счетчик
                wins = int(self.get_setting(KEY_WINS, "0")) + 1
                self.set_setting(KEY_WINS, str(wins))
                self.show_result(chat_id, win=True)
        else:
            tv.setText("🕳️"); game["game_over"] = True
            self.show_result(chat_id, win=False)

    def show_result(self, chat_id:int, win:bool):
        fragment = get_last_fragment(); activity = fragment and fragment.getParentActivity()
        if not activity: return
        builder = AlertDialogBuilder(activity)
        builder.set_title("Вы выиграли!" if win else "Вы проиграли!")
        builder.set_message("🎉 Вы собрали все алмазы!" if win else "💥 Вы попали в яму — игра окончена.")
        def restart(b,w):
            b.dismiss()
            run_on_ui_thread(lambda: self.start_game(chat_id), 10)
        builder.set_positive_button("Играть снова", restart)
        builder.set_negative_button("Закрыть", lambda b,w: b.dismiss())
        builder.show()