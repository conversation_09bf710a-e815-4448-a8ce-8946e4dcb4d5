import threading
import time

from org.telegram.messenger import UserConfig
from com.exteragram.messenger.utils import ChatUtils

from base_plugin import BasePlugin
from ui.settings import Text

from android_utils import log
from client_utils import send_message, run_on_queue, GLOBAL_QUEUE
from java import dynamic_proxy
from android.os import Bundle
from org.telegram.ui import DialogsActivity, LaunchActivity


__name__ = "Iris Autofarm"
__description__ = "Авто-фарминг для ириса по выбранному диалогу."
__icon__ = "LINE_HATSUNE_MIKU_Pom_Ver/38"
__version__ = "1.0"
__id__ = "irisautofarm"
__author__ = "@bleizix, @AyugramTop"
__min_version__ = "11.12.0"




class DialogsDelegate(dynamic_proxy(DialogsActivity.DialogsActivityDelegate)):
    def __init__(self, fn: callable):
        super().__init__()
        self._fn = fn

    def didSelectDialogs(self, fragment, dids, message, param, notify: bool, scheduleDate: int, topicsFragment):
        try:
            self._fn(fragment, dids, message, param, notify, scheduleDate, topicsFragment)
        except Exception as e:
            log(f"[AutoFarm] Error in delegate: {e}")


class TimedSenderPlugin(BasePlugin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs) # idk
        self._stop_event = None
        self._thread = None
        self._last_sent_time = 0

    def on_plugin_load(self):
        if self._thread and self._thread.is_alive():
            return

        self._stop_event = threading.Event()
        self._last_sent_time = float(self.get_setting("last_sent_timestamp", "0"))
        self._thread = threading.Thread(target=self._sending_loop, daemon=True)
        self._thread.start()

    def processDialogs(self, activity, fragment, dids, message, param, notify, scheduleDate, topicsFragment):
        activity.finishFragment()

        if dids.isEmpty():
            log("no dialogs, wtf!")
            return

        selected_id = dids.get(0)
        peer_id = str(selected_id.dialogId)
        self.set_setting("target_peer_id", peer_id)


        if peer_id != "0":
            peer_id_int = int(peer_id)

            chatUtils = ChatUtils.getInstance(UserConfig.selectedAccount)
            current_target_name = chatUtils.getName(peer_id_int)

            self.set_setting("target_name", current_target_name)

    def showDialogsActivity(self):

            args = Bundle()
            args.putBoolean("onlySelect", True)
            args.putBoolean("checkCanWrite", True)
            args.putInt("dialogsType", 0)  # 0 (all chats)
            args.putBoolean("allowGlobalSearch", True)

            activity = DialogsActivity(args)
            delegate = DialogsDelegate(
                lambda fragment, dids, message, param, notify, scheduleDate, topicsFragment:
                self.processDialogs(activity, fragment, dids, message, param, notify, scheduleDate, topicsFragment)
            )
            activity.setDelegate(delegate)

            if LaunchActivity.getLastFragment():
                LaunchActivity.getLastFragment().presentFragment(activity)



    def _sending_loop(self):
        time.sleep(5)  # initial delay
        while not self._stop_event.is_set():
            try:
                interval_seconds = 4 * 60 * 60 + 5 * 60  # 4 hours + 5 minutes

                if time.time() - self._last_sent_time >= interval_seconds:
                    self._send_message_to_configured_chat("фармить")
                    self._last_sent_time = time.time()
                    self.set_setting("last_sent_timestamp", str(self._last_sent_time))



            except Exception as e:
                log(f"fucked up: " + str(e))

            # 5 minutes
            time.sleep(60 * 5)

    def on_unload(self):
        self._stop_event.set()
        self._thread.join()

    def _send_message_to_configured_chat(self, message_text: str):
        chat_id_str = self.get_setting("target_peer_id", "0")

        if chat_id_str == "0":
            return

        run_on_queue(
            lambda: send_message({"peer": int(chat_id_str), "message": message_text}),
            GLOBAL_QUEUE
        )

    def create_settings(self):
        # похуй
        target_name = self.get_setting("target_name", "Не выбран")
        return [
            Text(
                text="Выбрать диалог",
                icon="msg_select",
                on_click=lambda view: self.showDialogsActivity()
            ),
            Text(
                text=f"Текущий: {target_name}",
                icon="msg_info"
            )
        ]