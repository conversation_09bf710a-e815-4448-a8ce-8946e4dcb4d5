import requests
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Selector
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>gy
from android_utils import log, run_on_ui_thread
from org.telegram.tgnet import TLRPC
from java.util import ArrayList
import threading

__id__ = "wiki_search"
__name__ = "Wikipedia Search"
__description__ = "Search Wikipedia with .wiki [term]"
__author__ = "@exteraDev"
__min_version__ = "11.9.0"
__icon__ = "exteraDevPlugins/1"

API_BASE_URL = "https://{lang}.wikipedia.org/api/rest_v1"

class WikiSearch(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_plugin_unload(self):
        self.remove_on_send_message_hook()

    def create_settings(self):
        return [
            Header(text="Wikipedia Search Settings"),
            Selector(
                key="language",
                text="Wikipedia Language",
                default=0,
                items=[
                    "English", "Portuguese", "Spanish", "French", 
                    "German", "Italian", "Russian", "Japanese", 
                    "Chinese", "Arabic", "Korean", "Hindi", "Turkish"
                ]
            ),
            <PERSON><PERSON><PERSON>(),
            <PERSON><PERSON>(text="Display Settings"),
            Switch(
                key="show_url",
                text="Include Article URL",
                default=True,
                subtext="Add a link to the full Wikipedia article"
            ),
            Switch(
                key="show_last_modified",
                text="Show last modified date",
                default=False,
                subtext="Displays the last modification date of the article"
            ),
            Divider(text="Usage: .wiki [term]"),
        ]

    def _get_language_code(self):
        languages = [
            "en", "pt", "es", "fr", "de", "it", "ru", "ja", 
            "zh", "ar", "ko", "hi", "tr"
        ]
        index = self.get_setting("language", 0)
        return languages[index] if 0 <= index < len(languages) else "en"

    def _search_wikipedia(self, query, language=None):
        try:
            language = language or self._get_language_code()
            url = f"https://{language}.wikipedia.org/w/api.php"
            params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srsearch": query,
                "srlimit": 5,
                "srprop": "snippet"
            }
            headers = {"User-Agent": "Wikipedia Plugin/1.0"}
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            data = response.json()
            results = data.get("query", {}).get("search", [])
            return results, None
        except Exception as e:
            log(f"Error searching Wikipedia: {e}")
            return None, f"Error searching Wikipedia: {str(e)}"

    def _get_article_summary(self, title, language=None):
        try:
            language = language or self._get_language_code()
            url = f"https://{language}.wikipedia.org/api/rest_v1/page/summary/{title.replace(' ', '_')}"
            headers = {"User-Agent": "Wikipedia Plugin/1.0"}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            return response.json(), None
        except Exception as e:
            log(f"Error fetching article summary: {e}")
            return None, f"Error fetching article summary: {str(e)}"

    def _format_summary(self, summary_data):
        try:
            title = summary_data.get("title", "No Title")
            extract = summary_data.get("extract", "No summary available")
            max_length = 1024
            if len(extract) > max_length:
                extract = extract[:max_length] + "..."
            url = summary_data.get("content_urls", {}).get("desktop", {}).get("page", "")
            result = f"{extract}"
            return title, result, url
        except Exception as e:
            log(f"Error formatting summary: {e}")
            return "Error formatting summary.", "", ""

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()

        if not params.message.startswith(".wiki "):
            return HookResult()

        query = params.message[6:].strip()
        if not query:
            params.message = "Please provide a search term after .wiki"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        def do_search():
            try:
                search_results, error = self._search_wikipedia(query)

                if not search_results and self._get_language_code() != "en":
                    search_results, error = self._search_wikipedia(query, language="en")
                if error:
                    params.message = error
                    run_on_ui_thread(lambda: self._send_result(params))
                    return

                if not search_results:
                    params.message = f"No results found for '{query}'"
                    run_on_ui_thread(lambda: self._send_result(params))
                    return

                first_result = search_results[0]
                title = first_result.get("title", "")
                summary_data, error = self._get_article_summary(title)

                if (not summary_data or "type" in summary_data and summary_data["type"] == "https://mediawiki.org/wiki/HyperSwitch/errors/not_found") and self._get_language_code() != "en":
                    summary_data, error = self._get_article_summary(title, language="en")

                if error:
                    params.message = error
                    run_on_ui_thread(lambda: self._send_result(params))
                    return

                title, citation, url = self._format_summary(summary_data)

                if self.get_setting("show_last_modified", False):
                    lastmod = summary_data.get("timestamp") or summary_data.get("lastmodified") or summary_data.get("last_modified")
                    if lastmod:
                        citation += f"\n\nLast updated: {lastmod[:10]}"

                show_url = self.get_setting("show_url", True)
                readmore_text = ""
                if url and show_url:
                    readmore_text = f"\n\n[Read more]"

                params.message = f"Wikipedia: {title}\n{citation}{readmore_text}"
                
                if not hasattr(params, "entities") or params.entities is None:
                    params.entities = ArrayList()
                elif not isinstance(params.entities, ArrayList):
                    params.entities = ArrayList(params.entities)

                offset = len(f"Wikipedia: {title}\n")
                entity = TLRPC.TL_messageEntityBlockquote()
                entity.collapsed = True
                entity.offset = offset
                entity.length = len(citation)
                params.entities.add(entity)

                if url and show_url:
                    link_text = "Read more"
                    link_offset = params.message.find("Read more")
                    if link_offset != -1:
                        entity_url = TLRPC.TL_messageEntityTextUrl()
                        entity_url.offset = link_offset
                        entity_url.length = len(link_text)
                        entity_url.url = url
                        params.entities.add(entity_url)

                run_on_ui_thread(lambda: self._send_result(params))
            except Exception as e:
                log(f"Error in Wikipedia plugin: {e}")
                params.message = f"Error: {str(e)}"
                run_on_ui_thread(lambda: self._send_result(params))

        threading.Thread(target=do_search, daemon=True).start()
        return HookResult(strategy=HookStrategy.CANCEL)

    def _send_result(self, params):
        from client_utils import get_send_messages_helper
        get_send_messages_helper().sendMessage(params)