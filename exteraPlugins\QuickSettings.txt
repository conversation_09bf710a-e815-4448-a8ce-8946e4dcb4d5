import traceback
import threading
import plugins_manager
import os
import re
import ast
import shutil
from typing import Optional, List, Dict, Any

from base_plugin import BasePlugin, MenuItemData, MenuItemType, XposedHook
from android_utils import log, run_on_ui_thread
from client_utils import get_last_fragment, get_account_instance
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity, PluginsActivity
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from ui.settings import Header, Selector, Switch, Text, Divider

from java.util import Locale
from java.lang import String, Boolean
from java.io import File
from org.telegram.messenger import Utilities, AndroidUtilities, SendMessagesHelper, ApplicationLoader, R
from android.content import Intent
from androidx.core.content import FileProvider

__id__ = "quick_settings_menu"
__name__ = "QuickSettings"
__description__ = "Adds a menu for quick access to other plugins' settings."
__author__ = "@bokudzhava"
__version__ = "2.0.1"
__min_version__ = "11.12.0"
__icon__ = "AnimatedEmojies/409"

QUICKSETTINGS_TEMP_DIR = "QuickSettingsTemp"
QUICKSETTINGS_CACHE_DIR = "QuickSettingsCache"
CACHE_FILENAME = "plugin_info_cache.py"

class Locales:
    default = {
        "HEADER_MENU_LOCATION": "Menu Location",
        "SELECTOR_SHOW_MENU_IN": "Show menu in:",
        "SELECTOR_ITEMS_MENU_LOCATION": ["Chat menu", "Drawer menu", "Everywhere"],
        "TITLE_PLUGIN_SETTINGS": "Plugin Settings",
        "DIALOG_CHOOSE_PLUGIN": "Choose a plugin",
        "INFO_NO_PLUGINS_WITH_SETTINGS": "No active plugins with settings found",
        "PLUGIN_WILL_BE_DELETED": "Plugin '{0}' will be deleted.",
        "PLUGIN_ENABLED": "Plugin '{0}' enabled.",
        "PLUGIN_DISABLED": "Plugin '{0}' disabled.",
        "PLUGIN_DELETED_SUCCESS": "Plugin '{0}' deleted.",
        "PLUGIN_ENABLE_ERROR": "Failed to enable plugin '{0}'.",
        "PLUGIN_DISABLE_ERROR": "Failed to disable plugin '{0}'.",
        "PLUGIN_DELETE_ERROR": "Failed to delete plugin '{0}'.",
        "PLUGIN_GENERIC_ERROR": "An error has occurred.",
        "DIALOG_ACTION_SETTINGS": "Open settings",
        "DIALOG_ACTION_SHOW_COMMANDS": "Show commands",
        "DIALOG_COMMANDS_TITLE": "Commands for '{0}'",
        "INFO_NO_COMMANDS_FOUND": "No commands found for this plugin.",
        "DIALOG_ACTION_ENABLE": "Enable",
        "DIALOG_ACTION_DISABLE": "Disable",
        "DIALOG_ACTION_DELETE": "Delete",
        "DIALOG_ACTION_SHARE": "Share...",
        "DIALOG_ACTION_OPEN_WITH": "Open with...",
        "SHARE_ERROR_NOT_FOUND": "Plugin file not found.",
        "SHARE_ERROR_GENERAL": "Error while sharing plugin.",
        "OPEN_WITH_TITLE": "Open plugin with...",
        "HEADER_CHAT_MENU_ACTION": "Action on tap in chat",
        "SELECTOR_CHAT_MENU_ACTION": "Action on tap:",
        "SELECTOR_ITEMS_CHAT_MENU_ACTION": ["Open settings", "Toggle state", "Open menu"],
        "PLUGIN_HAS_NO_SETTINGS": "This plugin has no settings.",
        "HEADER_PLUGINS_SHORTCUT": "'Plugins' Shortcut",
        "SWITCH_SHOW_CHAT_PLUGINS_SHORTCUT": "Show shortcut in chat menu",
        "UNDO": "Undo",
        "ACTION_UNDONE": "Action undone.",
        "HEADER_PLUGIN_VISIBILITY": "Plugin Filtering",
        "SWITCH_SHOW_PLUGINS_WITHOUT_SETTINGS": "Show plugins without settings",
        "SWITCH_SHOW_DISABLED_PLUGINS": "Show disabled plugins",
        "HEADER_CACHE_MANAGEMENT": "Cache Management",
        "SETTINGS_REBUILD_CACHE_BUTTON": "Rebuild Cache",
        "SETTINGS_REBUILD_CACHE_SUBTEXT": "Forces a full rescan of all plugins for settings and commands.",
        "INFO_CACHE_REBUILD_STARTED": "Cache rebuild started..."
    }
    ru = {
        "HEADER_MENU_LOCATION": "Расположение меню",
        "SELECTOR_SHOW_MENU_IN": "Показывать меню в:",
        "SELECTOR_ITEMS_MENU_LOCATION": ["Меню чата", "Боковое меню", "Везде"],
        "TITLE_PLUGIN_SETTINGS": "Настройки плагинов",
        "DIALOG_CHOOSE_PLUGIN": "Выберите плагин",
        "INFO_NO_PLUGINS_WITH_SETTINGS": "Нет активных плагинов с настройками",
        "PLUGIN_WILL_BE_DELETED": "Плагин '{0}' будет удален.",
        "PLUGIN_ENABLED": "Плагин '{0}' включен.",
        "PLUGIN_DISABLED": "Плагин '{0}' отключен.",
        "PLUGIN_DELETED_SUCCESS": "Плагин '{0}' удален.",
        "PLUGIN_ENABLE_ERROR": "Не удалось включить плагин '{0}'.",
        "PLUGIN_DISABLE_ERROR": "Не удалось отключить плагин '{0}'.",
        "PLUGIN_DELETE_ERROR": "Не удалось удалить плагин '{0}'.",
        "PLUGIN_GENERIC_ERROR": "Произошла ошибка.",
        "DIALOG_ACTION_SETTINGS": "Открыть настройки",
        "DIALOG_ACTION_SHOW_COMMANDS": "Показать команды",
        "DIALOG_COMMANDS_TITLE": "Команды для '{0}'",
        "INFO_NO_COMMANDS_FOUND": "Для этого плагина не найдено команд.",
        "DIALOG_ACTION_ENABLE": "Включить",
        "DIALOG_ACTION_DISABLE": "Отключить",
        "DIALOG_ACTION_DELETE": "Удалить",
        "DIALOG_ACTION_SHARE": "Поделиться...",
        "DIALOG_ACTION_OPEN_WITH": "Открыть с помощью...",
        "SHARE_ERROR_NOT_FOUND": "Не удалось найти файл плагина.",
        "SHARE_ERROR_GENERAL": "Ошибка при отправке плагина.",
        "OPEN_WITH_TITLE": "Открыть плагин с помощью...",
        "HEADER_CHAT_MENU_ACTION": "Действие при нажатии в чате",
        "SELECTOR_CHAT_MENU_ACTION": "Действие при нажатии:",
        "SELECTOR_ITEMS_CHAT_MENU_ACTION": ["Открыть настройки", "Переключить", "Открыть меню"],
        "PLUGIN_HAS_NO_SETTINGS": "У этого плагина нет настроек.",
        "HEADER_PLUGINS_SHORTCUT": "Шорткат 'Plugins'",
        "SWITCH_SHOW_CHAT_PLUGINS_SHORTCUT": "Показывать шорткат в меню чата",
        "UNDO": "Отмена",
        "ACTION_UNDONE": "Действие отменено.",
        "HEADER_PLUGIN_VISIBILITY": "Фильтрация плагинов",
        "SWITCH_SHOW_PLUGINS_WITHOUT_SETTINGS": "Показывать плагины без настроек",
        "SWITCH_SHOW_DISABLED_PLUGINS": "Показывать выключенные плагины",
        "HEADER_CACHE_MANAGEMENT": "Управление кэшем",
        "SETTINGS_REBUILD_CACHE_BUTTON": "Пересоздать кэш",
        "SETTINGS_REBUILD_CACHE_SUBTEXT": "Принудительно сканирует все плагины на наличие настроек и команд.",
        "INFO_CACHE_REBUILD_STARTED": "Пересоздание кэша началось..."
    }
    uk = {
        "HEADER_MENU_LOCATION": "Розташування меню",
        "SELECTOR_SHOW_MENU_IN": "Показувати меню в:",
        "SELECTOR_ITEMS_MENU_LOCATION": ["Меню чату", "Бічне меню", "Скрізь"],
        "TITLE_PLUGIN_SETTINGS": "Налаштування плагінів",
        "DIALOG_CHOOSE_PLUGIN": "Виберіть плагін",
        "INFO_NO_PLUGINS_WITH_SETTINGS": "Немає активних плагінів з налаштуваннями",
        "PLUGIN_WILL_BE_DELETED": "Плагін '{0}' буде видалено.",
        "PLUGIN_ENABLED": "Плагін '{0}' увімкнено.",
        "PLUGIN_DISABLED": "Плагін '{0}' вимкнено.",
        "PLUGIN_DELETED_SUCCESS": "Плагін '{0}' видалено.",
        "PLUGIN_ENABLE_ERROR": "Не вдалося увімкнути плагін '{0}'.",
        "PLUGIN_DISABLE_ERROR": "Не вдалося вимкнути плагін '{0}'.",
        "PLUGIN_DELETE_ERROR": "Не вдалося видалити плагін '{0}'.",
        "PLUGIN_GENERIC_ERROR": "Сталася помилка.",
        "DIALOG_ACTION_SETTINGS": "Відкрити налаштування",
        "DIALOG_ACTION_SHOW_COMMANDS": "Показати команди",
        "DIALOG_COMMANDS_TITLE": "Команди для '{0}'",
        "INFO_NO_COMMANDS_FOUND": "Для цього плагіна не знайдено команд.",
        "DIALOG_ACTION_ENABLE": "Увімкнути",
        "DIALOG_ACTION_DISABLE": "Вимкнути",
        "DIALOG_ACTION_DELETE": "Видалити",
        "DIALOG_ACTION_SHARE": "Поділитися...",
        "DIALOG_ACTION_OPEN_WITH": "Відкрити за допомогою...",
        "SHARE_ERROR_NOT_FOUND": "Не вдалося знайти файл плагіна.",
        "SHARE_ERROR_GENERAL": "Помилка під час надсилання плагіна.",
        "OPEN_WITH_TITLE": "Відкрити плагін за допомогою...",
        "HEADER_CHAT_MENU_ACTION": "Дія при натисканні в чаті",
        "SELECTOR_CHAT_MENU_ACTION": "Дія при натисканні:",
        "SELECTOR_ITEMS_CHAT_MENU_ACTION": ["Відкрити налаштування", "Перемкнути", "Відкрити меню"],
        "PLUGIN_HAS_NO_SETTINGS": "У цього плагіна немає налаштувань.",
        "HEADER_PLUGINS_SHORTCUT": "Шорткат 'Plugins'",
        "SWITCH_SHOW_CHAT_PLUGINS_SHORTCUT": "Показувати шорткат у меню чату",
        "UNDO": "Скасувати",
        "ACTION_UNDONE": "Дію скасовано.",
        "HEADER_PLUGIN_VISIBILITY": "Фільтрація плагінів",
        "SWITCH_SHOW_PLUGINS_WITHOUT_SETTINGS": "Показувати плагіни без налаштувань",
        "SWITCH_SHOW_DISABLED_PLUGINS": "Показувати вимкнені плагіни",
        "HEADER_CACHE_MANAGEMENT": "Керування кешем",
        "SETTINGS_REBUILD_CACHE_BUTTON": "Перестворити кеш",
        "SETTINGS_REBUILD_CACHE_SUBTEXT": "Примусово сканує всі плагіни на наявність налаштувань та команд.",
        "INFO_CACHE_REBUILD_STARTED": "Перестворення кешу почалося..."
    }
    en = default

def localise(key: str, *args) -> str:
    lang = Locale.getDefault().getLanguage()
    locale_dict = getattr(Locales, lang, Locales.default)
    text = locale_dict.get(key, Locales.default.get(key, key))
    return text.format(*args) if args else text

MENU_LOCATION_CHAT = 0
MENU_LOCATION_DRAWER = 1
MENU_LOCATION_BOTH = 2

ACTION_OPEN_SETTINGS = 0
ACTION_TOGGLE_STATE = 1
ACTION_OPEN_MENU = 2

class QuickCommandExtractor:
    class _CommandVisitor(ast.NodeVisitor):
        VALID_COMMAND_RE = re.compile(r"^\.[\w-]+$")

        def __init__(self):
            self.commands = set()
            self.variables = {}

        def _resolve_node_value(self, node: ast.AST) -> Optional[str]:
            if isinstance(node, ast.Constant):
                return str(node.value)
            if isinstance(node, ast.Name) and node.id in self.variables:
                return str(self.variables[node.id])
            if isinstance(node, ast.BinOp) and isinstance(node.op, ast.Add):
                left = self._resolve_node_value(node.left)
                right = self._resolve_node_value(node.right)
                if left is not None and right is not None:
                    return left + right
            return None

        def visit_Assign(self, node: ast.Assign):
            if len(node.targets) == 1 and isinstance(node.targets[0], ast.Name):
                try:
                    self.variables[node.targets[0].id] = ast.literal_eval(node.value)
                except (ValueError, TypeError, AttributeError, SyntaxError):
                    pass
            self.generic_visit(node)

        def _add_command(self, value: Any):
            if isinstance(value, str):
                command = value.strip().split(" ")[0]
                if self.VALID_COMMAND_RE.match(command):
                    self.commands.add(command)

        def visit_Compare(self, node: ast.Compare):
            if isinstance(node.ops[0], (ast.Eq, ast.NotEq, ast.In)):
                for item in [node.left] + node.comparators:
                    value = self._resolve_node_value(item)
                    if value:
                        self._add_command(value)
                    elif isinstance(item, (ast.List, ast.Tuple)):
                        for element in item.elts:
                            val = self._resolve_node_value(element)
                            if val:
                                self._add_command(val)
            self.generic_visit(node)

        def visit_Call(self, node: ast.Call):
            if isinstance(node.func, ast.Attribute) and node.func.attr == 'startswith':
                if node.args:
                    value = self._resolve_node_value(node.args[0])
                    if value:
                        self._add_command(value)
            self.generic_visit(node)

class PluginStateChangeHook(XposedHook):
    def __init__(self, plugin_instance):
        super().__init__()
        self.plugin = plugin_instance

    def after_hooked_method(self, param):
        self.plugin._schedule_cache_rebuild()

class QuickSettingsPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.chat_menu_items: List[MenuItemData] = []
        self.drawer_menu_item: Optional[MenuItemData] = None
        self.hooks: List[object] = []
        self.update_timer: Optional[threading.Timer] = None
        self.chat_plugins_activity_item: Optional[MenuItemData] = None
        self.plugin_info_cache: Dict[str, Dict] = {}
        self.cache_path: Optional[str] = None
        self.rebuild_timer: Optional[threading.Timer] = None

    def create_settings(self):
        return [
            Header(text=localise("HEADER_MENU_LOCATION")),
            Selector(
                key="menu_location",
                text=localise("SELECTOR_SHOW_MENU_IN"),
                items=localise("SELECTOR_ITEMS_MENU_LOCATION"),
                default=MENU_LOCATION_BOTH,
                on_change=lambda _: self._schedule_menu_update(delay=0)
            ),
            Header(text=localise("HEADER_PLUGINS_SHORTCUT")),
            Switch(
                key="show_chat_plugins_shortcut",
                text=localise("SWITCH_SHOW_CHAT_PLUGINS_SHORTCUT"),
                default=True,
                on_change=lambda _: self._schedule_menu_update(delay=0)
            ),
            Header(text=localise("HEADER_CHAT_MENU_ACTION")),
            Selector(
                key="chat_menu_action",
                text=localise("SELECTOR_CHAT_MENU_ACTION"),
                items=localise("SELECTOR_ITEMS_CHAT_MENU_ACTION"),
                default=ACTION_OPEN_MENU,
                on_change=lambda _: self._schedule_menu_update(delay=0)
            ),
            Header(text=localise("HEADER_PLUGIN_VISIBILITY")),
            Switch(
                key="show_plugins_without_settings",
                text=localise("SWITCH_SHOW_PLUGINS_WITHOUT_SETTINGS"),
                default=False,
                on_change=lambda _: self._schedule_menu_update(delay=0)
            ),
            Switch(
                key="show_disabled_plugins",
                text=localise("SWITCH_SHOW_DISABLED_PLUGINS"),
                default=False,
                on_change=lambda _: self._schedule_menu_update(delay=0)
            ),
            Header(text=localise("HEADER_CACHE_MANAGEMENT")),
            Text(
                text=localise("SETTINGS_REBUILD_CACHE_BUTTON"),
                icon="msg_update",
                on_click=self._handle_rebuild_cache_click
            ),
            Divider(text=localise("SETTINGS_REBUILD_CACHE_SUBTEXT"))
        ]

    def _handle_rebuild_cache_click(self, view):
        BulletinHelper.show_info(localise("INFO_CACHE_REBUILD_STARTED"))
        self._schedule_cache_rebuild(delay=0)

    def on_plugin_load(self):
        self._init_cache_system()
        self._load_cache()
        self._schedule_cache_rebuild(delay=2.0)
        self._schedule_menu_update(delay=0)
        self._apply_hooks()

    def on_plugin_unload(self):
        self._remove_menu_items()
        self._remove_hooks()
        if self.update_timer: self.update_timer.cancel()
        if self.rebuild_timer: self.rebuild_timer.cancel()

    def _init_cache_system(self):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                log(f"[{__id__}] ERROR: Could not get files directory.")
                return
            cache_dir = File(base_dir, QUICKSETTINGS_CACHE_DIR)
            if not cache_dir.exists():
                cache_dir.mkdirs()
            self.cache_path = File(cache_dir, CACHE_FILENAME).getAbsolutePath()
        except Exception as e:
            log(f"[{__id__}] Failed to initialize cache system: {e}")

    def _load_cache(self) -> bool:
        if not self.cache_path or not os.path.exists(self.cache_path):
            return False
        try:
            with open(self.cache_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            scope = {}
            exec(content, scope)
            self.plugin_info_cache = scope.get('PLUGIN_INFO_CACHE', {})
            log(f"[{__id__}] Cache loaded successfully with {len(self.plugin_info_cache)} entries.")
            return True
        except Exception as e:
            log(f"[{__id__}] Failed to load cache: {e}. Rebuilding.")
            return False

    def _rebuild_cache_and_update_menu(self):
        log(f"[{__id__}] Starting cache rebuild.")
        if not self.cache_path:
            log(f"[{__id__}] Cache path not initialized. Aborting rebuild.")
            return

        cache_data = {}
        try:
            plugins_dir = plugins_manager.PluginsManager._plugins_dir
            if not plugins_dir:
                log(f"[{__id__}] Plugins directory not found for cache rebuild.")
                return

            extractor = QuickCommandExtractor()
            for filename in os.listdir(plugins_dir):
                if not filename.endswith(('.py', '.plugin')):
                    continue
                file_path = os.path.join(plugins_dir, filename)
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    
                    plugin_id = self._get_id_from_file_content(content)
                    if not plugin_id:
                        continue

                    has_settings = "def create_settings(self)" in content
                    
                    visitor = extractor._CommandVisitor()
                    tree = ast.parse(content, filename=filename)
                    visitor.visit(tree)
                    commands = sorted(list(visitor.commands))

                    cache_data[plugin_id] = {
                        "has_settings": has_settings,
                        "commands": commands
                    }
                except Exception:
                    continue
            
            file_content = f"# Auto-generated by QuickSettings. Do not edit.\nPLUGIN_INFO_CACHE = {cache_data}"
            with open(self.cache_path, "w", encoding="utf-8") as f:
                f.write(file_content)
            
            log(f"[{__id__}] Cache rebuilt and saved with {len(cache_data)} entries.")
            
            if self._load_cache():
                self._schedule_menu_update(delay=0)

        except Exception as e:
            log(f"[{__id__}] Error during cache rebuild: {e}")

    def _schedule_cache_rebuild(self, delay: float = 1.0):
        if self.rebuild_timer:
            self.rebuild_timer.cancel()
        self.rebuild_timer = threading.Timer(delay, self._rebuild_cache_and_update_menu)
        self.rebuild_timer.start()
        log(f"[{__id__}] Scheduled cache rebuild in {delay}s.")

    def _apply_hooks(self):
        if self.hooks: return
        try:
            controller_class = PluginsController.getClass()
            hook_handler = PluginStateChangeHook(self)
            
            methods_to_hook = [
                ("loadPluginFromFile", String, Utilities.Callback),
                ("setPluginEnabled", String, Boolean.TYPE, Utilities.Callback),
                ("deletePlugin", String)
            ]

            for method_info in methods_to_hook:
                method_name, *arg_types = method_info
                target_method = controller_class.getDeclaredMethod(method_name, *arg_types)
                hook = self.hook_method(target_method, hook_handler)
                self.hooks.append(hook)

        except Exception:
            pass

    def _remove_hooks(self):
        for hook in self.hooks:
            try: self.unhook_method(hook)
            except Exception: pass
        self.hooks.clear()

    def _schedule_menu_update(self, delay: float = 1.0):
        if self.update_timer:
            self.update_timer.cancel()
        
        self.update_timer = threading.Timer(delay, self._update_menu_items)
        self.update_timer.start()

    def _get_id_from_file_content(self, content: str) -> Optional[str]:
        match = re.search(r"__id__\s*=\s*['\"]([^'\"]+)['\"]", content)
        return match.group(1) if match else None

    def _get_displayable_plugins(self) -> List[any]:
        plugins_to_show = []
        show_without_settings = self.get_setting("show_plugins_without_settings", False)
        show_disabled = self.get_setting("show_disabled_plugins", False)
        
        try:
            loaded_plugins_map = {p.id: p for p in plugins_manager.PluginsManager._plugins.values()}

            for plugin_id, plugin_object in loaded_plugins_map.items():
                if plugin_id == __id__:
                    continue

                if not (plugin_object.enabled or show_disabled):
                    continue

                if not show_without_settings:
                    if not self._plugin_has_settings(plugin_id) and not self._plugin_has_commands(plugin_id):
                        continue
                
                plugins_to_show.append(plugin_object)
        
        except Exception as e:
            log(f"[{__id__}] Error getting displayable plugins: {e}")

        return sorted(plugins_to_show, key=lambda p: p.name)

    def _update_menu_items(self):
        def action():
            self._remove_menu_items()
            
            location = self.get_setting("menu_location", MENU_LOCATION_BOTH)
            displayable_plugins = self._get_displayable_plugins()
            chat_action = self.get_setting("chat_menu_action", ACTION_OPEN_MENU)

            if location in [MENU_LOCATION_CHAT, MENU_LOCATION_BOTH]:
                if displayable_plugins:
                    for plugin in displayable_plugins:
                        if chat_action == ACTION_OPEN_MENU:
                            on_click_action = lambda ctx, p_id=plugin.id, p_name=plugin.name: self._show_plugin_action_dialog(p_id, p_name)
                        elif chat_action == ACTION_OPEN_SETTINGS:
                            on_click_action = lambda ctx, p_id=plugin.id: self._open_settings_or_show_bulletin(p_id)
                        elif chat_action == ACTION_TOGGLE_STATE:
                            on_click_action = lambda ctx, p_id=plugin.id, p_name=plugin.name: self._toggle_plugin_state(p_id, p_name)
                        else:
                            on_click_action = lambda ctx, p_id=plugin.id, p_name=plugin.name: self._show_plugin_action_dialog(p_id, p_name)
                        
                        icon = "msg_select_solar" if plugin.enabled else "msg_block_solar"
                        
                        item = self.add_menu_item(MenuItemData(
                            menu_type=MenuItemType.CHAT_ACTION_MENU,
                            text=plugin.name,
                            icon=icon,
                            priority=90,
                            on_click=on_click_action
                        ))
                        self.chat_menu_items.append(item)

            if location in [MENU_LOCATION_DRAWER, MENU_LOCATION_BOTH]:
                self.drawer_menu_item = self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.DRAWER_MENU,
                    text=localise("TITLE_PLUGIN_SETTINGS"),
                    icon="msg_reorder",
                    priority=90,
                    on_click=lambda ctx: self._show_plugin_list_dialog()
                ))

            if self.get_setting("show_chat_plugins_shortcut", True):
                self.chat_plugins_activity_item = self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.CHAT_ACTION_MENU,
                    text="exteraGram > Plugins",
                    icon="msg_plugins_solar",
                    priority=91,
                    on_click=lambda ctx: self._open_plugins_activity()
                ))

        run_on_ui_thread(action)

    def _remove_menu_items(self):
        for item in self.chat_menu_items:
            self.remove_menu_item(item)
        self.chat_menu_items.clear()

        if self.drawer_menu_item:
            self.remove_menu_item(self.drawer_menu_item)
            self.drawer_menu_item = None
        
        if self.chat_plugins_activity_item:
            self.remove_menu_item(self.chat_plugins_activity_item)
            self.chat_plugins_activity_item = None

    def _show_plugin_list_dialog(self):
        def action():
            try:
                displayable_plugins = self._get_displayable_plugins()
                
                if not displayable_plugins:
                    BulletinHelper.show_info(localise("INFO_NO_PLUGINS_WITH_SETTINGS"))
                    return

                plugin_names = [p.name for p in displayable_plugins]

                def on_item_click(dialog, which):
                    selected_plugin = displayable_plugins[which]
                    if self._plugin_has_settings(selected_plugin.id):
                        self._open_specific_settings(selected_plugin.id)
                    dialog.dismiss()

                builder = AlertDialogBuilder(get_last_fragment().getContext())
                builder.set_title(localise("DIALOG_CHOOSE_PLUGIN"))
                builder.set_items(plugin_names, on_item_click)
                builder.show()

            except Exception:
                BulletinHelper.show_error("QuickSettings: Error building list")

        run_on_ui_thread(action)

    def _plugin_has_settings(self, plugin_id: str) -> bool:
        return self.plugin_info_cache.get(plugin_id, {}).get('has_settings', False)

    def _plugin_has_commands(self, plugin_id: str) -> bool:
        return bool(self.plugin_info_cache.get(plugin_id, {}).get('commands', []))

    def _show_plugin_action_dialog(self, plugin_id: str, plugin_name: str):
        def action():
            try:
                builder = AlertDialogBuilder(get_last_fragment().getContext())
                builder.set_title(plugin_name)
                
                plugin_obj = plugins_manager.PluginsManager._plugins.get(plugin_id)
                if not plugin_obj: return

                actions = []
                if self._plugin_has_settings(plugin_id) and plugin_obj.enabled:
                    actions.append(("settings", localise("DIALOG_ACTION_SETTINGS")))
                
                if self._plugin_has_commands(plugin_id):
                    actions.append(("show_commands", localise("DIALOG_ACTION_SHOW_COMMANDS")))

                if plugin_obj.enabled:
                    actions.append(("disable", localise("DIALOG_ACTION_DISABLE")))
                else:
                    actions.append(("enable", localise("DIALOG_ACTION_ENABLE")))

                actions.extend([
                    ("delete", localise("DIALOG_ACTION_DELETE")),
                    ("share", localise("DIALOG_ACTION_SHARE")),
                    ("open_with", localise("DIALOG_ACTION_OPEN_WITH"))
                ])

                items = [text for _, text in actions]

                def on_item_click(dialog, which):
                    action_key = actions[which][0]
                    
                    if action_key == "settings":
                        self._open_specific_settings(plugin_id)
                    elif action_key == "show_commands":
                        self._show_plugin_commands_dialog(plugin_id, plugin_name)
                    elif action_key == "disable":
                        self._disable_plugin(plugin_id, plugin_name)
                    elif action_key == "enable":
                        self._enable_plugin(plugin_id, plugin_name)
                    elif action_key == "delete":
                        self._delete_plugin_with_undo(plugin_id, plugin_name)
                    elif action_key == "share":
                        self._share_plugin(plugin_id)
                    elif action_key == "open_with":
                        self._open_plugin_with(plugin_id)
                    dialog.dismiss()

                builder.set_items(items, on_item_click)
                builder.show()
            except Exception:
                BulletinHelper.show_error("QuickSettings: Error building action list")
        run_on_ui_thread(action)

    def _show_plugin_commands_dialog(self, plugin_id: str, plugin_name: str):
        def action():
            commands = self.plugin_info_cache.get(plugin_id, {}).get('commands', [])
            if not commands:
                BulletinHelper.show_info(localise("INFO_NO_COMMANDS_FOUND"))
                return
            
            try:
                context = get_last_fragment().getContext()
                builder = AlertDialogBuilder(context)
                builder.set_title(localise("DIALOG_COMMANDS_TITLE", plugin_name))
                
                commands_text = "\n".join(commands)
                builder.set_message(commands_text)
                
                builder.set_positive_button("OK", lambda d, w: d.dismiss())
                builder.show()
            except Exception as e:
                log(f"[{__id__}] Error showing commands dialog: {e}")

        run_on_ui_thread(action)

    def _open_settings_or_show_bulletin(self, plugin_id: str):
        plugin_obj = plugins_manager.PluginsManager._plugins.get(plugin_id)
        if not plugin_obj: return

        if not self._plugin_has_settings(plugin_id):
            BulletinHelper.show_info(localise("PLUGIN_HAS_NO_SETTINGS"))
            return
        
        if not plugin_obj.enabled:
            BulletinHelper.show_info(localise("PLUGIN_DISABLED", plugin_obj.name))
            return

        self._open_specific_settings(plugin_id)

    def _toggle_plugin_state(self, plugin_id: str, plugin_name: str):
        plugin_obj = plugins_manager.PluginsManager._plugins.get(plugin_id)
        if not plugin_obj: return

        if plugin_obj.enabled:
            self._disable_plugin(plugin_id, plugin_name)
        else:
            self._enable_plugin(plugin_id, plugin_name)

    def _open_specific_settings(self, plugin_id: str):
        def action():
            try:
                java_plugin = PluginsController.getInstance().plugins.get(plugin_id)
                if java_plugin:
                    get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
                else:
                    pass
            except Exception:
                pass
        
        run_on_ui_thread(action)

    def _open_plugins_activity(self):
        def action():
            try:
                get_last_fragment().presentFragment(PluginsActivity())
            except Exception:
                pass
        run_on_ui_thread(action)

    def _restart_plugin_engine(self):
        pm = plugins_manager.PluginsManager
        pc = PluginsController.getInstance()
        pm.shutdown()
        pc.shutdown()
        pm.init(pm._plugins_dir)
        pc.init()

    def _disable_plugin(self, plugin_id: str, plugin_name: str):
        try:
            pm = plugins_manager.PluginsManager
            if pm.set_plugin_enabled(plugin_id, False):
                BulletinHelper.show_success(localise("PLUGIN_DISABLED", plugin_name))
                self._restart_plugin_engine()
            else:
                BulletinHelper.show_error(localise("PLUGIN_DISABLE_ERROR", plugin_name))
        except Exception:
            BulletinHelper.show_error(localise("PLUGIN_GENERIC_ERROR"))

    def _enable_plugin(self, plugin_id: str, plugin_name: str):
        def action():
            try:
                pm = plugins_manager.PluginsManager
                if pm.set_plugin_enabled(plugin_id, True):
                    BulletinHelper.show_success(localise("PLUGIN_ENABLED", plugin_name))
                    self._restart_plugin_engine()
                else:
                    BulletinHelper.show_error(localise("PLUGIN_ENABLE_ERROR", plugin_name))
            except Exception:
                BulletinHelper.show_error(localise("PLUGIN_GENERIC_ERROR"))
        
        run_on_ui_thread(action)

    def _delete_plugin_with_undo(self, plugin_id: str, plugin_name: str):
        def perform_delete():
            run_on_ui_thread(lambda: self._delete_plugin(plugin_id, plugin_name))

        def undo_callback():
            BulletinHelper.show_info(localise("ACTION_UNDONE"))

        bulletin_text = localise("PLUGIN_WILL_BE_DELETED", plugin_name)
        BulletinHelper.show_undo(
            text=bulletin_text,
            on_action=perform_delete,
            on_undo=undo_callback
        )

    def _delete_plugin(self, plugin_id: str, plugin_name: str):
        try:
            pm = plugins_manager.PluginsManager
            if pm.delete_plugin(plugin_id):
                BulletinHelper.show_success(localise("PLUGIN_DELETED_SUCCESS", plugin_name))
                self._restart_plugin_engine()
            else:
                BulletinHelper.show_error(localise("PLUGIN_DELETE_ERROR", plugin_name))
        except Exception:
            BulletinHelper.show_error(localise("PLUGIN_GENERIC_ERROR"))

    def _find_plugin_path(self, plugin_id: str) -> Optional[str]:
        try:
            plugins_dir = plugins_manager.PluginsManager._plugins_dir
            if not plugins_dir: return None

            for filename in os.listdir(plugins_dir):
                if not filename.endswith(('.py', '.plugin')): continue
                
                file_path = os.path.join(plugins_dir, filename)
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                    id_from_file = self._get_id_from_file_content(content)
                    if id_from_file == plugin_id:
                        return file_path
                except Exception:
                    continue
        except Exception:
            pass
        return None

    def _share_plugin(self, plugin_id: str):
        def action():
            try:
                plugin_path = self._find_plugin_path(plugin_id)
                if not plugin_path:
                    BulletinHelper.show_error(localise("SHARE_ERROR_NOT_FOUND"))
                    return

                fragment = get_last_fragment()
                if not fragment: return
                
                dialog_id = fragment.getDialogId()
                account_instance = get_account_instance()
                
                ext_cache_root = ApplicationLoader.applicationContext.getExternalCacheDir()
                plugin_ext_dir = File(ext_cache_root, QUICKSETTINGS_TEMP_DIR)
                if not plugin_ext_dir.exists() and not plugin_ext_dir.mkdirs():
                    BulletinHelper.show_error(localise("SHARE_ERROR_GENERAL"))
                    return
                
                original_file = File(plugin_path)
                original_filename = original_file.getName()
                
                if original_filename.endswith(".py"):
                    new_filename = original_filename[:-3] + ".plugin"
                else:
                    new_filename = original_filename

                external_path = File(plugin_ext_dir, new_filename).getAbsolutePath()

                with open(plugin_path, 'rb') as f_in, open(external_path, 'wb') as f_out:
                    f_out.write(f_in.read())

                import mimetypes
                mime, _ = mimetypes.guess_type(external_path)
                if mime is None: mime = "application/octet-stream"

                SendMessagesHelper.prepareSendingDocument(
                    account_instance, external_path, external_path, None,
                    None, mime, dialog_id,
                    None, None, None, None, None,
                    True, 0, None, None, 0, False
                )
            except Exception:
                BulletinHelper.show_error(localise("SHARE_ERROR_GENERAL"))
        
        run_on_ui_thread(action)

    def _open_plugin_with(self, plugin_id: str):
        def action():
            try:
                plugin_path = self._find_plugin_path(plugin_id)
                if not plugin_path:
                    BulletinHelper.show_error(localise("SHARE_ERROR_NOT_FOUND"))
                    return
                
                context = get_last_fragment().getContext()
                
                ext_cache_root = ApplicationLoader.applicationContext.getExternalCacheDir()
                plugin_ext_dir = File(ext_cache_root, QUICKSETTINGS_TEMP_DIR)
                if not plugin_ext_dir.exists() and not plugin_ext_dir.mkdirs():
                    return

                original_file = File(plugin_path)
                cached_file = File(plugin_ext_dir, original_file.getName())
                with open(plugin_path, 'rb') as f_in, open(cached_file.getAbsolutePath(), 'wb') as f_out:
                    f_out.write(f_in.read())

                authority = context.getPackageName() + ".provider"
                uri = FileProvider.getUriForFile(context, authority, cached_file)

                intent = Intent(Intent.ACTION_VIEW)
                intent.setDataAndType(uri, "text/plain")
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)

                chooser_intent = Intent.createChooser(intent, localise("OPEN_WITH_TITLE"))
                context.startActivity(chooser_intent)

            except Exception:
                log(f"[{__id__}] Error in _open_plugin_with: {traceback.format_exc()}")
                BulletinHelper.show_error(localise("SHARE_ERROR_GENERAL"))
        
        run_on_ui_thread(action)