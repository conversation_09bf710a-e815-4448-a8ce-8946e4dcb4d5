import requests
import os
import uuid
import time
from java.io import File
from java.util import Locale
from org.telegram.messenger import ApplicationLoader
from org.telegram.ui.ActionBar import AlertDialog
from ui.settings import Head<PERSON>, Selector, Switch, Divider
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from android_utils import log, run_on_ui_thread
from client_utils import get_send_messages_helper, get_last_fragment, run_on_queue
from ui.bulletin import BulletinHelper

__id__ = "qrcode"
__name__ = "QR Generator"
__description__ = "Generate QR codes with .qr command"
__author__ = "@exteraDev"
__min_version__ = "11.9.0"
__icon__ = "exteraDevPlugins/5"

QR_API_URL = "https://api.qrserver.com/v1/create-qr-code/"
TEMP_IMAGES_DIR_NAME = "exteraGram Images"
QR_FORMATS = ["png", "jpeg", "jpg"]

progress_dialog = None

class QRGeneratorPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self._images_dir = None

    def on_plugin_load(self):
        self.add_on_send_message_hook()
        self._images_dir = self._get_images_dir()
        if self._images_dir:
            log("QR Generator plugin loaded successfully")
            self._cleanup_old_files()
        else:
            log("Failed to initialize images directory for QR Generator plugin")

    def on_plugin_unload(self):
        self.remove_on_send_message_hook()
        log("QR Generator plugin unloaded")

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("pt"):
            header = "Configurações do QR Code"
            size_label = "Tamanho do QR Code"
            size_items = ["Pequeno (256x256)", "Médio (384x384)", "Grande (512x512)", "Extra (768x768)"]
            ecc_label = "Nível de Correção de Erro"
            ecc_items = ["Baixo (L)", "Médio (M)", "Quartil (Q)", "Alto (H)"]
            format_label = "Formato"
            format_items = ["PNG", "GIF", "JPEG", "JPG"]
            margin_label = "Adicionar Margem"
            margin_sub = "Adiciona uma margem branca ao redor do QR"
            msg_header = "Opções de Mensagem"
            caption_label = "Adicionar Legenda"
            caption_sub = "Inclui o texto codificado como legenda"
            usage = "Uso: .qr [texto ou URL]"
        elif lang.startswith("ru"):
            header = "Настройки QR-кода"
            size_label = "Размер QR-кода"
            size_items = ["Маленький (256x256)", "Средний (384x384)", "Большой (512x512)", "Очень большой (768x768)"]
            ecc_label = "Уровень коррекции ошибок"
            ecc_items = ["Низкий (L)", "Средний (M)", "Квартиль (Q)", "Высокий (H)"]
            format_label = "Формат"
            format_items = ["PNG", "GIF", "JPEG", "JPG"]
            margin_label = "Добавить отступ"
            margin_sub = "Добавить белую рамку вокруг QR-кода"
            msg_header = "Параметры сообщения"
            caption_label = "Добавить подпись"
            caption_sub = "Включить закодированный текст как подпись"
            usage = "Использование: .qr [текст или URL]"
        else:
            header = "QR Code Settings"
            size_label = "QR Code Size"
            size_items = ["Small (256x256)", "Medium (384x384)", "Large (512x512)", "Extra Large (768x768)"]
            ecc_label = "Error Correction Level"
            ecc_items = ["Low (L)", "Medium (M)", "Quartile (Q)", "High (H)"]
            format_label = "QR Format"
            format_items = ["PNG", "GIF", "JPEG", "JPG"]
            margin_label = "Add Margin"
            margin_sub = "Add a white margin around the QR code"
            msg_header = "Message Options"
            caption_label = "Add Caption"
            caption_sub = "Include the encoded text as a caption"
            usage = "Usage: .qr [text or URL to encode]"
        return [
            Header(text=header),
            Selector(key="qr_size", text=size_label, default=1, items=size_items),
            Selector(key="error_correction", text=ecc_label, default=1, items=ecc_items),
            Selector(key="format", text=format_label, default=0, items=format_items),
            Switch(key="add_margin", text=margin_label, default=True, subtext=margin_sub),
            Divider(),
            Header(text=msg_header),
            Switch(key="show_caption", text=caption_label, default=False, subtext=caption_sub),
            Divider(text=usage),
        ]

    def _get_images_dir(self):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                log("Failed to get base directory via ApplicationLoader")
                return None
            images_dir = File(base_dir, TEMP_IMAGES_DIR_NAME)
            if not images_dir.exists():
                if not images_dir.mkdirs():
                    log(f"Failed to create images directory: {images_dir.getAbsolutePath()}")
                    return None
            if not images_dir.isDirectory():
                log(f"Images path is not a directory: {images_dir.getAbsolutePath()}")
                return None
            return images_dir
        except Exception as e:
            log(f"Error getting/creating images directory: {e}")
            return None

    def _cleanup_old_files(self, max_age_hours=24):
        try:
            images_dir = self._get_images_dir()
            if not images_dir or not images_dir.isDirectory():
                return
            now = time.time()
            max_age_seconds = max_age_hours * 3600
            for file in images_dir.listFiles():
                if file.getName().startswith("qr_") and now - file.lastModified() / 1000 > max_age_seconds:
                    file.delete()
        except Exception as e:
            log(f"Error cleaning up old QR files: {e}")

    def _get_qr_size(self):
        return ["256x256", "384x384", "512x512", "768x768"][self.get_setting("qr_size", 1)]

    def _get_format_extension(self):
        return QR_FORMATS[self.get_setting("format", 0)]

    def _get_error_correction(self):
        return ["L", "M", "Q", "H"][self.get_setting("error_correction", 1)]

    def _generate_qr_code(self, text):
        temp_photo_path = None
        try:
            images_dir = self._get_images_dir()
            if not images_dir or not images_dir.isDirectory():
                BulletinHelper.show_error("Images dir is invalid")
                return None
            format_ext = self._get_format_extension()
            filename = f"qr_{uuid.uuid4()}.{format_ext}"
            temp_photo_path = File(images_dir, filename).getAbsolutePath()
            params = {
                'data': text,
                'size': self._get_qr_size(),
                'ecc': self._get_error_correction(),
                'margin': 10 if self.get_setting("add_margin", True) else 0,
                'format': format_ext
            }
            response = requests.get(QR_API_URL, params=params, stream=True, timeout=15)
            if response.status_code != 200:
                BulletinHelper.show_error(f"QR API error: {response.status_code}")
                return None
            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                BulletinHelper.show_error("API did not return an image")
                return None
            with open(temp_photo_path, 'wb') as f:
                wrote = False
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        wrote = True
                if not wrote:
                    BulletinHelper.show_error("Failed to save QR file")
                    return None
            if not os.path.exists(temp_photo_path) or os.path.getsize(temp_photo_path) == 0:
                BulletinHelper.show_error("QR file not created or is empty")
                return None
            return temp_photo_path
        except Exception as e:
            BulletinHelper.show_error(f"Error generating QR: {e}")
            if temp_photo_path and os.path.exists(temp_photo_path):
                try:
                    os.remove(temp_photo_path)
                except Exception:
                    pass
            return None

    def _delete_temp_file_async(self, file_path, delay_seconds=5):
        def delete_action():
            try:
                time.sleep(delay_seconds)
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                log(f"Error deleting temp file: {e}")
        run_on_queue(delete_action)

    def _dismiss_dialog(self):
        global progress_dialog
        try:
            if progress_dialog is not None and progress_dialog.isShowing():
                progress_dialog.dismiss()
        except Exception:
            pass
        finally:
            progress_dialog = None

    def _process_qr(self, account, params, text_to_encode):
        global progress_dialog
        try:
            temp_file_path = self._generate_qr_code(text_to_encode)
            if not temp_file_path:
                self._dismiss_dialog()
                return
            send_helper = get_send_messages_helper()
            generated_photo = send_helper.generatePhotoSizes(temp_file_path, None)
            if not generated_photo:
                BulletinHelper.show_error("Failed to process QR image")
                self._delete_temp_file_async(temp_file_path)
                self._dismiss_dialog()
                return
            params.photo = generated_photo
            params.path = temp_file_path
            if self.get_setting("show_caption", False):
                params.caption = text_to_encode
            params.message = None
            if not hasattr(params, "entities") or params.entities is None:
                params.entities = []
            BulletinHelper.show_success("QR code generated successfully")
            self._delete_temp_file_async(temp_file_path)
            self._dismiss_dialog()
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
        except Exception as e:
            BulletinHelper.show_error(f"Unexpected error: {e}")
            if 'temp_file_path' in locals() and temp_file_path:
                self._delete_temp_file_async(temp_file_path)
            self._dismiss_dialog()

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()
        msg = params.message.strip()
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("pt"):
            no_text_msg = "Nenhum texto informado após .qr"
        else:
            no_text_msg = "No text provided after .qr"
        if not msg.startswith(".qr"):
            return HookResult()
        text_to_encode = msg[3:].strip()
        if not text_to_encode:
            show_error_bulletin(no_text_msg)
            return HookResult(strategy=HookStrategy.CANCEL)
        global progress_dialog
        try:
            progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
            progress_dialog.show()
        except Exception as e:
            log(f"Failed to show progress dialog: {e}")
        run_on_queue(lambda: self._process_qr(account, params, text_to_encode))
        return HookResult(strategy=HookStrategy.CANCEL)