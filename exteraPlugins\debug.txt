import os
import traceback
import plugins_manager
from android.os import Build, SystemClock
from android.app import ActivityManager
from android.net import NetworkCapabilities
from android.content import Context, Intent, IntentFilter
from java.lang import System as JavaSystem
from java.util import Locale
from java.util import ArrayList
from org.telegram.messenger import Shared<PERSON>onfig, ApplicationLoader, UserConfig
from base_plugin import BasePlugin, HookResult, HookStrategy
from ui.settings import Header, Switch, Divider
from android_utils import log, run_on_ui_thread
from client_utils import get_user_config, get_send_messages_helper
from org.telegram.tgnet import TLRPC
import threading

__id__ = "debug"
__name__ = "Debug"
__description__ = "Device & Plugin info [.dg]"
__author__ = "@exteraDev"
__min_version__ = "11.9.0"
__icon__ = "exteraDevPlugins/3"

class DebugPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_plugin_unload(self):
        self.remove_on_send_message_hook()

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            settings_options = [
                ("show_device_info", "Показать информацию об устройстве", "Включить детали устройства в вывод отладки"),
                ("show_app_info", "Показать информацию о приложении", "Включить версию приложения в вывод"),
                ("show_plugins", "Показать активные плагины", "Показать все включённые плагины в выводе"),
                ("show_connection", "Показать информацию о соединении", "Включить детали сети"),
                ("show_storage", "Показать информацию о хранилище", "Включить детали хранилища и кэша"),
                ("show_memory", "Показать информацию о памяти", "Включить использование ОЗУ"),
                ("show_account", "Показать информацию об аккаунте", "Включить основную информацию об аккаунте"),
                ("show_battery", "Показать информацию о батарее", "Включить статус батареи"),
                ("show_display", "Показать информацию о дисплее", "Включить детали экрана"),
                ("show_system", "Показать информацию о системе", "Включить аптайм и язык системы"),
            ]
            header_text = "Настройки отладочного плагина"
            divider_text = "Команда: .dg"
        elif lang.startswith("pt"):
            settings_options = [
                ("show_device_info", "Mostrar informações do dispositivo", "Incluir detalhes do dispositivo na saída de debug"),
                ("show_app_info", "Mostrar informações do app", "Incluir versão do app na saída"),
                ("show_plugins", "Mostrar plugins ativos", "Listar todos os plugins habilitados na saída"),
                ("show_connection", "Mostrar informações de conexão", "Incluir detalhes da conexão de rede"),
                ("show_storage", "Mostrar informações de armazenamento", "Incluir detalhes de armazenamento e cache"),
                ("show_memory", "Mostrar informações de memória", "Incluir uso de RAM"),
                ("show_account", "Mostrar informações da conta", "Incluir informações básicas da conta"),
                ("show_battery", "Mostrar informações da bateria", "Incluir status da bateria"),
                ("show_display", "Mostrar informações da tela", "Incluir detalhes da tela"),
                ("show_system", "Mostrar informações do sistema", "Incluir tempo ligado e idioma do sistema"),
            ]
            header_text = "Configurações do Plugin de Debug"
            divider_text = "Comando: .dg"
        else:
            settings_options = [
                ("show_device_info", "Show Device Information", "Include device details in the debug output"),
                ("show_app_info", "Show App Information", "Include version details in the output"),
                ("show_plugins", "Show Active Plugins", "List all enabled plugins in the debug output"),
                ("show_connection", "Show Connection Information", "Include network connection details"),
                ("show_storage", "Show Storage Information", "Include storage and cache details"),
                ("show_memory", "Show Memory Information", "Include RAM and memory usage details"),
                ("show_account", "Show Account Information", "Include basic account information"),
                ("show_battery", "Show Battery Information", "Include battery status and details"),
                ("show_display", "Show Display Information", "Include screen details and resolution"),
                ("show_system", "Show System Information", "Include system uptime and language settings")
            ]
            header_text = "Debug Plugin Settings"
            divider_text = "Command: .dg"

        settings = [Header(text=header_text)]
        for key, text, subtext in settings_options:
            settings.append(Switch(key=key, text=text, default=True, subtext=subtext))
        settings.append(Divider(text=divider_text))
        return settings

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()
        if not params.message.startswith(".dg"):
            return HookResult()
        def run_debug():
            try:
                debug_md = self._get_debug_markdown()
                params.message = debug_md
                if not hasattr(params, "entities") or params.entities is None:
                    params.entities = ArrayList()
                elif not isinstance(params.entities, ArrayList):
                    params.entities = ArrayList(params.entities)
                entity = TLRPC.TL_messageEntityBlockquote()
                entity.collapsed = True
                entity.offset = 0
                entity.length = int(len(debug_md.encode(encoding='utf_16_le')) / 2)
                params.entities.add(entity)

                run_on_ui_thread(lambda: self._send_debug_message(params))
            except Exception as e:
                error_msg = f"Error getting debug info: {str(e)}"
                log(f"{error_msg}\n{traceback.format_exc()}")
                params.message = error_msg
                run_on_ui_thread(lambda: self._send_debug_message(params))

        threading.Thread(target=run_debug, daemon=True).start()
        return HookResult(strategy=HookStrategy.CANCEL)

    def _send_debug_message(self, params):
        try:
            params.replyToMsg = params.replyToMsg
            params.replyToTopMsg = params.replyToTopMsg
        except Exception:
            pass
        send_helper = get_send_messages_helper()
        send_helper.sendMessage(params)

    def _get_device_info(self):
        if not self.get_setting("show_device_info", True):
            return ""
        try:
            return (
                "📱 Device\n"
                f"- Manufacturer: {Build.MANUFACTURER}\n"
                f"- Model: {Build.MODEL}\n"
                f"- Android: {Build.VERSION.RELEASE}\n"
                f"- Board: {Build.BOARD}\n"
                f"- Brand: {Build.BRAND}\n"
                f"- Codename: {Build.VERSION.CODENAME}\n"
                f"- ID: {Build.ID}\n"
                f"- Hardware: {Build.HARDWARE}\n"
                f"- Performance: {SharedConfig.performanceClassName(SharedConfig.getDevicePerformanceClass())}"
            )
        except Exception as e:
            return f"Error getting device info: {str(e)}"

    def _get_app_info(self):
        if not self.get_setting("show_app_info", True):
            return ""
        try:
            ctx = ApplicationLoader.applicationContext
            pkg = ctx.getPackageName()
            pm = ctx.getPackageManager()
            pi = pm.getPackageInfo(pkg, 0)
            version_name = pi.versionName
            version_code = pi.versionCode
            if Build.VERSION.SDK_INT >= 28:
                version_code = pi.getLongVersionCode()
            return (
                "📦 App\n"
                f"- Package: {pkg}\n"
                f"- Version: {version_name} ({version_code})"
            )
        except Exception as e:
            return f"Error getting app info: {str(e)}"

    def _get_plugins_info(self):
        if not self.get_setting("show_plugins", True):
            return ""
        try:
            active_plugins = [p.name for p in plugins_manager.PluginsManager._plugins.values() if p.enabled]
            count = len(active_plugins)
            if not active_plugins:
                return "🧩 Active Plugins\nNo active plugins"
            max_plugins = 20
            shown = active_plugins[:max_plugins]
            more = count - max_plugins
            plugins_md = "\n".join(f"- {name}" for name in shown)
            if more > 0:
                plugins_md += f"\n- ...and {more} more plugins"
            return f"🧩 Active Plugins ({count})\n{plugins_md}"
        except Exception as e:
            return f"Error getting plugins: {str(e)}"

    def _get_connection_info(self):
        if not self.get_setting("show_connection", True):
            return ""
        try:
            ctx = ApplicationLoader.applicationContext
            conn_mgr = ctx.getSystemService(Context.CONNECTIVITY_SERVICE)
            is_connected = False
            conn_type = "None"
            if conn_mgr:
                network = conn_mgr.getActiveNetwork()
                if network:
                    caps = conn_mgr.getNetworkCapabilities(network)
                    is_connected = caps is not None
                    if caps:
                        for t, n in [
                            (NetworkCapabilities.TRANSPORT_WIFI, "WiFi"),
                            (NetworkCapabilities.TRANSPORT_CELLULAR, "Mobile"),
                            (NetworkCapabilities.TRANSPORT_ETHERNET, "Ethernet"),
                            (NetworkCapabilities.TRANSPORT_BLUETOOTH, "Bluetooth"),
                            (NetworkCapabilities.TRANSPORT_VPN, "VPN"),
                        ]:
                            if caps.hasTransport(t):
                                conn_type = n
                                break
            return (
                "🌐 Connection\n"
                f"- Connected: {'Yes' if is_connected else 'No'}\n"
                f"- Type: {conn_type}"
            )
        except Exception as e:
            return f"Error getting connection: {str(e)}"

    def _get_dir_size(self, path):
        total = 0
        try:
            if not path:
                return 0
            if isinstance(path, str):
                import os
                if not os.path.exists(path):
                    return 0
                if os.path.isfile(path):
                    return os.path.getsize(path)
                for entry in os.scandir(path):
                    if entry.is_file():
                        total += entry.stat().st_size
                    elif entry.is_dir():
                        total += self._get_dir_size(entry.path)
            else:
                if path.exists():
                    if path.isFile():
                        return path.length()
                    for file in path.listFiles():
                        total += self._get_dir_size(file)
        except Exception:
            pass
        return total

    def _format_size(self, size_bytes):
        try:
            size_bytes = int(size_bytes)
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 ** 2:
                return f"{size_bytes / 1024:.2f} KB"
            elif size_bytes < 1024 ** 3:
                return f"{size_bytes / (1024 ** 2):.2f} MB"
            else:
                return f"{size_bytes / (1024 ** 3):.2f} GB"
        except Exception:
            return str(size_bytes)

    def _get_storage_info(self):
        if not self.get_setting("show_storage", True):
            return ""
        try:
            cache_path = ApplicationLoader.getFilesDirFixed()
            external_cache = ApplicationLoader.applicationContext.getExternalCacheDir()
            cache_size = self._get_dir_size(cache_path) if cache_path else 0
            external_cache_size = self._get_dir_size(external_cache.getAbsolutePath()) if external_cache else 0
            return (
                "💾 Storage\n"
                f"- Telegram Cache: {self._format_size(cache_size)}\n"
                f"- External Cache: {self._format_size(external_cache_size)}\n"
                f"- Cache Folder: {cache_path}"
            )
        except Exception as e:
            return f"Error getting storage: {str(e)}"

    def _get_memory_info(self):
        if not self.get_setting("show_memory", True):
            return ""
        try:
            ctx = ApplicationLoader.applicationContext
            am = ctx.getSystemService(Context.ACTIVITY_SERVICE)
            mem_info = ActivityManager.MemoryInfo()
            am.getMemoryInfo(mem_info)
            total = mem_info.totalMem / (1024 * 1024)
            avail = mem_info.availMem / (1024 * 1024)
            used = total - avail
            percent = (used / total) * 100.0 if total > 0 else 0
            return (
                "🧠 Memory\n"
                f"- Total: {total:.2f} MB\n"
                f"- Available: {avail:.2f} MB\n"
                f"- Used: {used:.2f} MB ({percent:.1f}%)\n"
                f"- Low memory: {'Yes' if mem_info.lowMemory else 'No'}"
            )
        except Exception as e:
            return f"Error getting memory: {str(e)}"

    def _get_account_info(self):
        if not self.get_setting("show_account", True):
            return ""
        try:
            acc = UserConfig.selectedAccount
            user_config = get_user_config()
            user_id = user_config.getClientUserId()
            premium = user_config.isPremium()
            user = user_config.getCurrentUser() if hasattr(user_config, "getCurrentUser") else None
            username = user.username if user and hasattr(user, "username") and user.username else ""
            first_name = user.first_name if user and hasattr(user, "first_name") and user.first_name else ""
            last_name = user.last_name if user and hasattr(user, "last_name") and user.last_name else ""
            full_name = first_name if not last_name else f"{first_name} {last_name}"
            status = getattr(user, "status", None)
            last_online = ""
            if status and hasattr(status, "was_online"):
                ts = getattr(status, "was_online")
                if ts and isinstance(ts, int) and ts > **********:
                    import datetime
                    dt = datetime.datetime.fromtimestamp(ts)
                    last_online = dt.strftime("%Y-%m-%d %H:%M:%S")
            elif status and hasattr(status, "expires"):
                ts = getattr(status, "expires")
                if ts and isinstance(ts, int) and ts > **********:
                    import datetime
                    dt = datetime.datetime.fromtimestamp(ts)
                    last_online = dt.strftime("%Y-%m-%d %H:%M:%S")

            info_lines = [
                "👤 Account",
                f"- ID: {user_id}",
                f"- Username: {username}",
                f"- Name: {full_name}",
                f"- Premium: {'Yes' if premium else 'No'}",
                f"- Last online: {last_online}" if last_online else "",
                f"- Index: {acc}"
            ]
            info_lines = [line for line in info_lines if line]
            return "\n".join(info_lines)
        except Exception as e:
            return f"Error getting account: {str(e)}"

    def _get_battery_info(self):
        if not self.get_setting("show_battery", True):
            return ""
        try:
            ctx = ApplicationLoader.applicationContext
            intent = ctx.registerReceiver(None, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            if intent:
                level = intent.getIntExtra("level", 0)
                scale = intent.getIntExtra("scale", 100)
                percent = (float(level) * 100.0) / float(scale) if scale > 0 else 0
                status = intent.getIntExtra("status", 0)
                is_charging = status in [2, 5]
                health = intent.getIntExtra("health", 1)
                temp = intent.getIntExtra("temperature", 0) / 10.0
                voltage = intent.getIntExtra("voltage", 0) / 1000.0
                health_statuses = {
                    1: "Unknown", 2: "Good", 3: "Overheat",
                    4: "Dead", 5: "Overvoltage", 6: "Failure", 7: "Cold"
                }
                health_status = health_statuses.get(health, "Unknown")
                return (
                    "🔋 Battery\n"
                    f"- Level: {percent:.1f}%\n"
                    f"- Charging: {'Yes' if is_charging else 'No'}\n"
                    f"- Health: {health_status}\n"
                    f"- Temperature: {temp}°C\n"
                    f"- Voltage: {voltage}V"
                )
            return ""
        except Exception as e:
            return f"Error getting battery: {str(e)}"

    def _get_display_info(self):
        if not self.get_setting("show_display", True):
            return ""
        try:
            ctx = ApplicationLoader.applicationContext
            wm = ctx.getSystemService(Context.WINDOW_SERVICE)
            display = wm.getDefaultDisplay()
            metrics = ctx.getResources().getDisplayMetrics()
            width = metrics.widthPixels
            height = metrics.heightPixels
            density = metrics.density
            dpi = metrics.densityDpi
            diagonal_pixels = (width ** 2 + height ** 2) ** 0.5
            diagonal_inches = diagonal_pixels / dpi if dpi > 0 else 0
            refresh_rate = display.getRefreshRate() if hasattr(display, "getRefreshRate") else 0
            try:
                refresh_rate = round(float(refresh_rate), 1)
            except Exception:
                refresh_rate = 0
            return (
                "🖥️ Display\n"
                f"- Resolution: {width}x{height}\n"
                f"- Density: {density:.2f}x ({dpi} DPI)\n"
                f"- Estimated size: {diagonal_inches:.2f}\"\n"
                f"- Refresh rate: {refresh_rate} Hz"
            )
        except Exception as e:
            return f"Error getting display: {str(e)}"

    def _get_system_info(self):
        if not self.get_setting("show_system", True):
            return ""
        try:
            uptime_ms = SystemClock.elapsedRealtime()
            uptime_seconds = int(uptime_ms / 1000)
            days = uptime_seconds // 86400
            hours = (uptime_seconds % 86400) // 3600
            minutes = (uptime_seconds % 3600) // 60
            seconds = uptime_seconds % 60
            uptime_str = f"{days}d {hours}h {minutes}m {seconds}s"
            cpu_cores = os.cpu_count() or 0
            locale = Locale.getDefault()
            return (
                "🛠️ System\n"
                f"- CPU cores: {cpu_cores}\n"
                f"- Uptime: {uptime_str}\n"
                f"- Language: {locale.getLanguage()}-{locale.getCountry()}\n"
                f"- Java VM: {JavaSystem.getProperty('java.vm.version')}"
            )
        except Exception as e:
            return f"Error getting system: {str(e)}"

    def _get_debug_markdown(self):
        sections = [
            self._get_device_info(),
            self._get_app_info(),
            self._get_connection_info(),
            self._get_storage_info(),
            self._get_memory_info(),
            self._get_account_info(),
            self._get_battery_info(),
            self._get_display_info(),
            self._get_system_info(),
            self._get_plugins_info(),
        ]
        md = ""
        for s in sections:
            if s:
                if md:
                    md += "\n\n────────────\n\n"
                md += s
        return md